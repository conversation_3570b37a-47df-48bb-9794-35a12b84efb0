#!/usr/bin/env python3
"""
环境管理器
管理实盘/模拟环境切换功能
"""

import tkinter as tk
from tkinter import messagebox
import threading
import time
from datetime import datetime
from logger import get_logger

class EnvironmentManager:
    """环境管理器"""
    
    # 环境模式常量
    MODE_SIMULATION = "simulation"  # 模拟模式
    MODE_LIVE = "live"             # 实盘模式
    MODE_TESTNET = "testnet"       # 测试网模式
    
    def __init__(self):
        self.logger = get_logger("environment_manager")
        
        # 当前环境状态
        self.current_mode = self.MODE_SIMULATION
        self.is_live_mode = False
        self.use_testnet = False
        
        # 环境切换回调函数列表
        self.mode_change_callbacks = []
        
        # 风险确认状态
        self.risk_acknowledged = False
        
        # 环境配置
        self.environment_configs = {
            self.MODE_SIMULATION: {
                'name': '模拟模式',
                'description': '使用模拟数据，无真实资金风险',
                'color': 'green',
                'sandbox': True,
                'use_real_api': False
            },
            self.MODE_TESTNET: {
                'name': '测试网模式',
                'description': '使用交易所测试环境，真实API但无真实资金',
                'color': 'orange',
                'sandbox': True,
                'use_real_api': True
            },
            self.MODE_LIVE: {
                'name': '实盘模式',
                'description': '真实交易环境，涉及真实资金风险',
                'color': 'red',
                'sandbox': False,
                'use_real_api': True
            }
        }
        
        self.logger.info("环境管理器初始化完成，当前模式: 模拟模式")
    
    def get_current_mode(self):
        """获取当前环境模式"""
        return self.current_mode
    
    def get_mode_info(self, mode=None):
        """获取模式信息"""
        if mode is None:
            mode = self.current_mode
        return self.environment_configs.get(mode, {})
    
    def is_simulation_mode(self):
        """是否为模拟模式"""
        return self.current_mode == self.MODE_SIMULATION
    
    def is_live_mode_enabled(self):
        """是否启用实盘模式"""
        return self.current_mode in [self.MODE_LIVE, self.MODE_TESTNET]
    
    def is_testnet_mode(self):
        """是否为测试网模式"""
        return self.current_mode == self.MODE_TESTNET
    
    def should_use_sandbox(self):
        """是否应该使用沙盒环境"""
        config = self.get_mode_info()
        return config.get('sandbox', True)
    
    def should_use_real_api(self):
        """是否应该使用真实API"""
        config = self.get_mode_info()
        return config.get('use_real_api', False)
    
    def add_mode_change_callback(self, callback):
        """添加模式切换回调函数"""
        if callback not in self.mode_change_callbacks:
            self.mode_change_callbacks.append(callback)
    
    def remove_mode_change_callback(self, callback):
        """移除模式切换回调函数"""
        if callback in self.mode_change_callbacks:
            self.mode_change_callbacks.remove(callback)
    
    def _notify_mode_change(self, old_mode, new_mode):
        """通知模式切换"""
        for callback in self.mode_change_callbacks:
            try:
                callback(old_mode, new_mode)
            except Exception as e:
                self.logger.error(f"模式切换回调执行失败: {e}")
    
    def switch_to_simulation(self):
        """切换到模拟模式"""
        old_mode = self.current_mode
        self.current_mode = self.MODE_SIMULATION
        self.is_live_mode = False
        self.use_testnet = False
        self.risk_acknowledged = False
        
        self.logger.info("切换到模拟模式")
        self._notify_mode_change(old_mode, self.current_mode)
        return True
    
    def switch_to_testnet(self):
        """切换到测试网模式"""
        if not self._show_testnet_warning():
            return False
        
        old_mode = self.current_mode
        self.current_mode = self.MODE_TESTNET
        self.is_live_mode = True
        self.use_testnet = True
        
        self.logger.warning("切换到测试网模式")
        self._notify_mode_change(old_mode, self.current_mode)
        return True
    
    def switch_to_live(self):
        """切换到实盘模式"""
        if not self._show_live_mode_warning():
            return False
        
        old_mode = self.current_mode
        self.current_mode = self.MODE_LIVE
        self.is_live_mode = True
        self.use_testnet = False
        self.risk_acknowledged = True
        
        self.logger.critical("切换到实盘模式 - 涉及真实资金风险！")
        self._notify_mode_change(old_mode, self.current_mode)
        return True
    
    def _show_testnet_warning(self):
        """显示测试网警告"""
        warning_text = """
⚠️ 测试网模式警告

您即将切换到测试网模式：

• 使用交易所的测试环境
• 使用真实API但无真实资金
• 适合测试策略和API连接
• 测试环境数据可能与实盘不同

确定要切换到测试网模式吗？
        """
        
        return messagebox.askyesno(
            "测试网模式确认",
            warning_text,
            icon='warning'
        )
    
    def _show_live_mode_warning(self):
        """显示实盘模式风险警告"""
        warning_text = """
🚨 实盘模式风险警告 🚨

您即将切换到实盘模式，这意味着：

⚠️ 所有交易将使用真实资金
⚠️ 策略错误可能导致资金损失
⚠️ 网络问题可能影响交易执行
⚠️ 市场波动可能超出预期

风险提示：
• 量化交易存在亏损风险
• 请确保充分测试策略
• 建议从小资金开始
• 设置合理的风险控制

我已充分理解上述风险，确定要启用实盘模式吗？
        """
        
        # 第一次确认
        if not messagebox.askyesno(
            "实盘模式风险警告",
            warning_text,
            icon='warning'
        ):
            return False
        
        # 第二次确认
        confirm_text = """
最终确认

请再次确认您已理解：

1. 实盘模式将使用真实资金进行交易
2. 任何策略错误都可能导致资金损失
3. 您将承担所有交易风险

确定要启用实盘模式吗？
        """
        
        return messagebox.askyesno(
            "最终确认",
            confirm_text,
            icon='question'
        )
    
    def get_api_config(self, exchange_name, api_key, secret, passphrase=""):
        """根据当前模式获取API配置"""
        config = {
            'apiKey': api_key,
            'secret': secret,
            'sandbox': self.should_use_sandbox(),
            'enableRateLimit': True,
            'timeout': 30000,
        }
        
        # OKX需要passphrase
        if exchange_name == 'okx' and passphrase:
            config['password'] = passphrase
        
        # 根据模式调整配置
        if self.is_simulation_mode():
            # 模拟模式：使用沙盒，可能使用模拟数据
            config['sandbox'] = True
            config['simulation'] = True
        elif self.is_testnet_mode():
            # 测试网模式：使用沙盒，真实API
            config['sandbox'] = True
            config['simulation'] = False
        else:
            # 实盘模式：不使用沙盒，真实API
            config['sandbox'] = False
            config['simulation'] = False
        
        return config
    
    def get_status_info(self):
        """获取状态信息"""
        mode_info = self.get_mode_info()
        
        return {
            'mode': self.current_mode,
            'mode_name': mode_info.get('name', '未知'),
            'description': mode_info.get('description', ''),
            'color': mode_info.get('color', 'black'),
            'is_live': self.is_live_mode_enabled(),
            'use_sandbox': self.should_use_sandbox(),
            'use_real_api': self.should_use_real_api(),
            'risk_acknowledged': self.risk_acknowledged
        }
    
    def log_mode_change(self, old_mode, new_mode, user_action=True):
        """记录模式切换日志"""
        log_msg = f"环境模式切换: {old_mode} -> {new_mode}"
        
        if user_action:
            log_msg += " (用户操作)"
        
        if new_mode == self.MODE_LIVE:
            self.logger.critical(log_msg + " - 已启用实盘模式，涉及真实资金风险！")
        elif new_mode == self.MODE_TESTNET:
            self.logger.warning(log_msg + " - 已启用测试网模式")
        else:
            self.logger.info(log_msg + " - 已切换到模拟模式")
    
    def validate_live_mode_requirements(self):
        """验证实盘模式要求"""
        if not self.is_live_mode_enabled():
            return True
        
        # 检查是否有有效的API配置
        # 这里可以添加更多验证逻辑
        
        return True
    
    def get_mode_display_text(self):
        """获取模式显示文本"""
        mode_info = self.get_mode_info()
        mode_name = mode_info.get('name', '未知模式')
        
        if self.is_live_mode_enabled():
            return f"🔴 {mode_name}"
        else:
            return f"🟢 {mode_name}"

# 全局环境管理器实例
environment_manager = EnvironmentManager()
