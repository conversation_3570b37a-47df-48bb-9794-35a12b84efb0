#!/usr/bin/env python3
"""
环境管理器模块

本模块负责管理量化交易系统的运行环境，支持模拟模式、测试网模式和实盘模式的切换。
这是系统安全性的重要保障，确保用户在不同环境下的资金安全。

环境类型说明：
1. 模拟模式 (Simulation)：
   - 完全虚拟的交易环境，不涉及真实资金
   - 用于策略开发、测试和学习
   - 数据来源于真实市场，但交易是模拟的
   - 适合新手学习和策略验证

2. 测试网模式 (Testnet)：
   - 使用交易所提供的测试网络
   - 使用测试代币，不是真实资金
   - 真实的API调用，但资金是虚拟的
   - 适合策略的最终验证

3. 实盘模式 (Live)：
   - 真实的交易环境，使用真实资金
   - 所有交易都会产生实际的资金变动
   - 需要严格的风险控制和确认机制
   - 适合经过充分测试的成熟策略

安全机制：
- 实盘模式需要双重确认
- 环境切换有明确的风险提示
- 自动记录环境切换日志
- 防止误操作的保护机制

使用建议：
1. 新策略先在模拟模式下测试
2. 验证无误后在测试网模式下运行
3. 确认策略稳定后再切换到实盘模式
4. 实盘模式建议从小资金开始

作者：量化交易系统开发团队
版本：v2.0 (增强版，包含用户友好提示)
"""

import tkinter as tk
from tkinter import messagebox
import threading
import time
from datetime import datetime
from logger import get_logger
from user_friendly_messages import user_messages

class EnvironmentManager:
    """
    环境管理器类

    这是量化交易系统的安全核心，负责管理不同交易环境的切换。
    通过严格的环境隔离和安全确认机制，保护用户的资金安全。

    核心功能：
    1. 环境状态管理：跟踪当前运行环境
    2. 安全切换：提供安全的环境切换机制
    3. 风险控制：实盘模式的多重确认保护
    4. 配置管理：根据环境自动调整API配置
    5. 日志记录：详细记录环境切换操作

    设计原则：
    - 安全第一：实盘模式需要明确确认
    - 用户友好：提供清晰的提示和说明
    - 防误操作：多重确认机制
    - 可追溯：完整的操作日志

    使用示例：
    ```python
    # 获取环境管理器实例
    env_manager = environment_manager

    # 检查当前环境
    if env_manager.is_simulation_mode():
        print("当前为模拟模式")

    # 切换到测试网模式
    env_manager.switch_to_testnet()

    # 切换到实盘模式（需要确认）
    env_manager.switch_to_live()
    ```
    """

    # 环境模式常量定义
    MODE_SIMULATION = "simulation"  # 模拟模式：完全虚拟环境，无真实资金风险
    MODE_LIVE = "live"             # 实盘模式：真实交易环境，涉及真实资金
    MODE_TESTNET = "testnet"       # 测试网模式：使用测试网络，测试代币
    
    def __init__(self):
        self.logger = get_logger("environment_manager")
        
        # 当前环境状态
        self.current_mode = self.MODE_SIMULATION
        self.is_live_mode = False
        self.use_testnet = False
        
        # 环境切换回调函数列表
        self.mode_change_callbacks = []
        
        # 风险确认状态
        self.risk_acknowledged = False
        
        # 环境配置
        self.environment_configs = {
            self.MODE_SIMULATION: {
                'name': '模拟模式',
                'description': '使用模拟数据，无真实资金风险',
                'color': 'green',
                'sandbox': True,
                'use_real_api': False
            },
            self.MODE_TESTNET: {
                'name': '测试网模式',
                'description': '使用交易所测试环境，真实API但无真实资金',
                'color': 'orange',
                'sandbox': True,
                'use_real_api': True
            },
            self.MODE_LIVE: {
                'name': '实盘模式',
                'description': '真实交易环境，涉及真实资金风险',
                'color': 'red',
                'sandbox': False,
                'use_real_api': True
            }
        }
        
        self.logger.info("环境管理器初始化完成，当前模式: 模拟模式")
    
    def get_current_mode(self):
        """获取当前环境模式"""
        return self.current_mode
    
    def get_mode_info(self, mode=None):
        """获取模式信息"""
        if mode is None:
            mode = self.current_mode
        return self.environment_configs.get(mode, {})
    
    def is_simulation_mode(self):
        """是否为模拟模式"""
        return self.current_mode == self.MODE_SIMULATION
    
    def is_live_mode_enabled(self):
        """是否启用实盘模式"""
        return self.current_mode in [self.MODE_LIVE, self.MODE_TESTNET]
    
    def is_testnet_mode(self):
        """是否为测试网模式"""
        return self.current_mode == self.MODE_TESTNET
    
    def should_use_sandbox(self):
        """是否应该使用沙盒环境"""
        config = self.get_mode_info()
        return config.get('sandbox', True)
    
    def should_use_real_api(self):
        """是否应该使用真实API"""
        config = self.get_mode_info()
        return config.get('use_real_api', False)
    
    def add_mode_change_callback(self, callback):
        """添加模式切换回调函数"""
        if callback not in self.mode_change_callbacks:
            self.mode_change_callbacks.append(callback)
    
    def remove_mode_change_callback(self, callback):
        """移除模式切换回调函数"""
        if callback in self.mode_change_callbacks:
            self.mode_change_callbacks.remove(callback)
    
    def _notify_mode_change(self, old_mode, new_mode):
        """通知模式切换"""
        for callback in self.mode_change_callbacks:
            try:
                callback(old_mode, new_mode)
            except Exception as e:
                self.logger.error(f"模式切换回调执行失败: {e}")
    
    def switch_to_simulation(self):
        """切换到模拟模式"""
        old_mode = self.current_mode
        self.current_mode = self.MODE_SIMULATION
        self.is_live_mode = False
        self.use_testnet = False
        self.risk_acknowledged = False
        
        self.logger.info("切换到模拟模式")
        self._notify_mode_change(old_mode, self.current_mode)
        return True
    
    def switch_to_testnet(self):
        """切换到测试网模式"""
        if not self._show_testnet_warning():
            return False
        
        old_mode = self.current_mode
        self.current_mode = self.MODE_TESTNET
        self.is_live_mode = True
        self.use_testnet = True
        
        self.logger.warning("切换到测试网模式")
        self._notify_mode_change(old_mode, self.current_mode)
        return True
    
    def switch_to_live(self):
        """切换到实盘模式"""
        if not self._show_live_mode_warning():
            return False
        
        old_mode = self.current_mode
        self.current_mode = self.MODE_LIVE
        self.is_live_mode = True
        self.use_testnet = False
        self.risk_acknowledged = True
        
        self.logger.critical("切换到实盘模式 - 涉及真实资金风险！")
        self._notify_mode_change(old_mode, self.current_mode)
        return True
    
    def _show_testnet_warning(self):
        """显示测试网警告"""
        warning_text = """
⚠️ 测试网模式警告

您即将切换到测试网模式：

• 使用交易所的测试环境
• 使用真实API但无真实资金
• 适合测试策略和API连接
• 测试环境数据可能与实盘不同

确定要切换到测试网模式吗？
        """
        
        return messagebox.askyesno(
            "测试网模式确认",
            warning_text,
            icon='warning'
        )
    
    def _show_live_mode_warning(self):
        """显示实盘模式风险警告"""
        # 使用用户友好消息中心显示警告
        first_confirm = user_messages.show_warning("live_mode_warning")

        if not first_confirm:
            self.logger.info("用户取消切换到实盘模式")
            return False

        # 第二次确认
        risks = [
            "实盘模式将使用真实资金进行交易",
            "任何策略错误都可能导致资金损失",
            "您将承担所有交易风险"
        ]

        final_confirm = user_messages.confirm_risky_operation("启用实盘模式", risks)

        if final_confirm:
            self.risk_acknowledged = True
            self.logger.info("用户确认切换到实盘模式")
        else:
            self.logger.info("用户在最终确认时取消切换到实盘模式")

        return final_confirm
    
    def get_api_config(self, exchange_name, api_key, secret, passphrase=""):
        """根据当前模式获取API配置"""
        config = {
            'apiKey': api_key,
            'secret': secret,
            'sandbox': self.should_use_sandbox(),
            'enableRateLimit': True,
            'timeout': 30000,
        }
        
        # OKX需要passphrase
        if exchange_name == 'okx' and passphrase:
            config['password'] = passphrase
        
        # 根据模式调整配置
        if self.is_simulation_mode():
            # 模拟模式：使用沙盒，可能使用模拟数据
            config['sandbox'] = True
            config['simulation'] = True
        elif self.is_testnet_mode():
            # 测试网模式：使用沙盒，真实API
            config['sandbox'] = True
            config['simulation'] = False
        else:
            # 实盘模式：不使用沙盒，真实API
            config['sandbox'] = False
            config['simulation'] = False
        
        return config
    
    def get_status_info(self):
        """获取状态信息"""
        mode_info = self.get_mode_info()
        
        return {
            'mode': self.current_mode,
            'mode_name': mode_info.get('name', '未知'),
            'description': mode_info.get('description', ''),
            'color': mode_info.get('color', 'black'),
            'is_live': self.is_live_mode_enabled(),
            'use_sandbox': self.should_use_sandbox(),
            'use_real_api': self.should_use_real_api(),
            'risk_acknowledged': self.risk_acknowledged
        }
    
    def log_mode_change(self, old_mode, new_mode, user_action=True):
        """记录模式切换日志"""
        log_msg = f"环境模式切换: {old_mode} -> {new_mode}"
        
        if user_action:
            log_msg += " (用户操作)"
        
        if new_mode == self.MODE_LIVE:
            self.logger.critical(log_msg + " - 已启用实盘模式，涉及真实资金风险！")
        elif new_mode == self.MODE_TESTNET:
            self.logger.warning(log_msg + " - 已启用测试网模式")
        else:
            self.logger.info(log_msg + " - 已切换到模拟模式")
    
    def validate_live_mode_requirements(self):
        """验证实盘模式要求"""
        if not self.is_live_mode_enabled():
            return True
        
        # 检查是否有有效的API配置
        # 这里可以添加更多验证逻辑
        
        return True
    
    def get_mode_display_text(self):
        """获取模式显示文本"""
        mode_info = self.get_mode_info()
        mode_name = mode_info.get('name', '未知模式')
        
        if self.is_live_mode_enabled():
            return f"🔴 {mode_name}"
        else:
            return f"🟢 {mode_name}"

# 全局环境管理器实例
environment_manager = EnvironmentManager()
