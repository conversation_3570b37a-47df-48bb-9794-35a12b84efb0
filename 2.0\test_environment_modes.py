#!/usr/bin/env python3
"""
环境切换功能全面测试
测试模拟/测试网/实盘三种环境模式的切换和功能验证
"""

import sys
import os
import time
import threading
from datetime import datetime

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from test_framework import test_framework, TestResult
from environment_manager import environment_manager
from strategy_environment_adapter import StrategyEnvironmentAdapter

class EnvironmentSwitchingTester:
    """环境切换测试器"""
    
    def __init__(self):
        self.framework = test_framework
        self.mock_exchange = self.framework.create_mock_exchange()
        
    def test_environment_manager(self):
        """测试环境管理器"""
        
        def test_environment_initialization():
            """测试环境管理器初始化"""
            self.framework.assert_not_none(environment_manager, "环境管理器不应为空")
            self.framework.assert_equal(environment_manager.get_current_mode(), 
                                      environment_manager.MODE_SIMULATION, 
                                      "默认应为模拟模式")
            self.framework.assert_true(environment_manager.is_simulation_mode(), 
                                     "初始状态应为模拟模式")
            self.framework.assert_false(environment_manager.is_live_mode_enabled(), 
                                       "初始状态不应为实盘模式")
        
        def test_mode_constants():
            """测试模式常量"""
            self.framework.assert_equal(environment_manager.MODE_SIMULATION, "simulation", 
                                      "模拟模式常量错误")
            self.framework.assert_equal(environment_manager.MODE_LIVE, "live", 
                                      "实盘模式常量错误")
            self.framework.assert_equal(environment_manager.MODE_TESTNET, "testnet", 
                                      "测试网模式常量错误")
        
        def test_environment_configuration():
            """测试环境配置"""
            configs = environment_manager.environment_configs
            
            # 验证所有模式都有配置
            for mode in [environment_manager.MODE_SIMULATION, 
                        environment_manager.MODE_LIVE, 
                        environment_manager.MODE_TESTNET]:
                self.framework.assert_true(mode in configs, f"{mode} 模式配置缺失")
                
                config = configs[mode]
                self.framework.assert_true('name' in config, f"{mode} 应有名称配置")
                self.framework.assert_true('description' in config, f"{mode} 应有描述配置")
                self.framework.assert_true('color' in config, f"{mode} 应有颜色配置")
                self.framework.assert_true('sandbox' in config, f"{mode} 应有沙盒配置")
                self.framework.assert_true('use_real_api' in config, f"{mode} 应有API配置")
        
        # 运行环境管理器测试
        self.framework.run_test_case(
            test_environment_initialization,
            "环境管理器初始化测试",
            "验证环境管理器的正确初始化",
            "环境切换测试",
            "HIGH"
        )
        
        self.framework.run_test_case(
            test_mode_constants,
            "模式常量测试",
            "验证环境模式常量的正确性",
            "环境切换测试",
            "MEDIUM"
        )
        
        self.framework.run_test_case(
            test_environment_configuration,
            "环境配置测试",
            "验证各环境模式的配置完整性",
            "环境切换测试",
            "HIGH"
        )
    
    def test_mode_switching(self):
        """测试模式切换功能"""
        
        def test_simulation_mode():
            """测试模拟模式"""
            # 确保在模拟模式
            environment_manager.switch_to_simulation()
            
            self.framework.assert_equal(environment_manager.get_current_mode(), 
                                      environment_manager.MODE_SIMULATION, 
                                      "应为模拟模式")
            self.framework.assert_true(environment_manager.is_simulation_mode(), 
                                     "应为模拟模式")
            self.framework.assert_true(environment_manager.should_use_sandbox(), 
                                     "模拟模式应使用沙盒")
            self.framework.assert_false(environment_manager.should_use_real_api(), 
                                       "模拟模式不应使用真实API")
        
        def test_testnet_mode():
            """测试测试网模式"""
            # 注意：这里不会显示确认对话框，因为是自动化测试
            original_show_warning = environment_manager._show_testnet_warning
            environment_manager._show_testnet_warning = lambda: True
            
            try:
                success = environment_manager.switch_to_testnet()
                
                if success:
                    self.framework.assert_equal(environment_manager.get_current_mode(), 
                                              environment_manager.MODE_TESTNET, 
                                              "应为测试网模式")
                    self.framework.assert_true(environment_manager.is_testnet_mode(), 
                                             "应为测试网模式")
                    self.framework.assert_true(environment_manager.should_use_sandbox(), 
                                             "测试网模式应使用沙盒")
                    self.framework.assert_true(environment_manager.should_use_real_api(), 
                                             "测试网模式应使用真实API")
                else:
                    self.framework.logger.info("测试网模式切换被用户取消")
            finally:
                environment_manager._show_testnet_warning = original_show_warning
        
        def test_live_mode():
            """测试实盘模式"""
            # 注意：这里不会显示确认对话框，因为是自动化测试
            original_show_warning = environment_manager._show_live_mode_warning
            environment_manager._show_live_mode_warning = lambda: True
            
            try:
                success = environment_manager.switch_to_live()
                
                if success:
                    self.framework.assert_equal(environment_manager.get_current_mode(), 
                                              environment_manager.MODE_LIVE, 
                                              "应为实盘模式")
                    self.framework.assert_true(environment_manager.is_live_mode_enabled(), 
                                             "应为实盘模式")
                    self.framework.assert_false(environment_manager.should_use_sandbox(), 
                                               "实盘模式不应使用沙盒")
                    self.framework.assert_true(environment_manager.should_use_real_api(), 
                                             "实盘模式应使用真实API")
                else:
                    self.framework.logger.info("实盘模式切换被用户取消")
            finally:
                environment_manager._show_live_mode_warning = original_show_warning
        
        def test_mode_switching_sequence():
            """测试模式切换序列"""
            # 测试完整的切换序列
            original_show_testnet_warning = environment_manager._show_testnet_warning
            original_show_live_warning = environment_manager._show_live_mode_warning
            
            environment_manager._show_testnet_warning = lambda: True
            environment_manager._show_live_mode_warning = lambda: True
            
            try:
                # 模拟 -> 测试网 -> 实盘 -> 模拟
                environment_manager.switch_to_simulation()
                self.framework.assert_equal(environment_manager.get_current_mode(), 
                                          environment_manager.MODE_SIMULATION)
                
                environment_manager.switch_to_testnet()
                self.framework.assert_equal(environment_manager.get_current_mode(), 
                                          environment_manager.MODE_TESTNET)
                
                environment_manager.switch_to_live()
                self.framework.assert_equal(environment_manager.get_current_mode(), 
                                          environment_manager.MODE_LIVE)
                
                environment_manager.switch_to_simulation()
                self.framework.assert_equal(environment_manager.get_current_mode(), 
                                          environment_manager.MODE_SIMULATION)
            finally:
                environment_manager._show_testnet_warning = original_show_testnet_warning
                environment_manager._show_live_mode_warning = original_show_live_warning
        
        # 运行模式切换测试
        self.framework.run_test_case(
            test_simulation_mode,
            "模拟模式测试",
            "验证模拟模式的功能和配置",
            "环境切换测试",
            "HIGH"
        )
        
        self.framework.run_test_case(
            test_testnet_mode,
            "测试网模式测试",
            "验证测试网模式的功能和配置",
            "环境切换测试",
            "HIGH"
        )
        
        self.framework.run_test_case(
            test_live_mode,
            "实盘模式测试",
            "验证实盘模式的功能和配置",
            "环境切换测试",
            "HIGH"
        )
        
        self.framework.run_test_case(
            test_mode_switching_sequence,
            "模式切换序列测试",
            "验证完整的模式切换序列",
            "环境切换测试",
            "MEDIUM"
        )
    
    def test_api_configuration(self):
        """测试API配置生成"""
        
        def test_simulation_api_config():
            """测试模拟模式API配置"""
            environment_manager.switch_to_simulation()
            
            config = environment_manager.get_api_config('okx', 'test_key', 'test_secret', 'test_pass')
            
            self.framework.assert_true(config['sandbox'], "模拟模式应使用沙盒")
            self.framework.assert_true(config['simulation'], "模拟模式应标记为模拟")
            self.framework.assert_equal(config['apiKey'], 'test_key', "API密钥应正确设置")
            self.framework.assert_equal(config['secret'], 'test_secret', "API密钥应正确设置")
        
        def test_live_api_config():
            """测试实盘模式API配置"""
            # 临时切换到实盘模式进行测试
            original_show_warning = environment_manager._show_live_mode_warning
            environment_manager._show_live_mode_warning = lambda: True
            
            try:
                environment_manager.switch_to_live()
                
                config = environment_manager.get_api_config('okx', 'test_key', 'test_secret', 'test_pass')
                
                self.framework.assert_false(config['sandbox'], "实盘模式不应使用沙盒")
                self.framework.assert_false(config['simulation'], "实盘模式不应标记为模拟")
                self.framework.assert_equal(config['apiKey'], 'test_key', "API密钥应正确设置")
                self.framework.assert_equal(config['secret'], 'test_secret', "API密钥应正确设置")
            finally:
                environment_manager._show_live_mode_warning = original_show_warning
                environment_manager.switch_to_simulation()  # 恢复模拟模式
        
        # 运行API配置测试
        self.framework.run_test_case(
            test_simulation_api_config,
            "模拟模式API配置测试",
            "验证模拟模式下的API配置生成",
            "环境切换测试",
            "HIGH"
        )
        
        self.framework.run_test_case(
            test_live_api_config,
            "实盘模式API配置测试",
            "验证实盘模式下的API配置生成",
            "环境切换测试",
            "HIGH"
        )
    
    def run_all_environment_tests(self):
        """运行所有环境切换测试"""
        print("开始环境切换测试...")
        
        # 测试所有环境切换功能
        self.test_environment_manager()
        self.test_mode_switching()
        self.test_api_configuration()
        
        print("环境切换测试完成")

def main():
    """主测试函数"""
    print("=" * 80)
    print("量化交易系统 - 环境切换功能全面测试")
    print("=" * 80)
    
    tester = EnvironmentSwitchingTester()
    tester.run_all_environment_tests()
    
    # 生成测试报告
    summary = test_framework.reporter.generate_summary()
    print(f"\n环境切换测试摘要:")
    print(f"总测试用例: {summary['total']}")
    print(f"通过: {summary['passed']}")
    print(f"失败: {summary['failed']}")
    print(f"错误: {summary['errors']}")
    print(f"通过率: {summary['pass_rate']:.1f}%")

if __name__ == "__main__":
    main()
