# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['C:/Users/<USER>/Desktop/yl/2.0/main.py'],
    pathex=[],
    binaries=[],
    datas=[('C:/Users/<USER>/Desktop/yl/2.0', '2.0')],
    hiddenimports=[],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='MyApp',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=['C:\\Users\\<USER>\\Desktop\\yl\\2.0\\favicon.ico'],
)
