#!/usr/bin/env python3
"""
AO指标策略测试
验证Awesome Oscillator策略的实现和功能
"""

import sys
import os
import pandas as pd
import numpy as np
from unittest.mock import Mock
from datetime import datetime, timedelta

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_ao_strategy_basic():
    """测试AO策略基本功能"""
    print("🧪 测试AO策略基本功能...")
    
    try:
        from strategies import AwesomeOscillatorStrategy
        
        # 创建模拟交易所
        mock_exchange = Mock()
        mock_exchange.fetch_ticker.return_value = {'last': 0.21375}
        mock_exchange.create_limit_buy_order.return_value = {'id': 'buy_123', 'status': 'open'}
        mock_exchange.create_limit_sell_order.return_value = {'id': 'sell_123', 'status': 'open'}
        
        # 创建AO策略实例
        strategy = AwesomeOscillatorStrategy(
            exchange=mock_exchange,
            symbol='CFX/USDT',
            short_period=5,
            long_period=34,
            amount=100,
            stop_loss_pct=0.03,
            take_profit_pct=0.06
        )
        
        print("✅ AO策略创建成功")
        print(f"   交易对: {strategy.symbol}")
        print(f"   短期周期: {strategy.short_period}")
        print(f"   长期周期: {strategy.long_period}")
        print(f"   交易数量: {strategy.amount}")
        print(f"   止损: {strategy.stop_loss_pct*100}%")
        print(f"   止盈: {strategy.take_profit_pct*100}%")
        
        return True
        
    except Exception as e:
        print(f"❌ AO策略基本功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ao_calculation():
    """测试AO指标计算"""
    print("\n📊 测试AO指标计算...")
    
    try:
        from strategies import AwesomeOscillatorStrategy
        
        # 创建模拟交易所和策略
        mock_exchange = Mock()
        strategy = AwesomeOscillatorStrategy(
            exchange=mock_exchange,
            symbol='CFX/USDT',
            short_period=5,
            long_period=34,
            amount=100
        )
        
        # 创建测试数据
        dates = pd.date_range(start='2024-01-01', periods=50, freq='1H')
        np.random.seed(42)
        
        # 生成模拟价格数据
        base_price = 0.21375
        prices = []
        for i in range(50):
            change = np.random.normal(0, 0.02)
            base_price *= (1 + change)
            prices.append(base_price)
        
        df = pd.DataFrame({
            'timestamp': dates,
            'open': prices,
            'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
            'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
            'close': prices,
            'volume': np.random.randint(1000, 10000, 50)
        })
        
        # 计算AO指标
        df_with_ao = strategy.calculate_awesome_oscillator(df)
        
        # 验证计算结果
        assert 'median_price' in df_with_ao.columns, "缺少中价列"
        assert 'sma_short' in df_with_ao.columns, "缺少短期均线列"
        assert 'sma_long' in df_with_ao.columns, "缺少长期均线列"
        assert 'ao' in df_with_ao.columns, "缺少AO指标列"
        assert 'ao_color' in df_with_ao.columns, "缺少AO颜色列"
        assert 'zero_cross' in df_with_ao.columns, "缺少零轴穿越列"
        
        # 检查数据有效性
        valid_ao_count = df_with_ao['ao'].notna().sum()
        print(f"✅ AO指标计算成功")
        print(f"   有效AO数据点: {valid_ao_count}")
        print(f"   最新AO值: {df_with_ao['ao'].iloc[-1]:.6f}")
        print(f"   零轴穿越信号数: {(df_with_ao['zero_cross'] != 0).sum()}")
        
        return True
        
    except Exception as e:
        print(f"❌ AO指标计算测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ao_signals():
    """测试AO信号检测"""
    print("\n🎯 测试AO信号检测...")
    
    try:
        from strategies import AwesomeOscillatorStrategy
        
        # 创建模拟交易所和策略
        mock_exchange = Mock()
        strategy = AwesomeOscillatorStrategy(
            exchange=mock_exchange,
            symbol='CFX/USDT',
            short_period=5,
            long_period=34,
            amount=100
        )
        
        # 创建更多测试数据用于信号检测
        dates = pd.date_range(start='2024-01-01', periods=100, freq='1H')
        np.random.seed(42)
        
        # 生成带趋势的价格数据
        base_price = 0.21375
        prices = []
        for i in range(100):
            # 添加一些趋势和周期性
            trend = 0.001 * np.sin(i * 0.1)
            noise = np.random.normal(0, 0.01)
            base_price *= (1 + trend + noise)
            prices.append(base_price)
        
        df = pd.DataFrame({
            'timestamp': dates,
            'open': prices,
            'high': [p * (1 + abs(np.random.normal(0, 0.005))) for p in prices],
            'low': [p * (1 - abs(np.random.normal(0, 0.005))) for p in prices],
            'close': prices,
            'volume': np.random.randint(1000, 10000, 100)
        })
        
        # 生成综合信号
        df_with_signals = strategy.generate_signals(df)
        
        # 验证信号生成
        assert 'ao_signal' in df_with_signals.columns, "缺少AO信号列"
        assert 'signal_type' in df_with_signals.columns, "缺少信号类型列"
        assert 'signal_strength' in df_with_signals.columns, "缺少信号强度列"
        
        # 统计信号
        buy_signals = (df_with_signals['ao_signal'] == 1).sum()
        sell_signals = (df_with_signals['ao_signal'] == -1).sum()
        total_signals = buy_signals + sell_signals
        
        print(f"✅ AO信号检测成功")
        print(f"   买入信号: {buy_signals}")
        print(f"   卖出信号: {sell_signals}")
        print(f"   总信号数: {total_signals}")
        
        # 显示一些信号详情
        signal_rows = df_with_signals[df_with_signals['ao_signal'] != 0]
        if len(signal_rows) > 0:
            print(f"   信号示例:")
            for i, row in signal_rows.head(3).iterrows():
                signal_type = "买入" if row['ao_signal'] == 1 else "卖出"
                print(f"     {signal_type}: {row['signal_type']}, 强度: {row['signal_strength']}")
        
        return True
        
    except Exception as e:
        print(f"❌ AO信号检测测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ao_gui_integration():
    """测试AO策略GUI集成"""
    print("\n🖥️ 测试AO策略GUI集成...")
    
    try:
        from strategy_tabs import AwesomeOscillatorTab
        from unittest.mock import Mock
        import tkinter as tk
        
        # 创建模拟主应用
        mock_app = Mock()
        mock_app.config_manager = Mock()
        mock_app.strategies = {}
        mock_app.strategy_threads = {}
        
        # 测试GUI组件创建
        try:
            root = tk.Tk()
            root.withdraw()  # 隐藏窗口
            
            ao_tab = AwesomeOscillatorTab(root, mock_app)
            
            # 验证GUI组件
            assert hasattr(ao_tab, 'create_widgets'), "缺少create_widgets方法"
            assert hasattr(ao_tab, 'start_strategy'), "缺少start_strategy方法"
            assert hasattr(ao_tab, 'stop_strategy'), "缺少stop_strategy方法"
            assert hasattr(ao_tab, 'run_backtest'), "缺少run_backtest方法"
            assert hasattr(ao_tab, 'save_config'), "缺少save_config方法"
            assert hasattr(ao_tab, 'load_config'), "缺少load_config方法"
            
            root.destroy()
            print("✅ AO策略GUI集成成功")
            print("   所有必要方法都存在")
            
        except tk.TclError:
            # 无GUI环境时的备选验证
            assert hasattr(AwesomeOscillatorTab, '__init__'), "AwesomeOscillatorTab类定义缺失"
            print("✅ AO策略GUI类定义验证成功（无GUI环境）")
        
        return True
        
    except Exception as e:
        print(f"❌ AO策略GUI集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ao_user_friendly_params():
    """测试AO策略用户友好参数"""
    print("\n👥 测试AO策略用户友好参数...")
    
    try:
        from user_friendly_input import PARAMETER_DEFINITIONS
        
        # 检查AO参数定义
        ao_params = [
            'ao_short_period',
            'ao_long_period', 
            'ao_amount',
            'ao_stop_loss',
            'ao_take_profit'
        ]
        
        for param in ao_params:
            assert param in PARAMETER_DEFINITIONS, f"缺少参数定义: {param}"
            
            param_info = PARAMETER_DEFINITIONS[param]
            assert param_info.name, f"{param}缺少名称"
            assert param_info.description, f"{param}缺少描述"
            assert param_info.suggested_range, f"{param}缺少建议范围"
            assert len(param_info.examples) > 0, f"{param}缺少示例"
            assert len(param_info.risk_warnings) > 0, f"{param}缺少风险警告"
            assert param_info.validation_func, f"{param}缺少验证函数"
        
        print(f"✅ AO策略用户友好参数验证成功")
        print(f"   验证了{len(ao_params)}个参数定义")
        
        # 显示参数信息
        for param in ao_params:
            param_info = PARAMETER_DEFINITIONS[param]
            print(f"   {param_info.name}: {param_info.suggested_range}")
        
        return True
        
    except Exception as e:
        print(f"❌ AO策略用户友好参数测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始AO指标策略全面测试")
    print("=" * 60)
    
    tests = [
        ("基本功能", test_ao_strategy_basic),
        ("AO指标计算", test_ao_calculation),
        ("信号检测", test_ao_signals),
        ("GUI集成", test_ao_gui_integration),
        ("用户友好参数", test_ao_user_friendly_params),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 测试: {test_name}")
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 AO策略测试结果: {passed}/{total} 通过")
    print(f"通过率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有AO策略测试通过！")
        print("✅ AO指标策略已准备好集成到系统中")
    else:
        print("⚠️ 部分测试失败，需要修复")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
