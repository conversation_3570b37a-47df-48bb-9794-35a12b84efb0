
538 INFO: PyInstaller: 6.15.0, contrib hooks: 2025.8
538 INFO: Python: 3.8.6
538 INFO: Platform: Windows-10-10.0.14393-SP0
539 INFO: Python environment: c:\program files\python
541 INFO: wrote C:/Users/<USER>/Desktop/yl\MyApp.spec
543 INFO: Removing temporary files and cleaning cache in C:\Users\<USER>\AppData\Local\pyinstaller
558 INFO: Module search paths (PYTHONPATH):
['C:\\Program Files\\python\\Scripts\\pyinstaller.exe',
 'c:\\program files\\python\\python38.zip',
 'c:\\program files\\python\\DLLs',
 'c:\\program files\\python\\lib',
 'c:\\program files\\python',
 'c:\\program files\\python\\lib\\site-packages',
 'c:\\program files\\python\\lib\\site-packages\\win32',
 'c:\\program files\\python\\lib\\site-packages\\win32\\lib',
 'c:\\program files\\python\\lib\\site-packages\\Pythonwin',
 'c:\\program files\\python\\lib\\site-packages\\setuptools\\_vendor',
 'C:\\Users\\<USER>\\Desktop\\yl']
1742 INFO: Appending 'datas' from .spec
1795 INFO: checking Analysis
1795 INFO: Building Analysis because Analysis-00.toc is non existent
1795 INFO: Running Analysis Analysis-00.toc
1795 INFO: Target bytecode optimization level: 0
1795 INFO: Initializing module dependency graph...
1796 INFO: Initializing module graph hook caches...
1849 INFO: Analyzing modules for base_library.zip ...
2868 INFO: Processing standard module hook 'hook-encodings.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks'
3549 INFO: Processing standard module hook 'hook-heapq.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks'
4731 INFO: Processing standard module hook 'hook-pickle.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks'
5766 INFO: Caching module dependency graph...
5823 INFO: Looking for Python shared library...
5843 INFO: Using Python shared library: c:\program files\python\python38.dll
5844 INFO: Analyzing C:\Users\<USER>\Desktop\yl\main.py
5847 INFO: Processing pre-find-module-path hook 'hook-tkinter.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks\\pre_find_module_path'
5849 INFO: TclTkInfo: initializing cached Tcl/Tk info...
6402 INFO: Processing standard module hook 'hook-_tkinter.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks'
6511 INFO: Processing standard module hook 'hook-cryptography.py' from 'c:\\program files\\python\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
7924 INFO: hook-cryptography: cryptography does not seem to be using dynamically linked OpenSSL.
8613 INFO: Processing pre-safe-import-module hook 'hook-typing_extensions.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
8615 INFO: SetuptoolsInfo: initializing cached setuptools info...
13194 INFO: Processing standard module hook 'hook-multiprocessing.util.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks'
13353 INFO: Processing standard module hook 'hook-xml.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks'
13619 INFO: Processing standard module hook 'hook-_ctypes.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks'
14067 INFO: Processing standard module hook 'hook-bcrypt.py' from 'c:\\program files\\python\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
14744 INFO: Processing standard module hook 'hook-platform.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks'
14965 INFO: Processing standard module hook 'hook-urllib3.py' from 'c:\\program files\\python\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
15023 INFO: Processing pre-safe-import-module hook 'hook-urllib3.packages.six.moves.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
16118 INFO: Processing standard module hook 'hook-charset_normalizer.py' from 'c:\\program files\\python\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
17424 INFO: Processing standard module hook 'hook-certifi.py' from 'c:\\program files\\python\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
26956 INFO: Processing standard module hook 'hook-pandas.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks'
29104 INFO: Processing standard module hook 'hook-numpy.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks'
30766 INFO: Processing standard module hook 'hook-difflib.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks'
31158 INFO: Processing standard module hook 'hook-sysconfig.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks'
31457 INFO: Processing standard module hook 'hook-psutil.py' from 'c:\\program files\\python\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
34039 INFO: Processing standard module hook 'hook-pytz.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks'
37594 INFO: Processing standard module hook 'hook-pandas.io.formats.style.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks'
40949 INFO: Processing standard module hook 'hook-pandas.plotting.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks'
41029 INFO: Processing standard module hook 'hook-matplotlib.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks'
41628 INFO: Processing pre-safe-import-module hook 'hook-packaging.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
41764 INFO: Processing pre-safe-import-module hook 'hook-gi.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
41924 INFO: Processing standard module hook 'hook-PIL.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks'
42367 INFO: Processing standard module hook 'hook-PIL.Image.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks'
42916 INFO: Processing standard module hook 'hook-xml.etree.cElementTree.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks'
43186 INFO: Processing standard module hook 'hook-pycparser.py' from 'c:\\program files\\python\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
44259 INFO: Processing pre-safe-import-module hook 'hook-distutils.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
44261 INFO: Processing pre-find-module-path hook 'hook-distutils.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks\\pre_find_module_path'
44619 INFO: Processing standard module hook 'hook-distutils.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks'
44709 INFO: Processing standard module hook 'hook-distutils.util.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks'
44746 INFO: Processing standard module hook 'hook-_osx_support.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks'
44943 INFO: Processing standard module hook 'hook-setuptools.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks'
45163 INFO: Processing pre-safe-import-module hook 'hook-jaraco.text.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
45187 INFO: Processing standard module hook 'hook-jaraco.text.py' from 'c:\\program files\\python\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
45383 INFO: Processing pre-safe-import-module hook 'hook-importlib_resources.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
45398 INFO: Processing standard module hook 'hook-importlib_resources.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks'
45479 INFO: Processing pre-safe-import-module hook 'hook-zipp.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
45523 INFO: Processing pre-safe-import-module hook 'hook-jaraco.functools.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
45545 INFO: Processing pre-safe-import-module hook 'hook-more_itertools.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
45675 INFO: Processing pre-safe-import-module hook 'hook-jaraco.context.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
45696 INFO: Processing pre-safe-import-module hook 'hook-backports.tarfile.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
45781 INFO: Processing standard module hook 'hook-backports.py' from 'c:\\program files\\python\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
45966 INFO: Processing pre-safe-import-module hook 'hook-importlib_metadata.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
45988 INFO: Processing standard module hook 'hook-importlib_metadata.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks'
46205 INFO: Processing pre-safe-import-module hook 'hook-tomli.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
47371 INFO: Processing standard module hook 'hook-pkg_resources.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks'
48662 INFO: Processing pre-safe-import-module hook 'hook-platformdirs.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
48689 INFO: Processing standard module hook 'hook-platformdirs.py' from 'c:\\program files\\python\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
49008 INFO: Processing pre-safe-import-module hook 'hook-wheel.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
50676 INFO: Processing standard module hook 'hook-PIL.ImageFilter.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks'
51472 INFO: Processing standard module hook 'hook-matplotlib.pyplot.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks'
52213 INFO: Processing standard module hook 'hook-matplotlib.backend_bases.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks'
52996 INFO: Processing standard module hook 'hook-dateutil.py' from 'c:\\program files\\python\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
53086 INFO: Processing pre-safe-import-module hook 'hook-six.moves.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
54574 INFO: Processing standard module hook 'hook-matplotlib.backends.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks'
57643 INFO: Processing standard module hook 'hook-sqlalchemy.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks'
60322 INFO: Processing standard module hook 'hook-sqlite3.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks'
60849 INFO: Processing standard module hook 'hook-pandas.io.clipboard.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks'
61298 INFO: Processing standard module hook 'hook-lxml.py' from 'c:\\program files\\python\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
62197 INFO: Processing standard module hook 'hook-lxml.etree.py' from 'c:\\program files\\python\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
62222 INFO: Processing standard module hook 'hook-xml.dom.domreg.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks'
63545 INFO: Processing module hooks (post-graph stage)...
63871 INFO: Processing standard module hook 'hook-lxml.isoschematron.py' from 'c:\\program files\\python\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
63924 WARNING: Hidden import "importlib_resources.trees" not found!
63924 INFO: Processing standard module hook 'hook-matplotlib.backends.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks'
63924 INFO: Matplotlib backend selection method: automatic discovery of used backends
64458 INFO: Trying determine the default backend as first importable candidate from the list: ['QtAgg', 'Qt5Agg', 'Gtk4Agg', 'Gtk3Agg', 'TkAgg', 'WxAgg']
67861 INFO: Selected matplotlib backends: ['TkAgg']
67936 INFO: Processing standard module hook 'hook-jinja2.py' from 'c:\\program files\\python\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
69861 INFO: Processing standard module hook 'hook-PIL.SpiderImagePlugin.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks'
70190 INFO: Processing pre-safe-import-module hook 'hook-autocommand.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
70359 INFO: Processing standard module hook 'hook-setuptools._vendor.importlib_metadata.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks'
70650 INFO: Processing pre-safe-import-module hook 'hook-typeguard.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
71997 INFO: Processing standard module hook 'hook-setuptools._vendor.jaraco.text.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks'
72013 INFO: Processing standard module hook 'hook-sqlalchemy.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks'
72286 INFO:   Found 4 sqlalchemy hidden imports
72287 WARNING: Hidden import "pysqlite2" not found!
72478 WARNING: Hidden import "psycopg2" not found!
74381 INFO: Processing pre-safe-import-module hook 'hook-win32com.py' from 'c:\\program files\\python\\lib\\site-packages\\_pyinstaller_hooks_contrib\\pre_safe_import_module'
74888 INFO: Processing standard module hook 'hook-win32com.py' from 'c:\\program files\\python\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
74900 INFO: Processing standard module hook 'hook-pythoncom.py' from 'c:\\program files\\python\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
75265 INFO: Processing standard module hook 'hook-pywintypes.py' from 'c:\\program files\\python\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
77772 WARNING: Hidden import "sqlalchemy.sql.functions.func" not found!
77787 INFO: Processing standard module hook 'hook-_tkinter.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks'
77788 INFO: Processing standard module hook 'hook-lxml.objectify.py' from 'c:\\program files\\python\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
77976 INFO: Performing binary vs. data reclassification (1863 entries)
98160 INFO: Looking for ctypes DLLs
98230 INFO: Analyzing run-time hooks ...
98245 INFO: Including run-time hook 'pyi_rth_pkgutil.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks\\rthooks'
98247 INFO: Including run-time hook 'pyi_rth_inspect.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks\\rthooks'
98251 INFO: Including run-time hook 'pyi_rth_multiprocessing.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks\\rthooks'
98255 INFO: Including run-time hook 'pyi_rth_cryptography_openssl.py' from 'c:\\program files\\python\\lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks'
98257 INFO: Including run-time hook 'pyi_rth_setuptools.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks\\rthooks'
98259 INFO: Including run-time hook 'pyi_rth_pkgres.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks\\rthooks'
98265 INFO: Including run-time hook 'pyi_rth_mplconfig.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks\\rthooks'
98267 INFO: Processing pre-find-module-path hook 'hook-_pyi_rth_utils.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks\\pre_find_module_path'
98270 INFO: Processing standard module hook 'hook-_pyi_rth_utils.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks'
98282 INFO: Including run-time hook 'pyi_rth_pywintypes.py' from 'c:\\program files\\python\\lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks'
98284 INFO: Including run-time hook 'pyi_rth_pythoncom.py' from 'c:\\program files\\python\\lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks'
98286 INFO: Including run-time hook 'pyi_rth__tkinter.py' from 'c:\\program files\\python\\lib\\site-packages\\PyInstaller\\hooks\\rthooks'
98395 INFO: Creating base_library.zip...
98502 INFO: Looking for dynamic libraries
c:\program files\python\lib\site-packages\PyInstaller\building\build_main.py:227: UserWarning: The numpy.array_api submodule is still experimental. See NEP 47.
  __import__(package)
105457 INFO: Extra DLL search directories (AddDllDirectory): ['c:\\program files\\python\\lib\\site-packages\\numpy\\.libs', 'c:\\program files\\python\\lib\\site-packages\\matplotlib.libs']
105457 INFO: Extra DLL search directories (PATH): []
108298 INFO: Warnings written to C:\Users\<USER>\Desktop\yl\build\MyApp\warn-MyApp.txt
108882 INFO: Graph cross-reference written to C:\Users\<USER>\Desktop\yl\build\MyApp\xref-MyApp.html
109020 INFO: checking PYZ
109020 INFO: Building PYZ because PYZ-00.toc is non existent
109020 INFO: Building PYZ (ZlibArchive) C:\Users\<USER>\Desktop\yl\build\MyApp\PYZ-00.pyz
113851 INFO: Building PYZ (ZlibArchive) C:\Users\<USER>\Desktop\yl\build\MyApp\PYZ-00.pyz completed successfully.
113973 INFO: checking PKG
113974 INFO: Building PKG because PKG-00.toc is non existent
113974 INFO: Building PKG (CArchive) MyApp.pkg
158791 INFO: Building PKG (CArchive) MyApp.pkg completed successfully.
158847 INFO: Bootloader c:\program files\python\lib\site-packages\PyInstaller\bootloader\Windows-64bit-intel\run.exe
158847 INFO: checking EXE
158848 INFO: Building EXE because EXE-00.toc is non existent
158848 INFO: Building EXE from EXE-00.toc
158848 INFO: Copying bootloader EXE to C:\Users\<USER>\Desktop\yl\dist\MyApp.exe
159054 INFO: Copying icon to EXE
Traceback (most recent call last):
  File "c:\program files\python\lib\runpy.py", line 194, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "c:\program files\python\lib\runpy.py", line 87, in _run_code
    exec(code, run_globals)
  File "C:\Program Files\python\Scripts\pyinstaller.exe\__main__.py", line 7, in <module>
  File "c:\program files\python\lib\site-packages\PyInstaller\__main__.py", line 231, in _console_script_run
    run()
  File "c:\program files\python\lib\site-packages\PyInstaller\__main__.py", line 215, in run
    run_build(pyi_config, spec_file, **vars(args))
  File "c:\program files\python\lib\site-packages\PyInstaller\__main__.py", line 70, in run_build
    PyInstaller.building.build_main.main(pyi_config, spec_file, **kwargs)
  File "c:\program files\python\lib\site-packages\PyInstaller\building\build_main.py", line 1282, in main
    build(specfile, distpath, workpath, clean_build)
  File "c:\program files\python\lib\site-packages\PyInstaller\building\build_main.py", line 1220, in build
    exec(code, spec_namespace)
  File "C:/Users/<USER>/Desktop/yl\MyApp.spec", line 19, in <module>
    exe = EXE(
  File "c:\program files\python\lib\site-packages\PyInstaller\building\api.py", line 678, in __init__
    self.__postinit__()
  File "c:\program files\python\lib\site-packages\PyInstaller\building\datastruct.py", line 184, in __postinit__
    self.assemble()
  File "c:\program files\python\lib\site-packages\PyInstaller\building\api.py", line 791, in assemble
    self._retry_operation(icon.CopyIcons, build_name, self.icon)
  File "c:\program files\python\lib\site-packages\PyInstaller\building\api.py", line 1061, in _retry_operation
    return func(*args)
  File "c:\program files\python\lib\site-packages\PyInstaller\utils\win32\icon.py", line 206, in CopyIcons
    srcpath = normalize_icon_type(srcpath, ("exe", "ico"), "ico", config.CONF["workpath"])
  File "c:\program files\python\lib\site-packages\PyInstaller\building\icon.py", line 34, in normalize_icon_type
    raise FileNotFoundError(f"Icon input file {icon_path} not found")
FileNotFoundError: Icon input file C:\Users\<USER>\Desktop\yl\mylogo.ico not found
