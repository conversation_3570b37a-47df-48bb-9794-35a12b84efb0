#!/usr/bin/env python3
"""
复制完整性验证工具
验证量化交易系统文件复制的完整性和正确性
"""

import os
import hashlib
from pathlib import Path
from datetime import datetime

def calculate_file_hash(file_path):
    """计算文件的MD5哈希值"""
    hash_md5 = hashlib.md5()
    try:
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    except Exception as e:
        return f"Error: {e}"

def verify_copy_integrity():
    """验证复制完整性"""
    source_dir = Path(".")
    target_dir = Path("C:/Users/<USER>/Desktop/yl/2.0")
    
    print("🔍 量化交易系统文件复制完整性验证")
    print("=" * 60)
    print(f"源目录: {source_dir.absolute()}")
    print(f"目标目录: {target_dir.absolute()}")
    print(f"验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 核心系统文件列表
    core_files = [
        "strategies.py",
        "exchange_manager.py", 
        "environment_manager.py",
        "parameter_validator.py",
        "error_handler.py",
        "user_friendly_messages.py",
        "test_framework.py",
        "logger.py",
        "risk_manager.py",
        "main.py",
        "main_gui.py",
        "config.py"
    ]
    
    print(f"\n📋 验证核心文件 ({len(core_files)} 个):")
    
    verification_results = []
    
    for filename in core_files:
        source_file = source_dir / filename
        target_file = target_dir / filename
        
        print(f"\n🔍 验证: {filename}")
        
        # 检查源文件是否存在
        if not source_file.exists():
            print(f"  ❌ 源文件不存在")
            verification_results.append((filename, "源文件不存在", False))
            continue
        
        # 检查目标文件是否存在
        if not target_file.exists():
            print(f"  ❌ 目标文件不存在")
            verification_results.append((filename, "目标文件不存在", False))
            continue
        
        # 检查文件大小
        source_size = source_file.stat().st_size
        target_size = target_file.stat().st_size
        
        if source_size != target_size:
            print(f"  ❌ 文件大小不匹配: 源{source_size} vs 目标{target_size}")
            verification_results.append((filename, f"大小不匹配: {source_size} vs {target_size}", False))
            continue
        
        # 检查文件哈希
        source_hash = calculate_file_hash(source_file)
        target_hash = calculate_file_hash(target_file)
        
        if source_hash.startswith("Error") or target_hash.startswith("Error"):
            print(f"  ⚠️ 哈希计算失败")
            verification_results.append((filename, "哈希计算失败", False))
            continue
        
        if source_hash != target_hash:
            print(f"  ❌ 文件内容不匹配")
            verification_results.append((filename, "内容不匹配", False))
            continue
        
        print(f"  ✅ 验证通过 (大小: {source_size} 字节)")
        verification_results.append((filename, "验证通过", True))
    
    # 统计结果
    passed = sum(1 for _, _, success in verification_results if success)
    total = len(verification_results)
    
    print(f"\n{'='*60}")
    print("📊 验证结果统计")
    print(f"{'='*60}")
    print(f"总文件数: {total}")
    print(f"验证通过: {passed}")
    print(f"验证失败: {total - passed}")
    print(f"通过率: {passed/total*100:.1f}%")
    
    # 显示失败的文件
    failed_files = [(f, r) for f, r, s in verification_results if not s]
    if failed_files:
        print(f"\n❌ 验证失败的文件:")
        for filename, reason in failed_files:
            print(f"  {filename}: {reason}")
    
    # 检查目标目录中的文件总数
    target_files = list(target_dir.glob("*"))
    target_py_files = list(target_dir.glob("*.py"))
    target_md_files = list(target_dir.glob("*.md"))
    
    print(f"\n📁 目标目录文件统计:")
    print(f"总文件数: {len(target_files)}")
    print(f"Python文件: {len(target_py_files)}")
    print(f"文档文件: {len(target_md_files)}")
    
    # 检查关键目录结构
    print(f"\n🏗️ 目录结构检查:")
    if target_dir.exists():
        print(f"  ✅ 目标目录存在: {target_dir}")
    else:
        print(f"  ❌ 目标目录不存在: {target_dir}")
    
    # 最终结论
    print(f"\n🎯 最终结论:")
    if passed == total and len(target_py_files) >= 20:
        print("✅ 复制完整性验证通过！")
        print("   所有核心文件都已正确复制到目标目录")
        print("   量化交易系统 2.0 版本准备就绪")
    elif passed == total:
        print("⚠️ 核心文件验证通过，但文件总数可能不足")
        print("   建议检查是否有遗漏的文件")
    else:
        print("❌ 复制完整性验证失败！")
        print("   请检查失败的文件并重新复制")
    
    return passed == total

def main():
    """主函数"""
    try:
        success = verify_copy_integrity()
        return 0 if success else 1
    except Exception as e:
        print(f"\n🔥 验证过程中发生错误: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
