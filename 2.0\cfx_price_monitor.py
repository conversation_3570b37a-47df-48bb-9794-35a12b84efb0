#!/usr/bin/env python3
"""
CFX价格实时监控脚本
"""

import ccxt
import time
from datetime import datetime

def monitor_cfx_price():
    """监控CFX价格"""
    print("CFX价格实时监控")
    print("=" * 40)
    
    # 创建交易所实例
    exchanges = {
        'Binance': ccxt.binance({'enableRateLimit': True}),
        'OKX': ccxt.okx({'enableRateLimit': True}),
        'Huobi': ccxt.huobi({'enableRateLimit': True})
    }
    
    symbol = 'CFX/USDT'
    
    try:
        while True:
            print(f"\n[{datetime.now().strftime('%H:%M:%S')}] CFX价格:")
            
            for name, exchange in exchanges.items():
                try:
                    ticker = exchange.fetch_ticker(symbol)
                    price = ticker['last']
                    change = ticker['percentage']
                    print(f"{name:8}: ${price:.6f} ({change:+.2f}%)")
                except Exception as e:
                    print(f"{name:8}: 获取失败 - {str(e)[:30]}...")
            
            time.sleep(10)  # 每10秒更新一次
            
    except KeyboardInterrupt:
        print("\n监控停止")

if __name__ == "__main__":
    monitor_cfx_price()
