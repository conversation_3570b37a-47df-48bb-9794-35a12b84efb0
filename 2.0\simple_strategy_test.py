#!/usr/bin/env python3
"""
简单策略测试
"""

import sys
import os
from unittest.mock import Mock

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_strategies():
    """测试所有策略"""
    print("🧪 测试策略创建...")
    
    # 创建模拟交易所
    mock_exchange = Mock()
    mock_exchange.fetch_ticker.return_value = {'last': 0.21375}
    mock_exchange.create_limit_buy_order.return_value = {'id': 'buy_123', 'status': 'open'}
    mock_exchange.create_limit_sell_order.return_value = {'id': 'sell_123', 'status': 'open'}
    
    results = []
    
    # 测试网格交易策略
    try:
        from strategies import GridTrading
        strategy = GridTrading(
            exchange=mock_exchange,
            symbol='CFX/USDT',
            base_price=0.21375,
            grid_spacing=1.0,
            grid_count=10,
            order_amount=100
        )
        print("✅ GridTrading 创建成功")
        results.append(("GridTrading", True, ""))
    except Exception as e:
        print(f"❌ GridTrading 创建失败: {e}")
        results.append(("GridTrading", False, str(e)))
    
    # 测试移动平均线策略
    try:
        from strategies import MovingAverageStrategy
        strategy = MovingAverageStrategy(
            exchange=mock_exchange,
            symbol='CFX/USDT',
            short_period=10,
            long_period=30,
            amount=100
        )
        print("✅ MovingAverageStrategy 创建成功")
        results.append(("MovingAverageStrategy", True, ""))
    except Exception as e:
        print(f"❌ MovingAverageStrategy 创建失败: {e}")
        results.append(("MovingAverageStrategy", False, str(e)))

    # 测试RSI策略
    try:
        from strategies import RSIStrategy
        strategy = RSIStrategy(
            exchange=mock_exchange,
            symbol='CFX/USDT',
            period=14,
            oversold=30,
            overbought=70,
            amount=100
        )
        print("✅ RSIStrategy 创建成功")
        results.append(("RSIStrategy", True, ""))
    except Exception as e:
        print(f"❌ RSIStrategy 创建失败: {e}")
        results.append(("RSIStrategy", False, str(e)))
    
    # 测试AO策略
    try:
        from strategies import AwesomeOscillatorStrategy
        strategy = AwesomeOscillatorStrategy(
            exchange=mock_exchange,
            symbol='CFX/USDT',
            short_period=5,
            long_period=34,
            amount=100
        )
        print("✅ AwesomeOscillatorStrategy 创建成功")
        results.append(("AwesomeOscillatorStrategy", True, ""))
    except Exception as e:
        print(f"❌ AwesomeOscillatorStrategy 创建失败: {e}")
        results.append(("AwesomeOscillatorStrategy", False, str(e)))
    
    # 测试FMACD策略
    try:
        from strategies import FMACDStrategy
        strategy = FMACDStrategy(
            exchange=mock_exchange,
            symbol='CFX/USDT',
            fast_period=12,
            slow_period=26,
            signal_period=9,
            amount=100
        )
        print("✅ FMACDStrategy 创建成功")
        results.append(("FMACDStrategy", True, ""))
    except Exception as e:
        print(f"❌ FMACDStrategy 创建失败: {e}")
        results.append(("FMACDStrategy", False, str(e)))
    
    # 统计结果
    passed = sum(1 for _, success, _ in results if success)
    total = len(results)
    
    print(f"\n📊 策略测试结果: {passed}/{total} 通过")
    
    # 显示失败详情
    for name, success, error in results:
        if not success:
            print(f"❌ {name}: {error}")
    
    return passed == total

if __name__ == "__main__":
    success = test_strategies()
    print("🎉 所有策略测试通过!" if success else "⚠️ 部分策略测试失败")
