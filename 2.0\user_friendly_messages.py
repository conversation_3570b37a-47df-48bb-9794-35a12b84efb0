#!/usr/bin/env python3
"""
用户友好提示模块
为各种操作和错误情况提供中文提示和解决建议
"""

import tkinter.messagebox as messagebox
from typing import List, Dict, Any, Optional
from logger import get_logger

class UserMessageCenter:
    """用户消息中心"""
    
    def __init__(self):
        self.logger = get_logger("user_messages")
        
        # 成功消息模板
        self.success_messages = {
            "strategy_started": {
                "title": "策略启动成功",
                "message": "交易策略已成功启动并开始运行",
                "tips": [
                    "请密切关注策略运行状态",
                    "建议定期检查交易日志",
                    "如需停止，请点击停止按钮"
                ]
            },
            "strategy_stopped": {
                "title": "策略停止成功",
                "message": "交易策略已安全停止",
                "tips": [
                    "所有挂单已被取消",
                    "可以查看交易历史记录",
                    "如需重新启动，请重新配置参数"
                ]
            },
            "connection_established": {
                "title": "连接成功",
                "message": "已成功连接到交易所",
                "tips": [
                    "现在可以开始交易",
                    "建议先在模拟模式下测试",
                    "确保账户余额充足"
                ]
            },
            "order_placed": {
                "title": "订单提交成功",
                "message": "交易订单已成功提交到交易所",
                "tips": [
                    "订单正在等待成交",
                    "可以在订单管理中查看状态",
                    "如需取消，请及时操作"
                ]
            }
        }
        
        # 警告消息模板
        self.warning_messages = {
            "simulation_mode": {
                "title": "当前为模拟模式",
                "message": "您正在模拟环境中运行，不会产生真实交易",
                "tips": [
                    "模拟模式用于测试策略",
                    "所有交易都是虚拟的",
                    "如需实盘交易，请切换到实盘模式"
                ]
            },
            "testnet_mode": {
                "title": "当前为测试网模式",
                "message": "您正在测试网环境中运行，使用测试资金",
                "tips": [
                    "测试网用于验证策略",
                    "使用的是测试资金，非真实资金",
                    "确认策略无误后可切换到实盘模式"
                ]
            },
            "live_mode_warning": {
                "title": "⚠️ 实盘模式风险警告",
                "message": "您即将切换到实盘模式，这将使用真实资金进行交易！",
                "tips": [
                    "🚨 实盘交易存在资金损失风险",
                    "📊 请确保已在模拟环境充分测试",
                    "💰 建议从小资金开始测试",
                    "📈 密切监控策略表现",
                    "🛑 设置合理的止损参数"
                ]
            },
            "insufficient_balance": {
                "title": "余额不足警告",
                "message": "当前账户余额可能不足以支持策略运行",
                "tips": [
                    "请检查账户余额",
                    "考虑减少交易数量",
                    "或者充值到交易账户"
                ]
            },
            "high_risk_parameters": {
                "title": "高风险参数警告",
                "message": "检测到您使用了高风险的策略参数",
                "tips": [
                    "建议降低交易数量",
                    "设置更严格的止损",
                    "在模拟模式下充分测试"
                ]
            }
        }
        
        # 操作指导消息
        self.guidance_messages = {
            "first_time_setup": {
                "title": "欢迎使用量化交易系统",
                "message": "这是您第一次使用本系统，让我们来进行初始设置",
                "steps": [
                    "1. 选择交易所并配置API密钥",
                    "2. 在模拟模式下测试策略",
                    "3. 验证策略参数和风险控制",
                    "4. 确认无误后切换到实盘模式"
                ]
            },
            "strategy_selection": {
                "title": "策略选择指导",
                "message": "请根据市场情况和风险偏好选择合适的策略",
                "strategies": {
                    "网格交易": "适合震荡市场，通过高抛低吸获利",
                    "移动平均线": "适合趋势市场，跟随趋势方向交易",
                    "RSI策略": "基于超买超卖信号，适合反转交易",
                    "成交量突破": "基于成交量异常，捕捉突破机会"
                }
            },
            "parameter_tuning": {
                "title": "参数调优建议",
                "message": "合理的参数设置是策略成功的关键",
                "tips": [
                    "🎯 网格间距: 建议1%-5%，根据波动率调整",
                    "📊 移动平均: 短期5-20，长期20-100",
                    "📈 RSI阈值: 超卖20-40，超买60-80",
                    "💹 成交量倍数: 建议1.5-3倍平均成交量",
                    "💰 交易数量: 不超过总资金的10%"
                ]
            }
        }
    
    def show_success(self, message_type: str, custom_message: str = None, 
                    custom_tips: List[str] = None) -> None:
        """显示成功消息"""
        if message_type in self.success_messages:
            template = self.success_messages[message_type]
            title = template["title"]
            message = custom_message or template["message"]
            tips = custom_tips or template["tips"]
            
            full_message = f"{message}\n\n💡 提示:\n"
            for i, tip in enumerate(tips, 1):
                full_message += f"   {i}. {tip}\n"
            
            messagebox.showinfo(title, full_message)
            self.logger.info(f"显示成功消息: {title} - {message}")
    
    def show_warning(self, message_type: str, custom_message: str = None, 
                    custom_tips: List[str] = None) -> bool:
        """显示警告消息，返回用户确认结果"""
        if message_type in self.warning_messages:
            template = self.warning_messages[message_type]
            title = template["title"]
            message = custom_message or template["message"]
            tips = custom_tips or template["tips"]
            
            full_message = f"{message}\n\n💡 注意事项:\n"
            for i, tip in enumerate(tips, 1):
                full_message += f"   {i}. {tip}\n"
            
            if message_type == "live_mode_warning":
                full_message += "\n⚠️ 您确定要继续吗？"
                result = messagebox.askyesno(title, full_message)
                self.logger.info(f"显示警告消息: {title} - 用户选择: {'确认' if result else '取消'}")
                return result
            else:
                messagebox.showwarning(title, full_message)
                self.logger.info(f"显示警告消息: {title}")
                return True
    
    def show_guidance(self, message_type: str) -> None:
        """显示操作指导"""
        if message_type in self.guidance_messages:
            template = self.guidance_messages[message_type]
            title = template["title"]
            message = template["message"]
            
            full_message = f"{message}\n\n"
            
            if "steps" in template:
                full_message += "📋 操作步骤:\n"
                for step in template["steps"]:
                    full_message += f"   {step}\n"
            
            if "strategies" in template:
                full_message += "📊 策略说明:\n"
                for strategy, description in template["strategies"].items():
                    full_message += f"   • {strategy}: {description}\n"
            
            if "tips" in template:
                full_message += "💡 建议:\n"
                for tip in template["tips"]:
                    full_message += f"   {tip}\n"
            
            messagebox.showinfo(title, full_message)
            self.logger.info(f"显示指导消息: {title}")
    
    def show_parameter_validation_error(self, parameter_name: str, error_message: str, 
                                      suggestions: List[str]) -> None:
        """显示参数验证错误"""
        title = f"参数错误: {parameter_name}"
        message = f"❌ {error_message}\n\n💡 解决建议:\n"
        
        for i, suggestion in enumerate(suggestions, 1):
            message += f"   {i}. {suggestion}\n"
        
        messagebox.showerror(title, message)
        self.logger.error(f"参数验证错误: {parameter_name} - {error_message}")
    
    def show_strategy_recommendation(self, market_condition: str) -> None:
        """根据市场情况推荐策略"""
        recommendations = {
            "trending": {
                "title": "趋势市场策略推荐",
                "message": "检测到当前市场处于趋势状态",
                "recommended": ["移动平均线策略", "成交量突破策略"],
                "tips": [
                    "趋势市场适合跟随策略",
                    "避免使用网格交易",
                    "设置合理的止损点"
                ]
            },
            "ranging": {
                "title": "震荡市场策略推荐",
                "message": "检测到当前市场处于震荡状态",
                "recommended": ["网格交易策略", "RSI策略"],
                "tips": [
                    "震荡市场适合高抛低吸",
                    "网格间距不宜过大",
                    "注意支撑阻力位"
                ]
            },
            "volatile": {
                "title": "高波动市场策略推荐",
                "message": "检测到当前市场波动较大",
                "recommended": ["RSI策略"],
                "tips": [
                    "高波动市场风险较大",
                    "建议降低仓位",
                    "设置更严格的止损"
                ]
            }
        }
        
        if market_condition in recommendations:
            rec = recommendations[market_condition]
            message = f"{rec['message']}\n\n📊 推荐策略:\n"
            
            for strategy in rec["recommended"]:
                message += f"   • {strategy}\n"
            
            message += "\n💡 注意事项:\n"
            for tip in rec["tips"]:
                message += f"   • {tip}\n"
            
            messagebox.showinfo(rec["title"], message)
            self.logger.info(f"显示策略推荐: {market_condition}")
    
    def confirm_risky_operation(self, operation: str, risks: List[str]) -> bool:
        """确认高风险操作"""
        title = f"⚠️ 风险确认: {operation}"
        message = f"您即将执行高风险操作: {operation}\n\n🚨 风险提示:\n"

        for risk in risks:
            message += f"   • {risk}\n"

        message += "\n❓ 您确定要继续吗？"

        result = messagebox.askyesno(title, message)
        self.logger.info(f"风险操作确认: {operation} - 用户选择: {'确认' if result else '取消'}")
        return result

    def show_confirmation(self, message_type: str, custom_message: str = None) -> bool:
        """显示确认对话框"""
        if custom_message:
            title = "确认操作"
            message = custom_message
        else:
            # 可以根据message_type提供预定义的消息
            title = "确认操作"
            message = "您确定要继续此操作吗？"

        result = messagebox.askyesno(title, message)
        self.logger.info(f"确认对话框: {message_type} - 用户选择: {'确认' if result else '取消'}")
        return result

# 全局用户消息中心实例
user_messages = UserMessageCenter()
