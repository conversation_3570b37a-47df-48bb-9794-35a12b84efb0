# 量化交易系统 v1.0

基于4.txt文件内容开发的完整GUI量化交易系统，支持多种交易策略和风险管理功能。

## 功能特性

### 支持的交易策略
- **网格交易策略 (GridTrading)** - 在价格区间内设置买卖网格，适合震荡市场
- **移动平均线策略 (MovingAverageStrategy)** - 基于均线交叉信号进行交易
- **RSI反转策略 (RSIStrategy)** - 利用RSI指标识别超买超卖区域
- **成交量突破策略 (VolumeBreakoutStrategy)** - 结合价格突破和成交量放大
- **智能网格策略 (SmartGridStrategy)** - 根据市场趋势动态调整网格参数

### 支持的交易所
- **Binance** - 全球最大的加密货币交易所
- **OKX** - 知名数字资产交易平台
- **Huobi** - 老牌加密货币交易所

### 核心功能
- 🔐 **API密钥加密存储** - 使用AES加密保护API凭证
- 📊 **实时市场数据** - 获取实时价格、K线、成交量等数据
- ⚠️ **风险管理系统** - 日亏损限制、最大仓位控制、回撤保护
- 📝 **完整日志记录** - 记录所有交易操作和系统事件
- 🎯 **回测功能** - 验证策略历史表现
- 🖥️ **直观GUI界面** - 易于使用的图形界面

## 安装要求

### Python版本
- Python 3.8 或更高版本

### 依赖包
```bash
pip install -r requirements.txt
```

主要依赖：
- `ccxt>=4.0.0` - 交易所API统一接口
- `pandas>=1.5.0` - 数据处理
- `numpy>=1.24.0` - 数值计算
- `cryptography>=3.4.8` - 加密功能
- `tkinter` - GUI界面（Python内置）

## 快速开始

### 1. 下载和安装
```bash
# 克隆或下载项目文件
# 安装依赖
pip install -r requirements.txt
```

### 2. 启动系统
```bash
python main.py
```

### 3. 连接交易所
1. 点击菜单 "文件" -> "连接交易所"
2. 选择交易所（Binance/OKX/Huobi）
3. 输入API Key和Secret
4. 建议先使用测试环境

### 4. 设置加密密码
1. 点击菜单 "文件" -> "设置密码"
2. 输入用于加密API凭证的密码
3. 密码将用于保护您的API密钥

### 5. 配置和启动策略
1. 选择相应的策略标签页
2. 配置策略参数
3. 点击"启动策略"开始交易

## 使用指南

### 网格交易策略
适合震荡市场，通过在价格区间内设置买卖网格获利。

**参数说明：**
- **交易对**: 选择要交易的币种对，如BTC/USDT
- **基准价格**: 网格中心价格
- **网格间距**: 网格之间的价格间距（百分比）
- **网格数量**: 总网格数量
- **交易数量**: 每次交易的数量

**使用建议：**
- 适合BTC、ETH等波动较大的主流币种
- 网格间距建议设置为1-2%
- 在震荡行情中效果最佳

### 移动平均线策略
基于短期和长期移动平均线的交叉信号进行交易。

**参数说明：**
- **短期均线**: 短期移动平均线周期（如10）
- **长期均线**: 长期移动平均线周期（如30）
- **时间框架**: K线周期（1h、4h、1d等）

**交易信号：**
- 金叉（短均线上穿长均线）→ 买入
- 死叉（短均线下穿长均线）→ 卖出

### RSI反转策略
利用RSI指标识别超买超卖区域进行反转交易。

**参数说明：**
- **RSI周期**: RSI计算周期（通常为14）
- **超卖阈值**: RSI低于此值时买入（通常为30）
- **超买阈值**: RSI高于此值时卖出（通常为70）

**适用场景：**
- 震荡市场
- 短期反转机会捕捉

## 风险管理

### 风险控制参数
- **最大日亏损**: 单日最大亏损比例（默认2%）
- **最大仓位**: 单个策略最大仓位比例（默认10%）
- **最大回撤**: 账户最大回撤比例（默认15%）

### 安全建议
1. **小资金测试**: 建议先用小额资金测试策略
2. **使用测试环境**: 在实盘前先在测试环境验证
3. **分散投资**: 不要将所有资金投入单一策略
4. **定期监控**: 密切关注策略运行状态
5. **及时止损**: 设置合理的止损点

## 文件结构

```
量化交易系统/
├── main.py                 # 主程序入口
├── main_gui.py             # GUI主界面
├── strategy_tabs.py        # 策略标签页
├── strategies.py           # 基础交易策略
├── strategies_extended.py  # 扩展交易策略
├── exchange_manager.py     # 交易所管理
├── risk_manager.py         # 风险管理
├── config.py              # 配置管理
├── logger.py              # 日志系统
├── requirements.txt       # 依赖包列表
├── README.md             # 说明文档
├── logs/                 # 日志文件目录
├── config.json           # 配置文件（自动生成）
└── data/                 # 数据文件目录
```

## 注意事项

### 安全警告
- **保护API密钥**: 不要将API密钥分享给他人
- **使用强密码**: 设置复杂的加密密码
- **定期备份**: 备份重要的配置文件

### 交易风险
- **市场风险**: 加密货币市场波动巨大
- **技术风险**: 策略可能存在缺陷
- **流动性风险**: 某些币种可能流动性不足
- **API风险**: 交易所API可能出现故障

### 免责声明
本系统仅供学习和研究使用，不构成投资建议。使用本系统进行实际交易的所有风险由用户自行承担。

## 技术支持

如遇到问题，请检查：
1. Python版本是否符合要求
2. 依赖包是否正确安装
3. API密钥是否有效
4. 网络连接是否正常
5. 查看日志文件获取详细错误信息

## 更新日志

### v1.0 (2024-01-01)
- 初始版本发布
- 支持5种交易策略
- 支持3个主流交易所
- 完整的风险管理系统
- GUI界面实现

---

**警告**: 量化交易存在风险，请谨慎使用，建议先在测试环境中验证策略效果。
