@echo off
chcp 65001 >nul
echo ========================================
echo 量化交易系统快速打包工具
echo ========================================

echo.
echo [1/5] 清理环境...
if exist build rmdir /s /q build
if exist dist rmdir /s /q dist
if exist __pycache__ rmdir /s /q __pycache__
if exist "*.spec" del /q *.spec
echo 清理完成

echo.
echo [2/5] 重新安装PyInstaller...
python -m pip uninstall pyinstaller -y >nul 2>&1
python -m pip install pyinstaller >nul 2>&1
echo PyInstaller重新安装完成

echo.
echo [3/5] 检查依赖包...
python -c "import tkinter, ccxt, pandas, numpy, requests, cryptography; print('所有依赖包正常')" 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo 警告: 部分依赖包可能缺失
    echo 正在安装依赖包...
    python -m pip install -r requirements.txt >nul 2>&1
)

echo.
echo [4/5] 开始打包...
echo 这可能需要几分钟时间，请耐心等待...

python -m PyInstaller ^
    --onefile ^
    --windowed ^
    --name="QuantTradingSystem" ^
    --hidden-import=tkinter ^
    --hidden-import=tkinter.ttk ^
    --hidden-import=tkinter.messagebox ^
    --hidden-import=tkinter.filedialog ^
    --hidden-import=ccxt ^
    --hidden-import=pandas ^
    --hidden-import=numpy ^
    --hidden-import=requests ^
    --hidden-import=cryptography ^
    --hidden-import=threading ^
    --hidden-import=queue ^
    --hidden-import=json ^
    --hidden-import=time ^
    --hidden-import=datetime ^
    --hidden-import=logging ^
    --hidden-import=hashlib ^
    --hidden-import=hmac ^
    --hidden-import=base64 ^
    --exclude-module=matplotlib ^
    --exclude-module=scipy ^
    --exclude-module=IPython ^
    --exclude-module=jupyter ^
    --exclude-module=notebook ^
    --exclude-module=pytest ^
    main.py

echo.
echo [5/5] 检查结果...
if exist "dist\QuantTradingSystem.exe" (
    echo ========================================
    echo          打包成功！
    echo ========================================
    echo.
    echo 可执行文件位置: dist\QuantTradingSystem.exe
    echo 文件大小: 
    for %%A in ("dist\QuantTradingSystem.exe") do echo %%~zA 字节
    echo.
    echo 您现在可以:
    echo 1. 运行 dist\QuantTradingSystem.exe 测试程序
    echo 2. 将exe文件复制到其他电脑使用
    echo 3. 创建桌面快捷方式
    echo.
) else (
    echo ========================================
    echo          打包失败！
    echo ========================================
    echo.
    echo 可能的解决方案:
    echo 1. 检查Python版本冲突
    echo 2. 使用虚拟环境
    echo 3. 手动安装缺失的依赖包
    echo.
    echo 详细解决方案请查看: build_solution.md
)

echo ========================================
pause
