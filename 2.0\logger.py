"""
日志系统模块
提供统一的日志记录功能，支持文件和控制台输出
"""
import logging
import os
from datetime import datetime
import threading

class TradingLogger:
    def __init__(self, log_dir="logs"):
        self.log_dir = log_dir
        self.loggers = {}
        self.lock = threading.Lock()
        self.gui_handlers = []  # 存储GUI日志处理器

        # 创建日志目录
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)

        # 设置主日志器
        self.setup_main_logger()
    
    def setup_main_logger(self):
        """设置主日志器"""
        logger = logging.getLogger("trading_system")
        logger.setLevel(logging.DEBUG)
        
        # 清除已有的处理器
        logger.handlers.clear()
        
        # 文件处理器
        log_file = os.path.join(self.log_dir, f"trading_{datetime.now().strftime('%Y%m%d')}.log")
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # 格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)

        # 添加GUI处理器（如果有的话）
        for gui_handler in self.gui_handlers:
            logger.addHandler(gui_handler)

        self.loggers["main"] = logger
    
    def get_strategy_logger(self, strategy_name: str):
        """获取策略专用日志器"""
        with self.lock:
            if strategy_name not in self.loggers:
                logger = logging.getLogger(f"strategy_{strategy_name}")
                logger.setLevel(logging.DEBUG)
                
                # 策略专用日志文件
                log_file = os.path.join(
                    self.log_dir, 
                    f"{strategy_name}_{datetime.now().strftime('%Y%m%d')}.log"
                )
                file_handler = logging.FileHandler(log_file, encoding='utf-8')
                file_handler.setLevel(logging.DEBUG)
                
                formatter = logging.Formatter(
                    '%(asctime)s - %(levelname)s - %(message)s',
                    datefmt='%Y-%m-%d %H:%M:%S'
                )
                file_handler.setFormatter(formatter)

                logger.addHandler(file_handler)

                # 添加GUI处理器（如果有的话）
                for gui_handler in self.gui_handlers:
                    logger.addHandler(gui_handler)

                self.loggers[strategy_name] = logger
            
            return self.loggers[strategy_name]
    
    def log_trade(self, strategy_name: str, action: str, symbol: str, 
                  amount: float, price: float, order_id: str = None):
        """记录交易日志"""
        logger = self.get_strategy_logger(strategy_name)
        message = f"交易执行 - {action} {symbol} 数量:{amount:.6f} 价格:{price:.2f}"
        if order_id:
            message += f" 订单ID:{order_id}"
        logger.info(message)
    
    def log_error(self, strategy_name: str, error_msg: str, exception: Exception = None):
        """记录错误日志"""
        logger = self.get_strategy_logger(strategy_name)
        if exception:
            logger.error(f"错误: {error_msg} - 异常: {str(exception)}")
        else:
            logger.error(f"错误: {error_msg}")
    
    def log_strategy_start(self, strategy_name: str, config: dict):
        """记录策略启动日志"""
        logger = self.get_strategy_logger(strategy_name)
        logger.info(f"策略启动 - 配置: {config}")
    
    def log_strategy_stop(self, strategy_name: str, reason: str = "用户停止"):
        """记录策略停止日志"""
        logger = self.get_strategy_logger(strategy_name)
        logger.info(f"策略停止 - 原因: {reason}")
    
    def log_performance(self, strategy_name: str, performance_data: dict):
        """记录性能数据"""
        logger = self.get_strategy_logger(strategy_name)
        logger.info(f"性能统计 - {performance_data}")
    
    def log_risk_alert(self, strategy_name: str, alert_type: str, message: str):
        """记录风险警报"""
        logger = self.get_strategy_logger(strategy_name)
        logger.warning(f"风险警报 - {alert_type}: {message}")

    def add_gui_handler(self, handler):
        """添加GUI日志处理器"""
        with self.lock:
            self.gui_handlers.append(handler)

            # 为所有现有的日志器添加GUI处理器
            for logger in self.loggers.values():
                logger.addHandler(handler)

    def remove_gui_handler(self, handler):
        """移除GUI日志处理器"""
        with self.lock:
            if handler in self.gui_handlers:
                self.gui_handlers.remove(handler)

                # 从所有日志器中移除GUI处理器
                for logger in self.loggers.values():
                    if handler in logger.handlers:
                        logger.removeHandler(handler)

# 全局日志器实例
trading_logger = TradingLogger()

def get_logger(strategy_name: str = "main"):
    """获取日志器的便捷函数"""
    if strategy_name == "main":
        return trading_logger.loggers["main"]
    return trading_logger.get_strategy_logger(strategy_name)

def log_trade(strategy_name: str, action: str, symbol: str, amount: float, price: float, order_id: str = None):
    """记录交易的便捷函数"""
    trading_logger.log_trade(strategy_name, action, symbol, amount, price, order_id)

def log_error(strategy_name: str, error_msg: str, exception: Exception = None):
    """记录错误的便捷函数"""
    trading_logger.log_error(strategy_name, error_msg, exception)

def log_strategy_start(strategy_name: str, config: dict):
    """记录策略启动的便捷函数"""
    trading_logger.log_strategy_start(strategy_name, config)

def log_strategy_stop(strategy_name: str, reason: str = "用户停止"):
    """记录策略停止的便捷函数"""
    trading_logger.log_strategy_stop(strategy_name, reason)

def log_performance(strategy_name: str, performance_data: dict):
    """记录性能数据的便捷函数"""
    trading_logger.log_performance(strategy_name, performance_data)

def log_risk_alert(strategy_name: str, alert_type: str, message: str):
    """记录风险警报的便捷函数"""
    trading_logger.log_risk_alert(strategy_name, alert_type, message)

def add_gui_log_handler(handler):
    """添加GUI日志处理器的便捷函数"""
    trading_logger.add_gui_handler(handler)

def remove_gui_log_handler(handler):
    """移除GUI日志处理器的便捷函数"""
    trading_logger.remove_gui_handler(handler)
