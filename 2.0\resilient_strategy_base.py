#!/usr/bin/env python3
"""
具有自动重连功能的策略基类
为所有策略提供网络连接容错和自动重连机制
"""

import time
import threading
from logger import get_logger

class ResilientStrategyBase:
    """具有自动重连功能的策略基类"""
    
    def __init__(self, exchange, symbol, exchange_manager=None):
        self.exchange = exchange
        self.symbol = symbol
        self.exchange_manager = exchange_manager
        self.logger = get_logger(self.__class__.__name__)
        
        # 连接状态管理
        self.connection_healthy = True
        self.last_successful_call = time.time()
        self.connection_check_interval = 60  # 每分钟检查一次连接
        self.max_retry_attempts = 3
        self.retry_delay = 5  # 重试间隔秒数
        
        # 启动连接监控线程
        self.start_connection_monitor()
    
    def start_connection_monitor(self):
        """启动连接监控线程"""
        def monitor_connection():
            while getattr(self, 'running', True):
                try:
                    # 检查连接健康状态
                    if time.time() - self.last_successful_call > self.connection_check_interval:
                        self.check_connection_health()
                    
                    time.sleep(30)  # 每30秒检查一次
                except Exception as e:
                    self.logger.error(f"连接监控线程错误: {e}")
                    time.sleep(60)
        
        monitor_thread = threading.Thread(target=monitor_connection, daemon=True)
        monitor_thread.start()
    
    def check_connection_health(self):
        """检查连接健康状态"""
        try:
            # 简单的连接测试
            ticker = self.exchange.fetch_ticker(self.symbol)
            if ticker and 'last' in ticker:
                self.connection_healthy = True
                self.last_successful_call = time.time()
                return True
            else:
                raise Exception("获取数据异常")
        except Exception as e:
            self.logger.warning(f"连接健康检查失败: {e}")
            self.connection_healthy = False
            return False
    
    def safe_exchange_call(self, method_name, *args, **kwargs):
        """安全的交易所API调用，带自动重连"""
        for attempt in range(self.max_retry_attempts):
            try:
                # 获取交易所方法
                method = getattr(self.exchange, method_name)
                
                # 执行API调用
                result = method(*args, **kwargs)
                
                # 调用成功，更新状态
                self.connection_healthy = True
                self.last_successful_call = time.time()
                
                return result
                
            except Exception as e:
                self.logger.warning(f"API调用 {method_name} 失败 (第{attempt+1}次): {e}")
                
                # 标记连接不健康
                self.connection_healthy = False
                
                if attempt < self.max_retry_attempts - 1:
                    # 尝试重连
                    if self.attempt_reconnection():
                        self.logger.info(f"重连成功，重试 {method_name}")
                        continue
                    else:
                        # 重连失败，等待后重试
                        wait_time = self.retry_delay * (attempt + 1)
                        self.logger.info(f"等待 {wait_time} 秒后重试...")
                        time.sleep(wait_time)
                else:
                    # 最后一次尝试失败
                    self.logger.error(f"API调用 {method_name} 最终失败: {e}")
                    raise e
        
        return None
    
    def attempt_reconnection(self):
        """尝试重新连接"""
        if not self.exchange_manager:
            self.logger.warning("没有exchange_manager，无法自动重连")
            return False
        
        try:
            self.logger.info("尝试自动重连...")
            
            # 检查并重连所有交易所
            result = self.exchange_manager.check_and_reconnect_all(max_retries=2)
            
            if result['reconnected']:
                self.logger.info(f"重连成功: {result['reconnected']}")
                
                # 更新exchange实例
                current_exchange = self.exchange_manager.get_exchange()
                if current_exchange:
                    self.exchange = current_exchange
                    return True
            
            if result['failed']:
                self.logger.error(f"重连失败: {result['failed']}")
            
            return False
            
        except Exception as e:
            self.logger.error(f"自动重连失败: {e}")
            return False
    
    def get_current_price_safe(self):
        """安全的价格获取方法"""
        try:
            # 方法1: 使用ticker
            ticker = self.safe_exchange_call('fetch_ticker', self.symbol)
            if ticker and ticker.get('last', 0) > 0:
                price = float(ticker['last'])
                self.last_price = getattr(self, 'last_price', price)
                self.last_price = price
                return price
            
            # 方法2: 使用orderbook
            orderbook = self.safe_exchange_call('fetch_order_book', self.symbol, 5)
            if orderbook and orderbook.get('bids') and orderbook.get('asks'):
                bid_price = float(orderbook['bids'][0][0])
                ask_price = float(orderbook['asks'][0][0])
                price = (bid_price + ask_price) / 2
                self.last_price = price
                return price
            
            # 方法3: 使用最近交易
            trades = self.safe_exchange_call('fetch_trades', self.symbol, 1)
            if trades and len(trades) > 0:
                price = float(trades[0]['price'])
                self.last_price = price
                return price
            
            # 如果所有方法都失败，返回缓存价格
            return getattr(self, 'last_price', 0.0)
            
        except Exception as e:
            self.logger.error(f"获取价格失败: {e}")
            return getattr(self, 'last_price', 0.0)
    
    def get_balance_safe(self):
        """安全的余额获取方法"""
        try:
            return self.safe_exchange_call('fetch_balance')
        except Exception as e:
            self.logger.error(f"获取余额失败: {e}")
            return None
    
    def create_order_safe(self, order_type, side, amount, price=None):
        """安全的下单方法"""
        try:
            if order_type == 'market':
                if side == 'buy':
                    return self.safe_exchange_call('create_market_buy_order', self.symbol, amount)
                else:
                    return self.safe_exchange_call('create_market_sell_order', self.symbol, amount)
            elif order_type == 'limit':
                if side == 'buy':
                    return self.safe_exchange_call('create_limit_buy_order', self.symbol, amount, price)
                else:
                    return self.safe_exchange_call('create_limit_sell_order', self.symbol, amount, price)
        except Exception as e:
            self.logger.error(f"下单失败: {e}")
            return None
    
    def cancel_order_safe(self, order_id):
        """安全的撤单方法"""
        try:
            return self.safe_exchange_call('cancel_order', order_id, self.symbol)
        except Exception as e:
            self.logger.error(f"撤单失败: {e}")
            return None
    
    def fetch_order_safe(self, order_id):
        """安全的查询订单方法"""
        try:
            return self.safe_exchange_call('fetch_order', order_id, self.symbol)
        except Exception as e:
            self.logger.error(f"查询订单失败: {e}")
            return None
    
    def get_connection_status(self):
        """获取连接状态信息"""
        return {
            'healthy': self.connection_healthy,
            'last_successful_call': self.last_successful_call,
            'time_since_last_success': time.time() - self.last_successful_call
        }
    
    def log_connection_status(self):
        """记录连接状态"""
        status = self.get_connection_status()
        if status['healthy']:
            self.logger.info(f"连接状态: 健康 (最后成功调用: {status['time_since_last_success']:.1f}秒前)")
        else:
            self.logger.warning(f"连接状态: 不健康 (最后成功调用: {status['time_since_last_success']:.1f}秒前)")

# 使用示例：
# class MyStrategy(ResilientStrategyBase):
#     def __init__(self, exchange, symbol, exchange_manager):
#         super().__init__(exchange, symbol, exchange_manager)
#         # 其他初始化代码
#     
#     def run(self):
#         while self.running:
#             # 使用安全方法
#             price = self.get_current_price_safe()
#             balance = self.get_balance_safe()
#             # 其他策略逻辑
