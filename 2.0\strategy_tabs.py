"""
策略标签页模块
包含所有策略的GUI界面实现
"""
import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
from datetime import datetime
import pandas as pd

from strategies import GridTrading, MovingAverageStrategy, RSIStrategy
from strategies_extended import VolumeBreakoutStrategyExtended, SmartGridStrategy
from exchange_manager import exchange_manager
from logger import get_logger
from user_friendly_input import create_user_friendly_input, UserFriendlyInput

class BaseStrategyTab:
    """策略标签页基类"""
    def __init__(self, parent, main_app):
        self.parent = parent
        self.main_app = main_app
        self.frame = ttk.Frame(parent)
        self.strategy = None
        self.strategy_thread = None
        self.running = False
        self.logger = get_logger(self.__class__.__name__)
        
        self.create_widgets()
        self.update_display_timer()
    
    def create_widgets(self):
        """创建控件 - 子类需要实现"""
        pass
    
    def start_strategy(self):
        """启动策略 - 子类需要实现"""
        pass
    
    def stop_strategy(self):
        """停止策略"""
        if self.strategy and hasattr(self.strategy, 'stop'):
            self.strategy.stop()
            self.running = False
            if self.strategy_thread and self.strategy_thread.is_alive():
                self.strategy_thread.join(timeout=5)
        
        self.update_buttons()
    
    def update_buttons(self):
        """更新按钮状态"""
        if hasattr(self, 'start_btn') and hasattr(self, 'stop_btn'):
            if self.running:
                self.start_btn.config(state=tk.DISABLED)
                self.stop_btn.config(state=tk.NORMAL)
            else:
                self.start_btn.config(state=tk.NORMAL)
                self.stop_btn.config(state=tk.DISABLED)
    
    def update_exchange_info(self):
        """更新交易所信息"""
        if hasattr(self, 'symbol_combo'):
            # 更新交易对列表
            symbols = exchange_manager.get_supported_symbols()
            self.symbol_combo['values'] = symbols[:20]  # 显示前20个常用交易对
    
    def update_display_timer(self):
        """定时更新显示"""
        def update_loop():
            while True:
                try:
                    if self.running and hasattr(self, 'update_display'):
                        self.update_display()
                    time.sleep(5)  # 每5秒更新一次
                except:
                    break
        
        timer_thread = threading.Thread(target=update_loop)
        timer_thread.daemon = True
        timer_thread.start()


class GridTradingTab(BaseStrategyTab):
    """网格交易策略标签页"""
    
    def create_widgets(self):
        """创建网格交易界面"""
        # 主框架
        main_frame = ttk.Frame(self.frame)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 参数设置框架
        params_frame = ttk.LabelFrame(main_frame, text="网格交易策略参数配置")
        params_frame.pack(fill=tk.X, pady=5)

        # 添加策略说明
        info_frame = ttk.Frame(params_frame)
        info_frame.pack(fill=tk.X, padx=10, pady=5)

        info_text = """
📊 网格交易策略说明：
网格交易是一种适合震荡市场的自动化交易策略。系统会在设定的价格区间内自动设置多个买入和卖出订单，
通过价格波动实现"低买高卖"，获取价差收益。适合有一定波动但总体趋势相对稳定的市场环境。
        """.strip()

        ttk.Label(info_frame, text=info_text, wraplength=600, justify=tk.LEFT,
                 foreground="blue", font=("Arial", 9)).pack(anchor=tk.W)

        # 创建用户友好输入组件
        inputs_frame = ttk.Frame(params_frame)
        inputs_frame.pack(fill=tk.X, padx=10, pady=10)

        # 交易对选择（保持原有的下拉框）
        symbol_frame = ttk.Frame(inputs_frame)
        symbol_frame.pack(fill=tk.X, pady=5)

        ttk.Label(symbol_frame, text="交易对:", width=12).pack(side=tk.LEFT, padx=(0, 5))
        self.symbol_var = tk.StringVar(value="CFX/USDT")
        self.symbol_combo = ttk.Combobox(symbol_frame, textvariable=self.symbol_var, width=15,
                                        values=["CFX/USDT", "CFX-USDT-SWAP", "BTC/USDT", "ETH/USDT",
                                               "BNB/USDT", "ADA/USDT"])
        self.symbol_combo.pack(side=tk.LEFT, padx=(0, 5))

        # 添加交易对说明
        symbol_tip = ttk.Label(symbol_frame, text="💡 支持现货和合约格式，如 CFX/USDT 或 CFX-USDT-SWAP",
                              foreground="blue", font=("Arial", 8))
        symbol_tip.pack(side=tk.LEFT, padx=(10, 0))

        # 基准价格输入
        self.base_price_input = create_user_friendly_input(inputs_frame, "grid_base_price")
        self.base_price_input.pack(fill=tk.X, pady=5)
        self.base_price_input.set_value("0.21375")  # CFX默认价格

        # 网格间距输入
        self.grid_spacing_input = create_user_friendly_input(inputs_frame, "grid_spacing")
        self.grid_spacing_input.pack(fill=tk.X, pady=5)

        # 网格数量输入
        self.grid_count_input = create_user_friendly_input(inputs_frame, "grid_count")
        self.grid_count_input.pack(fill=tk.X, pady=5)

        # 交易数量输入
        self.order_amount_input = create_user_friendly_input(inputs_frame, "grid_order_amount")
        self.order_amount_input.pack(fill=tk.X, pady=5)
        self.order_amount_input.set_value("100")  # CFX默认数量
        
        # 控制按钮
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=5)
        
        self.start_btn = ttk.Button(control_frame, text="启动策略", command=self.start_strategy)
        self.start_btn.pack(side=tk.LEFT, padx=5)
        
        self.stop_btn = ttk.Button(control_frame, text="停止策略", command=self.stop_strategy, state=tk.DISABLED)
        self.stop_btn.pack(side=tk.LEFT, padx=5)
        
        ttk.Button(control_frame, text="保存配置", command=self.save_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="加载配置", command=self.load_config).pack(side=tk.LEFT, padx=5)
        
        # 状态显示框架
        status_frame = ttk.LabelFrame(main_frame, text="运行状态")
        status_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # 创建状态显示区域
        self.status_text = tk.Text(status_frame, height=10, state=tk.DISABLED)
        scrollbar = ttk.Scrollbar(status_frame, orient=tk.VERTICAL, command=self.status_text.yview)
        self.status_text.configure(yscrollcommand=scrollbar.set)
        
        self.status_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 订单列表
        orders_frame = ttk.LabelFrame(main_frame, text="当前订单")
        orders_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # 创建订单表格
        columns = ('价格', '类型', '数量', '状态')
        self.orders_tree = ttk.Treeview(orders_frame, columns=columns, show='headings', height=6)
        
        for col in columns:
            self.orders_tree.heading(col, text=col)
            self.orders_tree.column(col, width=100)
        
        orders_scrollbar = ttk.Scrollbar(orders_frame, orient=tk.VERTICAL, command=self.orders_tree.yview)
        self.orders_tree.configure(yscrollcommand=orders_scrollbar.set)
        
        self.orders_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        orders_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def start_strategy(self):
        """启动网格交易策略"""
        try:
            exchange = exchange_manager.get_exchange()
            if not exchange:
                messagebox.showerror("错误", "请先连接交易所")
                return
            
            # 验证所有输入参数
            if not self._validate_all_inputs():
                return

            # 获取参数
            symbol = self.symbol_var.get()
            base_price = float(self.base_price_input.get_value())
            grid_spacing = float(self.grid_spacing_input.get_value())
            grid_count = int(self.grid_count_input.get_value())
            order_amount = float(self.order_amount_input.get_value())

            # 显示参数确认对话框
            if not self._confirm_strategy_parameters(symbol, base_price, grid_spacing, grid_count, order_amount):
                return
            
            # 创建策略实例
            self.strategy = GridTrading(
                exchange=exchange,
                symbol=symbol,
                base_price=base_price,
                grid_spacing=grid_spacing,
                grid_count=grid_count,
                order_amount=order_amount
            )
            
            # 启动策略线程
            self.strategy_thread = threading.Thread(target=self.strategy.run)
            self.strategy_thread.daemon = True
            self.strategy_thread.start()
            
            self.running = True
            self.update_buttons()
            
            # 保存到主应用的策略字典
            self.main_app.strategies['grid_trading'] = self.strategy
            self.main_app.strategy_threads['grid_trading'] = self.strategy_thread
            
            self.add_status_message("网格交易策略已启动")
            
        except Exception as e:
            messagebox.showerror("错误", f"启动策略失败: {str(e)}")

    def _validate_all_inputs(self):
        """验证所有输入参数"""
        validation_errors = []

        # 验证交易对
        symbol = self.symbol_var.get().strip()
        if not symbol:
            validation_errors.append("请选择交易对")

        # 验证各个输入组件
        inputs = [
            ("基准价格", self.base_price_input),
            ("网格间距", self.grid_spacing_input),
            ("网格数量", self.grid_count_input),
            ("交易数量", self.order_amount_input)
        ]

        for name, input_widget in inputs:
            if not input_widget.is_value_valid():
                validation_errors.append(f"{name}: {input_widget.get_validation_message()}")

        # 额外的业务逻辑验证
        try:
            grid_spacing = float(self.grid_spacing_input.get_value())
            grid_count = int(self.grid_count_input.get_value())

            if grid_spacing <= 0:
                validation_errors.append("网格间距必须大于0")
            if grid_count <= 1:
                validation_errors.append("网格数量必须大于1")

        except ValueError:
            pass  # 已经在单独验证中处理

        if validation_errors:
            error_message = "参数验证失败：\n\n" + "\n".join([f"• {error}" for error in validation_errors])
            messagebox.showerror("参数验证失败", error_message)
            return False

        return True

    def _confirm_strategy_parameters(self, symbol, base_price, grid_spacing, grid_count, order_amount):
        """确认策略参数"""
        # 计算预估资金需求
        total_grids = grid_count * 2  # 上下各grid_count个
        estimated_funds = base_price * order_amount * grid_count  # 粗略估算

        confirm_message = f"""
🔍 请确认网格交易策略参数：

📊 交易对: {symbol}
💰 基准价格: {base_price}
📏 网格间距: {grid_spacing}%
🔢 网格数量: {grid_count} (总共约 {total_grids} 个订单)
💱 每次交易: {order_amount}
💵 预估资金需求: {estimated_funds:.2f} USDT

⚠️ 风险提示：
• 网格交易适合震荡市场，单边趋势可能造成损失
• 请确保有足够资金支持所有网格订单
• 建议先在模拟模式下测试策略

是否确认启动策略？
        """.strip()

        return messagebox.askyesno("确认策略参数", confirm_message)

    def save_config(self):
        """保存配置"""
        config = {
            'symbol': self.symbol_var.get(),
            'base_price': self.base_price_input.get_value(),
            'grid_spacing': self.grid_spacing_input.get_value(),
            'grid_count': self.grid_count_input.get_value(),
            'order_amount': self.order_amount_input.get_value()
        }
        
        self.main_app.config_manager.save_strategy_config('grid_trading', config)
        messagebox.showinfo("成功", "配置已保存")
    
    def load_config(self):
        """加载配置"""
        config = self.main_app.config_manager.get_strategy_config('grid_trading')
        if config:
            self.symbol_var.set(config.get('symbol', 'CFX/USDT'))
            self.base_price_input.set_value(config.get('base_price', '0.21375'))
            self.grid_spacing_input.set_value(config.get('grid_spacing', '1.0'))
            self.grid_count_input.set_value(config.get('grid_count', '10'))
            self.order_amount_input.set_value(config.get('order_amount', '100'))
            messagebox.showinfo("成功", "配置已加载")
        else:
            messagebox.showinfo("提示", "没有找到保存的配置")
    
    def add_status_message(self, message):
        """添加状态消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        full_message = f"[{timestamp}] {message}\n"
        
        self.status_text.config(state=tk.NORMAL)
        self.status_text.insert(tk.END, full_message)
        self.status_text.see(tk.END)
        self.status_text.config(state=tk.DISABLED)
    
    def update_display(self):
        """更新显示"""
        if self.strategy and hasattr(self.strategy, 'orders'):
            # 更新订单列表
            self.orders_tree.delete(*self.orders_tree.get_children())
            
            for price, order_info in self.strategy.orders.items():
                self.orders_tree.insert('', tk.END, values=(
                    f"{price:.2f}",
                    order_info['type'],
                    f"{order_info.get('amount', 0):.6f}",
                    "活跃"
                ))
        
        # 更新当前价格
        if self.running:
            try:
                current_price = exchange_manager.get_ticker(self.symbol_var.get()).get('last', 0)
                if current_price > 0:
                    self.add_status_message(f"当前价格: {current_price:.6f}")
            except Exception as e:
                # 如果exchange_manager获取失败，尝试从策略实例获取
                if self.strategy and hasattr(self.strategy, 'get_current_price'):
                    try:
                        current_price = self.strategy.get_current_price()
                        if current_price > 0:
                            self.add_status_message(f"当前价格: {current_price:.6f}")
                    except:
                        pass


class MovingAverageTab(BaseStrategyTab):
    """移动平均线策略标签页"""

    def create_widgets(self):
        """创建移动平均线策略界面"""
        # 主框架
        main_frame = ttk.Frame(self.frame)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 参数设置框架
        params_frame = ttk.LabelFrame(main_frame, text="策略参数")
        params_frame.pack(fill=tk.X, pady=5)

        # 交易对选择
        ttk.Label(params_frame, text="交易对:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.symbol_var = tk.StringVar(value="BTC/USDT")
        self.symbol_combo = ttk.Combobox(params_frame, textvariable=self.symbol_var, width=15)
        self.symbol_combo.grid(row=0, column=1, padx=5, pady=5)

        # 短期均线周期
        ttk.Label(params_frame, text="短期均线:").grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)
        self.short_window_var = tk.StringVar(value="10")
        ttk.Entry(params_frame, textvariable=self.short_window_var, width=10).grid(row=0, column=3, padx=5, pady=5)

        # 长期均线周期
        ttk.Label(params_frame, text="长期均线:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.long_window_var = tk.StringVar(value="30")
        ttk.Entry(params_frame, textvariable=self.long_window_var, width=10).grid(row=1, column=1, padx=5, pady=5)

        # 时间框架
        ttk.Label(params_frame, text="时间框架:").grid(row=1, column=2, sticky=tk.W, padx=5, pady=5)
        self.timeframe_var = tk.StringVar(value="1h")
        timeframe_combo = ttk.Combobox(params_frame, textvariable=self.timeframe_var,
                                      values=["1m", "5m", "15m", "1h", "4h", "1d"], width=10)
        timeframe_combo.grid(row=1, column=3, padx=5, pady=5)

        # 控制按钮
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=5)

        self.start_btn = ttk.Button(control_frame, text="启动策略", command=self.start_strategy)
        self.start_btn.pack(side=tk.LEFT, padx=5)

        self.stop_btn = ttk.Button(control_frame, text="停止策略", command=self.stop_strategy, state=tk.DISABLED)
        self.stop_btn.pack(side=tk.LEFT, padx=5)

        ttk.Button(control_frame, text="回测", command=self.run_backtest).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="保存配置", command=self.save_config).pack(side=tk.LEFT, padx=5)

        # 状态显示
        status_frame = ttk.LabelFrame(main_frame, text="运行状态")
        status_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        self.status_text = tk.Text(status_frame, height=15, state=tk.DISABLED)
        scrollbar = ttk.Scrollbar(status_frame, orient=tk.VERTICAL, command=self.status_text.yview)
        self.status_text.configure(yscrollcommand=scrollbar.set)

        self.status_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def start_strategy(self):
        """启动移动平均线策略"""
        try:
            exchange = exchange_manager.get_exchange()
            if not exchange:
                messagebox.showerror("错误", "请先连接交易所")
                return

            # 获取参数
            symbol = self.symbol_var.get()
            short_window = int(self.short_window_var.get())
            long_window = int(self.long_window_var.get())

            # 创建策略实例
            self.strategy = MovingAverageStrategy(
                exchange=exchange,
                symbol=symbol,
                short_window=short_window,
                long_window=long_window
            )

            # 启动策略线程
            self.strategy_thread = threading.Thread(target=self.strategy.run_live)
            self.strategy_thread.daemon = True
            self.strategy_thread.start()

            self.running = True
            self.update_buttons()

            # 保存到主应用
            self.main_app.strategies['moving_average'] = self.strategy
            self.main_app.strategy_threads['moving_average'] = self.strategy_thread

            self.add_status_message("移动平均线策略已启动")

        except Exception as e:
            messagebox.showerror("错误", f"启动策略失败: {str(e)}")

    def run_backtest(self):
        """运行回测"""
        try:
            exchange = exchange_manager.get_exchange()
            if not exchange:
                messagebox.showerror("错误", "请先连接交易所")
                return

            symbol = self.symbol_var.get()
            short_window = int(self.short_window_var.get())
            long_window = int(self.long_window_var.get())

            # 创建临时策略实例进行回测
            temp_strategy = MovingAverageStrategy(
                exchange=exchange,
                symbol=symbol,
                short_window=short_window,
                long_window=long_window
            )

            self.add_status_message("开始回测...")

            # 在新线程中运行回测
            def backtest_thread():
                try:
                    result = temp_strategy.run_backtest(days=30)
                    if result is not None:
                        self.add_status_message("回测完成，请查看日志文件获取详细结果")
                    else:
                        self.add_status_message("回测失败，无法获取历史数据")
                except Exception as e:
                    self.add_status_message(f"回测错误: {str(e)}")

            threading.Thread(target=backtest_thread, daemon=True).start()

        except Exception as e:
            messagebox.showerror("错误", f"回测失败: {str(e)}")

    def save_config(self):
        """保存配置"""
        config = {
            'symbol': self.symbol_var.get(),
            'short_window': self.short_window_var.get(),
            'long_window': self.long_window_var.get(),
            'timeframe': self.timeframe_var.get()
        }

        self.main_app.config_manager.save_strategy_config('moving_average', config)
        messagebox.showinfo("成功", "配置已保存")

    def add_status_message(self, message):
        """添加状态消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        full_message = f"[{timestamp}] {message}\n"

        self.status_text.config(state=tk.NORMAL)
        self.status_text.insert(tk.END, full_message)
        self.status_text.see(tk.END)
        self.status_text.config(state=tk.DISABLED)


class RSIStrategyTab(BaseStrategyTab):
    """RSI反转策略标签页"""

    def create_widgets(self):
        """创建RSI策略界面"""
        # 主框架
        main_frame = ttk.Frame(self.frame)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 参数设置框架
        params_frame = ttk.LabelFrame(main_frame, text="策略参数")
        params_frame.pack(fill=tk.X, pady=5)

        # 交易对选择
        ttk.Label(params_frame, text="交易对:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.symbol_var = tk.StringVar(value="ETH/USDT")
        self.symbol_combo = ttk.Combobox(params_frame, textvariable=self.symbol_var, width=15)
        self.symbol_combo.grid(row=0, column=1, padx=5, pady=5)

        # RSI周期
        ttk.Label(params_frame, text="RSI周期:").grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)
        self.rsi_period_var = tk.StringVar(value="14")
        ttk.Entry(params_frame, textvariable=self.rsi_period_var, width=10).grid(row=0, column=3, padx=5, pady=5)

        # 超卖阈值
        ttk.Label(params_frame, text="超卖阈值:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.oversold_var = tk.StringVar(value="30")
        ttk.Entry(params_frame, textvariable=self.oversold_var, width=10).grid(row=1, column=1, padx=5, pady=5)

        # 超买阈值
        ttk.Label(params_frame, text="超买阈值:").grid(row=1, column=2, sticky=tk.W, padx=5, pady=5)
        self.overbought_var = tk.StringVar(value="70")
        ttk.Entry(params_frame, textvariable=self.overbought_var, width=10).grid(row=1, column=3, padx=5, pady=5)

        # 控制按钮
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=5)

        self.start_btn = ttk.Button(control_frame, text="启动策略", command=self.start_strategy)
        self.start_btn.pack(side=tk.LEFT, padx=5)

        self.stop_btn = ttk.Button(control_frame, text="停止策略", command=self.stop_strategy, state=tk.DISABLED)
        self.stop_btn.pack(side=tk.LEFT, padx=5)

        ttk.Button(control_frame, text="回测", command=self.run_backtest).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="保存配置", command=self.save_config).pack(side=tk.LEFT, padx=5)

        # 状态显示
        status_frame = ttk.LabelFrame(main_frame, text="运行状态与RSI指标")
        status_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        self.status_text = tk.Text(status_frame, height=15, state=tk.DISABLED)
        scrollbar = ttk.Scrollbar(status_frame, orient=tk.VERTICAL, command=self.status_text.yview)
        self.status_text.configure(yscrollcommand=scrollbar.set)

        self.status_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def start_strategy(self):
        """启动RSI策略"""
        try:
            exchange = exchange_manager.get_exchange()
            if not exchange:
                messagebox.showerror("错误", "请先连接交易所")
                return

            # 获取参数
            symbol = self.symbol_var.get()
            rsi_period = int(self.rsi_period_var.get())
            oversold_threshold = float(self.oversold_var.get())
            overbought_threshold = float(self.overbought_var.get())

            # 创建策略实例
            self.strategy = RSIStrategy(
                exchange=exchange,
                symbol=symbol,
                rsi_period=rsi_period,
                oversold_threshold=oversold_threshold,
                overbought_threshold=overbought_threshold
            )

            # 启动策略线程
            self.strategy_thread = threading.Thread(target=self.strategy.run_live)
            self.strategy_thread.daemon = True
            self.strategy_thread.start()

            self.running = True
            self.update_buttons()

            # 保存到主应用
            self.main_app.strategies['rsi_strategy'] = self.strategy
            self.main_app.strategy_threads['rsi_strategy'] = self.strategy_thread

            self.add_status_message("RSI反转策略已启动")

        except Exception as e:
            messagebox.showerror("错误", f"启动策略失败: {str(e)}")

    def run_backtest(self):
        """运行回测"""
        try:
            exchange = exchange_manager.get_exchange()
            if not exchange:
                messagebox.showerror("错误", "请先连接交易所")
                return

            symbol = self.symbol_var.get()
            rsi_period = int(self.rsi_period_var.get())
            oversold_threshold = float(self.oversold_var.get())
            overbought_threshold = float(self.overbought_var.get())

            # 创建临时策略实例进行回测
            temp_strategy = RSIStrategy(
                exchange=exchange,
                symbol=symbol,
                rsi_period=rsi_period,
                oversold_threshold=oversold_threshold,
                overbought_threshold=overbought_threshold
            )

            self.add_status_message("开始RSI策略回测...")

            # 在新线程中运行回测
            def backtest_thread():
                try:
                    result = temp_strategy.run_backtest(days=60)
                    if result:
                        self.add_status_message(f"回测完成，共执行{len(result)}次交易")
                    else:
                        self.add_status_message("回测失败，无法获取历史数据")
                except Exception as e:
                    self.add_status_message(f"回测错误: {str(e)}")

            threading.Thread(target=backtest_thread, daemon=True).start()

        except Exception as e:
            messagebox.showerror("错误", f"回测失败: {str(e)}")

    def save_config(self):
        """保存配置"""
        config = {
            'symbol': self.symbol_var.get(),
            'rsi_period': self.rsi_period_var.get(),
            'oversold_threshold': self.oversold_var.get(),
            'overbought_threshold': self.overbought_var.get()
        }

        self.main_app.config_manager.save_strategy_config('rsi_strategy', config)
        messagebox.showinfo("成功", "配置已保存")

    def add_status_message(self, message):
        """添加状态消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        full_message = f"[{timestamp}] {message}\n"

        self.status_text.config(state=tk.NORMAL)
        self.status_text.insert(tk.END, full_message)
        self.status_text.see(tk.END)
        self.status_text.config(state=tk.DISABLED)

    def update_display(self):
        """更新RSI显示"""
        if self.running and self.strategy:
            try:
                # 获取当前RSI值
                df = self.strategy.get_market_data(timeframe='1h', limit=20)
                if df is not None and not df.empty:
                    current_rsi = df['rsi'].iloc[-1]
                    current_price = df['close'].iloc[-1]

                    if not pd.isna(current_rsi):
                        status = "正常"
                        if current_rsi < self.strategy.oversold_threshold:
                            status = "超卖"
                        elif current_rsi > self.strategy.overbought_threshold:
                            status = "超买"

                        self.add_status_message(f"价格: {current_price:.6f}, RSI: {current_rsi:.2f} ({status})")
            except Exception as e:
                # 如果策略数据获取失败，尝试直接获取价格
                try:
                    current_price = exchange_manager.get_ticker(self.symbol_var.get()).get('last', 0)
                    if current_price > 0:
                        self.add_status_message(f"当前价格: {current_price:.6f} (RSI计算中...)")
                except:
                    pass


# 简化的其他策略标签页
class VolumeBreakoutTab(BaseStrategyTab):
    """成交量突破策略标签页"""

    def create_widgets(self):
        """创建成交量突破策略界面"""
        # 主框架
        main_frame = ttk.Frame(self.frame)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 参数设置框架
        params_frame = ttk.LabelFrame(main_frame, text="策略参数")
        params_frame.pack(fill=tk.X, pady=5)

        # 基本参数
        ttk.Label(params_frame, text="交易对:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.symbol_var = tk.StringVar(value="ADA/USDT")
        self.symbol_combo = ttk.Combobox(params_frame, textvariable=self.symbol_var, width=15)
        self.symbol_combo.grid(row=0, column=1, padx=5, pady=5)

        ttk.Label(params_frame, text="回看周期:").grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)
        self.lookback_var = tk.StringVar(value="20")
        ttk.Entry(params_frame, textvariable=self.lookback_var, width=10).grid(row=0, column=3, padx=5, pady=5)

        ttk.Label(params_frame, text="成交量倍数:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.volume_mult_var = tk.StringVar(value="2.0")
        ttk.Entry(params_frame, textvariable=self.volume_mult_var, width=10).grid(row=1, column=1, padx=5, pady=5)

        ttk.Label(params_frame, text="突破阈值(%):").grid(row=1, column=2, sticky=tk.W, padx=5, pady=5)
        self.breakout_var = tk.StringVar(value="2.0")
        ttk.Entry(params_frame, textvariable=self.breakout_var, width=10).grid(row=1, column=3, padx=5, pady=5)

        # 控制按钮
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=5)

        self.start_btn = ttk.Button(control_frame, text="启动策略", command=self.start_strategy)
        self.start_btn.pack(side=tk.LEFT, padx=5)

        self.stop_btn = ttk.Button(control_frame, text="停止策略", command=self.stop_strategy, state=tk.DISABLED)
        self.stop_btn.pack(side=tk.LEFT, padx=5)

        # 状态显示
        status_frame = ttk.LabelFrame(main_frame, text="运行状态")
        status_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        self.status_text = tk.Text(status_frame, height=15, state=tk.DISABLED)
        scrollbar = ttk.Scrollbar(status_frame, orient=tk.VERTICAL, command=self.status_text.yview)
        self.status_text.configure(yscrollcommand=scrollbar.set)

        self.status_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def start_strategy(self):
        """启动成交量突破策略"""
        self.add_status_message("成交量突破策略功能开发中...")

    def add_status_message(self, message):
        """添加状态消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        full_message = f"[{timestamp}] {message}\n"

        self.status_text.config(state=tk.NORMAL)
        self.status_text.insert(tk.END, full_message)
        self.status_text.see(tk.END)
        self.status_text.config(state=tk.DISABLED)


class SmartGridTab(BaseStrategyTab):
    """智能网格策略标签页"""

    def create_widgets(self):
        """创建智能网格策略界面"""
        # 主框架
        main_frame = ttk.Frame(self.frame)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 参数设置框架
        params_frame = ttk.LabelFrame(main_frame, text="策略参数")
        params_frame.pack(fill=tk.X, pady=5)

        # 基本参数
        ttk.Label(params_frame, text="交易对:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.symbol_var = tk.StringVar(value="ETH/USDT")
        self.symbol_combo = ttk.Combobox(params_frame, textvariable=self.symbol_var, width=15)
        self.symbol_combo.grid(row=0, column=1, padx=5, pady=5)

        ttk.Label(params_frame, text="初始投资:").grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)
        self.investment_var = tk.StringVar(value="1000")
        ttk.Entry(params_frame, textvariable=self.investment_var, width=10).grid(row=0, column=3, padx=5, pady=5)

        # 控制按钮
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=5)

        self.start_btn = ttk.Button(control_frame, text="启动策略", command=self.start_strategy)
        self.start_btn.pack(side=tk.LEFT, padx=5)

        self.stop_btn = ttk.Button(control_frame, text="停止策略", command=self.stop_strategy, state=tk.DISABLED)
        self.stop_btn.pack(side=tk.LEFT, padx=5)

        # 状态显示
        status_frame = ttk.LabelFrame(main_frame, text="运行状态")
        status_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        self.status_text = tk.Text(status_frame, height=15, state=tk.DISABLED)
        scrollbar = ttk.Scrollbar(status_frame, orient=tk.VERTICAL, command=self.status_text.yview)
        self.status_text.configure(yscrollcommand=scrollbar.set)

        self.status_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def start_strategy(self):
        """启动智能网格策略"""
        self.add_status_message("智能网格策略功能开发中...")

    def add_status_message(self, message):
        """添加状态消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        full_message = f"[{timestamp}] {message}\n"

        self.status_text.config(state=tk.NORMAL)
        self.status_text.insert(tk.END, full_message)
        self.status_text.see(tk.END)
        self.status_text.config(state=tk.DISABLED)


class RiskManagementTab(BaseStrategyTab):
    """风险管理与监控标签页"""

    def create_widgets(self):
        """创建风险管理界面"""
        # 主框架
        main_frame = ttk.Frame(self.frame)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 风险参数设置
        risk_frame = ttk.LabelFrame(main_frame, text="风险控制参数")
        risk_frame.pack(fill=tk.X, pady=5)

        ttk.Label(risk_frame, text="最大日亏损(%):").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.max_daily_loss_var = tk.StringVar(value="2.0")
        ttk.Entry(risk_frame, textvariable=self.max_daily_loss_var, width=10).grid(row=0, column=1, padx=5, pady=5)

        ttk.Label(risk_frame, text="最大仓位(%):").grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)
        self.max_position_var = tk.StringVar(value="10.0")
        ttk.Entry(risk_frame, textvariable=self.max_position_var, width=10).grid(row=0, column=3, padx=5, pady=5)

        ttk.Label(risk_frame, text="最大回撤(%):").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.max_drawdown_var = tk.StringVar(value="15.0")
        ttk.Entry(risk_frame, textvariable=self.max_drawdown_var, width=10).grid(row=1, column=1, padx=5, pady=5)

        # 控制按钮
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=5)

        ttk.Button(control_frame, text="启动监控", command=self.start_monitoring).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="停止监控", command=self.stop_monitoring).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="查看报告", command=self.show_report).pack(side=tk.LEFT, padx=5)

        # 监控显示
        monitor_frame = ttk.LabelFrame(main_frame, text="实时监控")
        monitor_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        self.monitor_text = tk.Text(monitor_frame, height=15, state=tk.DISABLED)
        scrollbar = ttk.Scrollbar(monitor_frame, orient=tk.VERTICAL, command=self.monitor_text.yview)
        self.monitor_text.configure(yscrollcommand=scrollbar.set)

        self.monitor_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def start_monitoring(self):
        """启动风险监控"""
        if self.main_app.risk_manager:
            self.main_app.risk_manager.start_monitoring()
            self.add_monitor_message("风险监控已启动")
        else:
            messagebox.showwarning("警告", "请先连接交易所")

    def stop_monitoring(self):
        """停止风险监控"""
        if self.main_app.risk_manager:
            self.main_app.risk_manager.stop_monitoring()
            self.add_monitor_message("风险监控已停止")

    def show_report(self):
        """显示风险报告"""
        if self.main_app.risk_manager:
            metrics = self.main_app.risk_manager.get_risk_metrics()
            report = f"""
风险报告:
当前余额: ${metrics['current_balance']:.2f}
今日盈亏: {metrics['daily_pnl']:.2%}
总收益率: {metrics['total_return']:.2%}
当前回撤: {metrics['drawdown']:.2%}
峰值余额: ${metrics['peak_balance']:.2f}
            """
            self.add_monitor_message(report)
        else:
            messagebox.showwarning("警告", "请先连接交易所")

    def add_monitor_message(self, message):
        """添加监控消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        full_message = f"[{timestamp}] {message}\n"

        self.monitor_text.config(state=tk.NORMAL)
        self.monitor_text.insert(tk.END, full_message)
        self.monitor_text.see(tk.END)
        self.monitor_text.config(state=tk.DISABLED)


class AwesomeOscillatorTab(BaseStrategyTab):
    """AO指标策略标签页"""

    def create_widgets(self):
        """创建AO策略界面"""
        # 主框架
        main_frame = ttk.Frame(self.frame)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 策略说明
        info_frame = ttk.LabelFrame(main_frame, text="🌊 AO指标策略说明")
        info_frame.pack(fill=tk.X, pady=5)

        info_text = """
📊 AO指标策略 (Awesome Oscillator Strategy)

核心原理：AO = SMA(中价, 5) - SMA(中价, 34)
• 中价 = (最高价 + 最低价) / 2，比收盘价更能反映真实波动
• 5日均线代表短期动能，34日均线代表长期动能
• 差值反映动量的加速或减速

信号类型：
🎯 零轴穿越：柱状图由负转正→金叉买入，由正转负→死叉卖出
🍽️ 碟型信号：零轴上方绿柱后接红柱再转绿→买入信号
⛰️ 双峰信号：零轴下方第二个低点高于第一个→底部反转
📈 背离信号：价格与AO指标方向相反→趋势反转预警

超前性优势：比MACD平均提前1-2根K线发出信号，适合趋势识别和震荡市场买卖点捕捉。
        """.strip()

        ttk.Label(info_frame, text=info_text, wraplength=700, justify=tk.LEFT,
                 foreground="blue", font=("Arial", 9)).pack(padx=10, pady=5)

        # 参数设置框架
        params_frame = ttk.LabelFrame(main_frame, text="策略参数配置")
        params_frame.pack(fill=tk.X, pady=5)

        # 交易对选择
        symbol_frame = ttk.Frame(params_frame)
        symbol_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(symbol_frame, text="交易对:", width=12).pack(side=tk.LEFT)
        self.symbol_var = tk.StringVar(value="CFX/USDT")
        self.symbol_combo = ttk.Combobox(symbol_frame, textvariable=self.symbol_var, width=15,
                                        values=["CFX/USDT", "CFX-USDT-SWAP", "BTC/USDT", "ETH/USDT"])
        self.symbol_combo.pack(side=tk.LEFT, padx=(10, 0))

        # 使用用户友好输入组件
        try:
            from user_friendly_input import create_user_friendly_input

            # 短期周期
            self.short_period_input = create_user_friendly_input(params_frame, "ao_short_period")
            self.short_period_input.pack(fill=tk.X, pady=2, padx=10)
            self.short_period_input.set_value("5")

            # 长期周期
            self.long_period_input = create_user_friendly_input(params_frame, "ao_long_period")
            self.long_period_input.pack(fill=tk.X, pady=2, padx=10)
            self.long_period_input.set_value("34")

            # 交易数量
            self.amount_input = create_user_friendly_input(params_frame, "ao_amount")
            self.amount_input.pack(fill=tk.X, pady=2, padx=10)
            self.amount_input.set_value("100")

            # 止损百分比
            self.stop_loss_input = create_user_friendly_input(params_frame, "ao_stop_loss")
            self.stop_loss_input.pack(fill=tk.X, pady=2, padx=10)
            self.stop_loss_input.set_value("3")

            # 止盈百分比
            self.take_profit_input = create_user_friendly_input(params_frame, "ao_take_profit")
            self.take_profit_input.pack(fill=tk.X, pady=2, padx=10)
            self.take_profit_input.set_value("6")

        except ImportError:
            # 备用简单输入框
            self._create_simple_inputs(params_frame)

        # 控制按钮
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=5)

        self.start_btn = ttk.Button(control_frame, text="启动策略", command=self.start_strategy)
        self.start_btn.pack(side=tk.LEFT, padx=5)

        self.stop_btn = ttk.Button(control_frame, text="停止策略", command=self.stop_strategy, state=tk.DISABLED)
        self.stop_btn.pack(side=tk.LEFT, padx=5)

        ttk.Button(control_frame, text="回测策略", command=self.run_backtest).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="保存配置", command=self.save_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="加载配置", command=self.load_config).pack(side=tk.LEFT, padx=5)

        # 状态显示区域
        status_frame = ttk.LabelFrame(main_frame, text="策略状态与AO指标")
        status_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        self.status_text = tk.Text(status_frame, height=12, state=tk.DISABLED)
        scrollbar = ttk.Scrollbar(status_frame, orient=tk.VERTICAL, command=self.status_text.yview)
        self.status_text.configure(yscrollcommand=scrollbar.set)

        self.status_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        self.add_status_message("AO指标策略已初始化，等待启动...")

    def _create_simple_inputs(self, parent):
        """创建简单输入框（备用方案）"""
        # 短期周期
        short_frame = ttk.Frame(parent)
        short_frame.pack(fill=tk.X, padx=10, pady=2)
        ttk.Label(short_frame, text="短期周期:").pack(side=tk.LEFT)
        self.short_period_var = tk.StringVar(value="5")
        ttk.Entry(short_frame, textvariable=self.short_period_var, width=10).pack(side=tk.LEFT, padx=(10, 0))

        # 长期周期
        long_frame = ttk.Frame(parent)
        long_frame.pack(fill=tk.X, padx=10, pady=2)
        ttk.Label(long_frame, text="长期周期:").pack(side=tk.LEFT)
        self.long_period_var = tk.StringVar(value="34")
        ttk.Entry(long_frame, textvariable=self.long_period_var, width=10).pack(side=tk.LEFT, padx=(10, 0))

        # 交易数量
        amount_frame = ttk.Frame(parent)
        amount_frame.pack(fill=tk.X, padx=10, pady=2)
        ttk.Label(amount_frame, text="交易数量:").pack(side=tk.LEFT)
        self.amount_var = tk.StringVar(value="100")
        ttk.Entry(amount_frame, textvariable=self.amount_var, width=10).pack(side=tk.LEFT, padx=(10, 0))

        # 止损百分比
        stop_loss_frame = ttk.Frame(parent)
        stop_loss_frame.pack(fill=tk.X, padx=10, pady=2)
        ttk.Label(stop_loss_frame, text="止损百分比(%):").pack(side=tk.LEFT)
        self.stop_loss_var = tk.StringVar(value="3")
        ttk.Entry(stop_loss_frame, textvariable=self.stop_loss_var, width=10).pack(side=tk.LEFT, padx=(10, 0))

        # 止盈百分比
        take_profit_frame = ttk.Frame(parent)
        take_profit_frame.pack(fill=tk.X, padx=10, pady=2)
        ttk.Label(take_profit_frame, text="止盈百分比(%):").pack(side=tk.LEFT)
        self.take_profit_var = tk.StringVar(value="6")
        ttk.Entry(take_profit_frame, textvariable=self.take_profit_var, width=10).pack(side=tk.LEFT, padx=(10, 0))

    def start_strategy(self):
        """启动AO指标策略"""
        try:
            exchange = exchange_manager.get_exchange()
            if not exchange:
                messagebox.showerror("错误", "请先连接交易所")
                return

            # 验证输入参数
            if not self._validate_inputs():
                return

            # 获取参数
            symbol = self.symbol_var.get()
            short_period = self._get_input_value('short_period', 5)
            long_period = self._get_input_value('long_period', 34)
            amount = self._get_input_value('amount', 100)
            stop_loss_pct = self._get_input_value('stop_loss', 3) / 100
            take_profit_pct = self._get_input_value('take_profit', 6) / 100

            # 显示参数确认
            if not self._confirm_strategy_parameters(symbol, short_period, long_period, amount,
                                                   stop_loss_pct, take_profit_pct):
                return

            # 创建策略实例
            from strategies import AwesomeOscillatorStrategy
            self.strategy = AwesomeOscillatorStrategy(
                exchange=exchange,
                symbol=symbol,
                short_period=short_period,
                long_period=long_period,
                amount=amount,
                stop_loss_pct=stop_loss_pct,
                take_profit_pct=take_profit_pct
            )

            # 启动策略线程
            self.strategy_thread = threading.Thread(target=self.strategy.run_live)
            self.strategy_thread.daemon = True
            self.strategy_thread.start()

            self.running = True
            self.update_buttons()

            # 保存到主应用
            self.main_app.strategies['awesome_oscillator'] = self.strategy
            self.main_app.strategy_threads['awesome_oscillator'] = self.strategy_thread

            self.add_status_message("AO指标策略已启动")

        except Exception as e:
            messagebox.showerror("错误", f"启动策略失败: {str(e)}")

    def _get_input_value(self, param_name, default_value):
        """获取输入值"""
        try:
            if hasattr(self, f'{param_name}_input'):
                # 使用用户友好输入组件
                input_widget = getattr(self, f'{param_name}_input')
                return float(input_widget.get_value())
            else:
                # 使用简单输入框
                var = getattr(self, f'{param_name}_var')
                return float(var.get())
        except:
            return default_value

    def _validate_inputs(self):
        """验证输入参数"""
        try:
            short_period = self._get_input_value('short_period', 5)
            long_period = self._get_input_value('long_period', 34)
            amount = self._get_input_value('amount', 100)
            stop_loss = self._get_input_value('stop_loss', 3)
            take_profit = self._get_input_value('take_profit', 6)

            errors = []

            if short_period <= 0 or short_period >= long_period:
                errors.append("短期周期必须大于0且小于长期周期")

            if long_period <= 5:
                errors.append("长期周期必须大于5")

            if amount <= 0:
                errors.append("交易数量必须大于0")

            if stop_loss <= 0 or stop_loss >= 50:
                errors.append("止损百分比必须在0-50%之间")

            if take_profit <= 0 or take_profit >= 100:
                errors.append("止盈百分比必须在0-100%之间")

            if errors:
                messagebox.showerror("参数验证失败", "\n".join(errors))
                return False

            return True

        except Exception as e:
            messagebox.showerror("参数验证失败", f"参数格式错误: {str(e)}")
            return False

    def _confirm_strategy_parameters(self, symbol, short_period, long_period, amount,
                                   stop_loss_pct, take_profit_pct):
        """确认策略参数"""
        confirm_message = f"""
🌊 请确认AO指标策略参数：

📊 交易对: {symbol}
📈 短期周期: {short_period}日
📉 长期周期: {long_period}日
💱 交易数量: {amount}
🛡️ 止损: {stop_loss_pct*100:.1f}%
🎯 止盈: {take_profit_pct*100:.1f}%

📋 策略特点：
• 基于Bill Williams的Awesome Oscillator指标
• 比MACD平均提前1-2根K线发出信号
• 适合趋势识别和震荡市场交易
• 包含零轴穿越、碟型、双峰、背离等多种信号

⚠️ 风险提示：
• AO指标在强趋势市场中可能产生假信号
• 建议结合其他指标和价格行为确认
• 请确保有足够资金支持交易
• 建议先在模拟模式下测试

是否确认启动AO指标策略？
        """.strip()

        return messagebox.askyesno("确认策略参数", confirm_message)

    def run_backtest(self):
        """运行AO策略回测"""
        try:
            exchange = exchange_manager.get_exchange()
            if not exchange:
                messagebox.showerror("错误", "请先连接交易所")
                return

            if not self._validate_inputs():
                return

            # 获取参数
            symbol = self.symbol_var.get()
            short_period = self._get_input_value('short_period', 5)
            long_period = self._get_input_value('long_period', 34)
            amount = self._get_input_value('amount', 100)
            stop_loss_pct = self._get_input_value('stop_loss', 3) / 100
            take_profit_pct = self._get_input_value('take_profit', 6) / 100

            self.add_status_message("开始AO策略回测...")

            def backtest_thread():
                try:
                    from strategies import AwesomeOscillatorStrategy
                    temp_strategy = AwesomeOscillatorStrategy(
                        exchange=exchange,
                        symbol=symbol,
                        short_period=short_period,
                        long_period=long_period,
                        amount=amount,
                        stop_loss_pct=stop_loss_pct,
                        take_profit_pct=take_profit_pct
                    )

                    # 运行回测
                    from datetime import datetime, timedelta
                    end_date = datetime.now()
                    start_date = end_date - timedelta(days=60)

                    result = temp_strategy.run_backtest(start_date.strftime('%Y-%m-%d'),
                                                       end_date.strftime('%Y-%m-%d'))

                    if result:
                        self.add_status_message(f"回测完成！")
                        self.add_status_message(f"总收益: {result['total_return']:.2f}%")
                        self.add_status_message(f"交易次数: {result['total_trades']}")
                        self.add_status_message(f"胜率: {result['win_rate']:.2f}%")
                        self.add_status_message(f"最大回撤: {result['max_drawdown']:.2f}%")
                    else:
                        self.add_status_message("回测失败，无法获取历史数据")

                except Exception as e:
                    self.add_status_message(f"回测错误: {str(e)}")

            threading.Thread(target=backtest_thread, daemon=True).start()

        except Exception as e:
            messagebox.showerror("错误", f"回测失败: {str(e)}")

    def save_config(self):
        """保存配置"""
        config = {
            'symbol': self.symbol_var.get(),
            'short_period': self._get_input_value('short_period', 5),
            'long_period': self._get_input_value('long_period', 34),
            'amount': self._get_input_value('amount', 100),
            'stop_loss': self._get_input_value('stop_loss', 3),
            'take_profit': self._get_input_value('take_profit', 6)
        }

        self.main_app.config_manager.save_strategy_config('awesome_oscillator', config)
        messagebox.showinfo("成功", "AO策略配置已保存")

    def load_config(self):
        """加载配置"""
        config = self.main_app.config_manager.get_strategy_config('awesome_oscillator')
        if config:
            self.symbol_var.set(config.get('symbol', 'CFX/USDT'))

            # 设置参数值
            if hasattr(self, 'short_period_input'):
                self.short_period_input.set_value(str(config.get('short_period', 5)))
                self.long_period_input.set_value(str(config.get('long_period', 34)))
                self.amount_input.set_value(str(config.get('amount', 100)))
                self.stop_loss_input.set_value(str(config.get('stop_loss', 3)))
                self.take_profit_input.set_value(str(config.get('take_profit', 6)))
            else:
                self.short_period_var.set(str(config.get('short_period', 5)))
                self.long_period_var.set(str(config.get('long_period', 34)))
                self.amount_var.set(str(config.get('amount', 100)))
                self.stop_loss_var.set(str(config.get('stop_loss', 3)))
                self.take_profit_var.set(str(config.get('take_profit', 6)))

            messagebox.showinfo("成功", "AO策略配置已加载")
        else:
            messagebox.showinfo("提示", "没有找到保存的AO策略配置")

    def add_status_message(self, message):
        """添加状态消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        full_message = f"[{timestamp}] {message}\n"

        self.status_text.config(state=tk.NORMAL)
        self.status_text.insert(tk.END, full_message)
        self.status_text.see(tk.END)
        self.status_text.config(state=tk.DISABLED)

    def update_display(self):
        """更新AO指标显示"""
        if self.running and self.strategy:
            try:
                status = self.strategy.get_strategy_status()
                if status:
                    position_text = "无仓位"
                    if status['position'] == 1:
                        position_text = f"多头 @ {status['entry_price']:.6f}"
                    elif status['position'] == -1:
                        position_text = f"空头 @ {status['entry_price']:.6f}"

                    latest_ao = status.get('latest_ao', 0)
                    ao_status = "零轴上方" if latest_ao > 0 else "零轴下方"

                    self.add_status_message(f"仓位: {position_text}, AO: {latest_ao:.6f} ({ao_status})")

            except Exception as e:
                # 如果策略状态获取失败，尝试获取基本价格信息
                try:
                    current_price = exchange_manager.get_ticker(self.symbol_var.get()).get('last', 0)
                    if current_price > 0:
                        self.add_status_message(f"当前价格: {current_price:.6f} (AO计算中...)")
                except:
                    pass


class FMACDTab(BaseStrategyTab):
    """FMACD策略标签页"""

    def create_widgets(self):
        """创建FMACD策略界面"""
        # 主框架
        main_frame = ttk.Frame(self.frame)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 策略说明
        info_frame = ttk.LabelFrame(main_frame, text="🚀 FMACD策略说明 (Forward-looking MACD)")
        info_frame.pack(fill=tk.X, pady=5)

        info_text = """
🧠 改进型MACD策略 - 消除滞后的前沿方法

核心技术：
① 全连接神经网络（4隐藏层+ReLU激活）分析高频/财务/技术因子
② 预测次日收盘价并与历史数据融合，重构价格序列
③ 基于重构数据计算FMACD（FDIF、FDEA），形成超前信号

技术优势：
🎯 比传统MACD提前1-3天发出信号
📈 年化收益提升约15%
🔬 结合多维因子：技术、高频、财务、宏观
🤖 机器学习驱动，动态适应市场变化

因子体系：
• 技术因子：价格动量、波动率、成交量指标
• 高频因子：分钟级价格变化、订单流指标
• 财务因子：基本面数据、市场情绪指标
• 宏观因子：市场环境、相关性指标

适用场景：中短期趋势交易、高频量化策略、多因子模型组合
        """.strip()

        ttk.Label(info_frame, text=info_text, wraplength=700, justify=tk.LEFT,
                 foreground="blue", font=("Arial", 9)).pack(padx=10, pady=5)

        # 参数设置框架
        params_frame = ttk.LabelFrame(main_frame, text="策略参数配置")
        params_frame.pack(fill=tk.X, pady=5)

        # 交易对选择
        symbol_frame = ttk.Frame(params_frame)
        symbol_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(symbol_frame, text="交易对:", width=12).pack(side=tk.LEFT)
        self.symbol_var = tk.StringVar(value="CFX/USDT")
        self.symbol_combo = ttk.Combobox(symbol_frame, textvariable=self.symbol_var, width=15,
                                        values=["CFX/USDT", "CFX-USDT-SWAP", "BTC/USDT", "ETH/USDT"])
        self.symbol_combo.pack(side=tk.LEFT, padx=(10, 0))

        # 使用用户友好输入组件
        try:
            from user_friendly_input import create_user_friendly_input

            # 快速周期
            self.fast_period_input = create_user_friendly_input(params_frame, "fmacd_fast_period")
            self.fast_period_input.pack(fill=tk.X, pady=2, padx=10)
            self.fast_period_input.set_value("12")

            # 慢速周期
            self.slow_period_input = create_user_friendly_input(params_frame, "fmacd_slow_period")
            self.slow_period_input.pack(fill=tk.X, pady=2, padx=10)
            self.slow_period_input.set_value("26")

            # 信号周期
            self.signal_period_input = create_user_friendly_input(params_frame, "fmacd_signal_period")
            self.signal_period_input.pack(fill=tk.X, pady=2, padx=10)
            self.signal_period_input.set_value("9")

            # 交易数量
            self.amount_input = create_user_friendly_input(params_frame, "fmacd_amount")
            self.amount_input.pack(fill=tk.X, pady=2, padx=10)
            self.amount_input.set_value("100")

            # 止损百分比
            self.stop_loss_input = create_user_friendly_input(params_frame, "fmacd_stop_loss")
            self.stop_loss_input.pack(fill=tk.X, pady=2, padx=10)
            self.stop_loss_input.set_value("3")

            # 止盈百分比
            self.take_profit_input = create_user_friendly_input(params_frame, "fmacd_take_profit")
            self.take_profit_input.pack(fill=tk.X, pady=2, padx=10)
            self.take_profit_input.set_value("6")

            # 模型重训练间隔
            self.retrain_days_input = create_user_friendly_input(params_frame, "fmacd_retrain_days")
            self.retrain_days_input.pack(fill=tk.X, pady=2, padx=10)
            self.retrain_days_input.set_value("30")

            # 预测置信度
            self.confidence_input = create_user_friendly_input(params_frame, "fmacd_confidence")
            self.confidence_input.pack(fill=tk.X, pady=2, padx=10)
            self.confidence_input.set_value("0.7")

        except ImportError:
            # 备用简单输入框
            self._create_simple_inputs(params_frame)

        # 控制按钮
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=5)

        self.start_btn = ttk.Button(control_frame, text="启动策略", command=self.start_strategy)
        self.start_btn.pack(side=tk.LEFT, padx=5)

        self.stop_btn = ttk.Button(control_frame, text="停止策略", command=self.stop_strategy, state=tk.DISABLED)
        self.stop_btn.pack(side=tk.LEFT, padx=5)

        ttk.Button(control_frame, text="回测策略", command=self.run_backtest).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="训练模型", command=self.train_model).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="保存配置", command=self.save_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="加载配置", command=self.load_config).pack(side=tk.LEFT, padx=5)

        # 状态显示区域
        status_frame = ttk.LabelFrame(main_frame, text="策略状态与FMACD指标")
        status_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        self.status_text = tk.Text(status_frame, height=12, state=tk.DISABLED)
        scrollbar = ttk.Scrollbar(status_frame, orient=tk.VERTICAL, command=self.status_text.yview)
        self.status_text.configure(yscrollcommand=scrollbar.set)

        self.status_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        self.add_status_message("FMACD策略已初始化，等待启动...")

    def _create_simple_inputs(self, parent):
        """创建简单输入框（备用方案）"""
        # 快速周期
        fast_frame = ttk.Frame(parent)
        fast_frame.pack(fill=tk.X, padx=10, pady=2)
        ttk.Label(fast_frame, text="快速周期:").pack(side=tk.LEFT)
        self.fast_period_var = tk.StringVar(value="12")
        ttk.Entry(fast_frame, textvariable=self.fast_period_var, width=10).pack(side=tk.LEFT, padx=(10, 0))

        # 慢速周期
        slow_frame = ttk.Frame(parent)
        slow_frame.pack(fill=tk.X, padx=10, pady=2)
        ttk.Label(slow_frame, text="慢速周期:").pack(side=tk.LEFT)
        self.slow_period_var = tk.StringVar(value="26")
        ttk.Entry(slow_frame, textvariable=self.slow_period_var, width=10).pack(side=tk.LEFT, padx=(10, 0))

        # 信号周期
        signal_frame = ttk.Frame(parent)
        signal_frame.pack(fill=tk.X, padx=10, pady=2)
        ttk.Label(signal_frame, text="信号周期:").pack(side=tk.LEFT)
        self.signal_period_var = tk.StringVar(value="9")
        ttk.Entry(signal_frame, textvariable=self.signal_period_var, width=10).pack(side=tk.LEFT, padx=(10, 0))

        # 交易数量
        amount_frame = ttk.Frame(parent)
        amount_frame.pack(fill=tk.X, padx=10, pady=2)
        ttk.Label(amount_frame, text="交易数量:").pack(side=tk.LEFT)
        self.amount_var = tk.StringVar(value="100")
        ttk.Entry(amount_frame, textvariable=self.amount_var, width=10).pack(side=tk.LEFT, padx=(10, 0))

        # 止损百分比
        stop_loss_frame = ttk.Frame(parent)
        stop_loss_frame.pack(fill=tk.X, padx=10, pady=2)
        ttk.Label(stop_loss_frame, text="止损百分比(%):").pack(side=tk.LEFT)
        self.stop_loss_var = tk.StringVar(value="3")
        ttk.Entry(stop_loss_frame, textvariable=self.stop_loss_var, width=10).pack(side=tk.LEFT, padx=(10, 0))

        # 止盈百分比
        take_profit_frame = ttk.Frame(parent)
        take_profit_frame.pack(fill=tk.X, padx=10, pady=2)
        ttk.Label(take_profit_frame, text="止盈百分比(%):").pack(side=tk.LEFT)
        self.take_profit_var = tk.StringVar(value="6")
        ttk.Entry(take_profit_frame, textvariable=self.take_profit_var, width=10).pack(side=tk.LEFT, padx=(10, 0))

        # 模型重训练间隔
        retrain_frame = ttk.Frame(parent)
        retrain_frame.pack(fill=tk.X, padx=10, pady=2)
        ttk.Label(retrain_frame, text="重训练间隔(天):").pack(side=tk.LEFT)
        self.retrain_days_var = tk.StringVar(value="30")
        ttk.Entry(retrain_frame, textvariable=self.retrain_days_var, width=10).pack(side=tk.LEFT, padx=(10, 0))

        # 预测置信度
        confidence_frame = ttk.Frame(parent)
        confidence_frame.pack(fill=tk.X, padx=10, pady=2)
        ttk.Label(confidence_frame, text="预测置信度:").pack(side=tk.LEFT)
        self.confidence_var = tk.StringVar(value="0.7")
        ttk.Entry(confidence_frame, textvariable=self.confidence_var, width=10).pack(side=tk.LEFT, padx=(10, 0))

    def start_strategy(self):
        """启动FMACD策略"""
        try:
            exchange = exchange_manager.get_exchange()
            if not exchange:
                messagebox.showerror("错误", "请先连接交易所")
                return

            # 验证输入参数
            if not self._validate_inputs():
                return

            # 获取参数
            symbol = self.symbol_var.get()
            fast_period = self._get_input_value('fast_period', 12)
            slow_period = self._get_input_value('slow_period', 26)
            signal_period = self._get_input_value('signal_period', 9)
            amount = self._get_input_value('amount', 100)
            stop_loss_pct = self._get_input_value('stop_loss', 3) / 100
            take_profit_pct = self._get_input_value('take_profit', 6) / 100
            retrain_days = int(self._get_input_value('retrain_days', 30))
            confidence = self._get_input_value('confidence', 0.7)

            # 显示参数确认
            if not self._confirm_strategy_parameters(symbol, fast_period, slow_period, signal_period,
                                                   amount, stop_loss_pct, take_profit_pct,
                                                   retrain_days, confidence):
                return

            # 创建策略实例
            from strategies import FMACDStrategy
            self.strategy = FMACDStrategy(
                exchange=exchange,
                symbol=symbol,
                fast_period=fast_period,
                slow_period=slow_period,
                signal_period=signal_period,
                amount=amount,
                stop_loss_pct=stop_loss_pct,
                take_profit_pct=take_profit_pct,
                model_retrain_days=retrain_days,
                prediction_confidence=confidence
            )

            # 启动策略线程
            self.strategy_thread = threading.Thread(target=self.strategy.run_live)
            self.strategy_thread.daemon = True
            self.strategy_thread.start()

            self.running = True
            self.update_buttons()

            # 保存到主应用
            self.main_app.strategies['fmacd'] = self.strategy
            self.main_app.strategy_threads['fmacd'] = self.strategy_thread

            self.add_status_message("FMACD策略已启动")

        except Exception as e:
            messagebox.showerror("错误", f"启动策略失败: {str(e)}")

    def _get_input_value(self, param_name, default_value):
        """获取输入值"""
        try:
            if hasattr(self, f'{param_name}_input'):
                # 使用用户友好输入组件
                input_widget = getattr(self, f'{param_name}_input')
                return float(input_widget.get_value())
            else:
                # 使用简单输入框
                var = getattr(self, f'{param_name}_var')
                return float(var.get())
        except:
            return default_value

    def _validate_inputs(self):
        """验证输入参数"""
        try:
            fast_period = self._get_input_value('fast_period', 12)
            slow_period = self._get_input_value('slow_period', 26)
            signal_period = self._get_input_value('signal_period', 9)
            amount = self._get_input_value('amount', 100)
            stop_loss = self._get_input_value('stop_loss', 3)
            take_profit = self._get_input_value('take_profit', 6)
            retrain_days = self._get_input_value('retrain_days', 30)
            confidence = self._get_input_value('confidence', 0.7)

            errors = []

            if fast_period <= 0 or fast_period >= slow_period:
                errors.append("快速周期必须大于0且小于慢速周期")

            if slow_period <= 10:
                errors.append("慢速周期必须大于10")

            if signal_period <= 0 or signal_period >= 30:
                errors.append("信号周期必须在1-30之间")

            if amount <= 0:
                errors.append("交易数量必须大于0")

            if stop_loss <= 0 or stop_loss >= 50:
                errors.append("止损百分比必须在0-50%之间")

            if take_profit <= 0 or take_profit >= 100:
                errors.append("止盈百分比必须在0-100%之间")

            if retrain_days < 7 or retrain_days > 90:
                errors.append("重训练间隔必须在7-90天之间")

            if confidence < 0.5 or confidence > 0.95:
                errors.append("预测置信度必须在0.5-0.95之间")

            if errors:
                messagebox.showerror("参数验证失败", "\n".join(errors))
                return False

            return True

        except Exception as e:
            messagebox.showerror("参数验证失败", f"参数格式错误: {str(e)}")
            return False

    def _confirm_strategy_parameters(self, symbol, fast_period, slow_period, signal_period,
                                   amount, stop_loss_pct, take_profit_pct, retrain_days, confidence):
        """确认策略参数"""
        confirm_message = f"""
🚀 请确认FMACD策略参数：

📊 交易对: {symbol}
⚡ 快速周期: {fast_period}日
🐌 慢速周期: {slow_period}日
📡 信号周期: {signal_period}日
💱 交易数量: {amount}
🛡️ 止损: {stop_loss_pct*100:.1f}%
🎯 止盈: {take_profit_pct*100:.1f}%
🔄 重训练间隔: {retrain_days}天
🎲 预测置信度: {confidence:.1f}

🧠 策略特点：
• 基于机器学习的改进型MACD策略
• 比传统MACD提前1-3天发出信号
• 年化收益提升约15%
• 结合多维因子分析和神经网络预测
• 动态适应市场变化

⚠️ 风险提示：
• 机器学习模型需要定期重训练
• 对数据质量要求较高
• 市场环境变化可能影响预测效果
• 建议先在模拟模式下测试

是否确认启动FMACD策略？
        """.strip()

        return messagebox.askyesno("确认策略参数", confirm_message)

    def train_model(self):
        """手动训练模型"""
        try:
            if not self.strategy:
                messagebox.showwarning("警告", "请先创建策略实例")
                return

            self.add_status_message("开始手动训练机器学习模型...")

            def train_thread():
                try:
                    # 获取历史数据
                    df = self.strategy.get_market_data(limit=500)
                    if df is None or len(df) < 100:
                        self.add_status_message("历史数据不足，无法训练模型")
                        return

                    # 提取特征
                    df = self.strategy.extract_features(df)

                    # 获取可用特征列
                    feature_columns = [
                        'returns', 'returns_5', 'returns_10', 'returns_20',
                        'volatility_5', 'volatility_10', 'volatility_20',
                        'high_low_ratio', 'close_position', 'price_acceleration',
                        'volume_ratio', 'order_flow', 'trend_strength',
                        'rsi', 'macd', 'macd_histogram', 'market_regime',
                        'volatility_regime', 'price_volume_corr'
                    ]

                    available_features = [col for col in feature_columns if col in df.columns]

                    if len(available_features) < 5:
                        self.add_status_message("可用特征不足，无法训练模型")
                        return

                    # 训练模型
                    self.strategy._train_ml_model(df, available_features)
                    self.add_status_message("机器学习模型训练完成")

                except Exception as e:
                    self.add_status_message(f"模型训练失败: {str(e)}")

            threading.Thread(target=train_thread, daemon=True).start()

        except Exception as e:
            messagebox.showerror("错误", f"训练模型失败: {str(e)}")

    def run_backtest(self):
        """运行FMACD策略回测"""
        try:
            exchange = exchange_manager.get_exchange()
            if not exchange:
                messagebox.showerror("错误", "请先连接交易所")
                return

            if not self._validate_inputs():
                return

            # 获取参数
            symbol = self.symbol_var.get()
            fast_period = self._get_input_value('fast_period', 12)
            slow_period = self._get_input_value('slow_period', 26)
            signal_period = self._get_input_value('signal_period', 9)
            amount = self._get_input_value('amount', 100)
            stop_loss_pct = self._get_input_value('stop_loss', 3) / 100
            take_profit_pct = self._get_input_value('take_profit', 6) / 100
            retrain_days = int(self._get_input_value('retrain_days', 30))
            confidence = self._get_input_value('confidence', 0.7)

            self.add_status_message("开始FMACD策略回测...")

            def backtest_thread():
                try:
                    from strategies import FMACDStrategy
                    temp_strategy = FMACDStrategy(
                        exchange=exchange,
                        symbol=symbol,
                        fast_period=fast_period,
                        slow_period=slow_period,
                        signal_period=signal_period,
                        amount=amount,
                        stop_loss_pct=stop_loss_pct,
                        take_profit_pct=take_profit_pct,
                        model_retrain_days=retrain_days,
                        prediction_confidence=confidence
                    )

                    # 运行回测
                    from datetime import datetime, timedelta
                    end_date = datetime.now()
                    start_date = end_date - timedelta(days=60)

                    result = temp_strategy.run_backtest(start_date.strftime('%Y-%m-%d'),
                                                       end_date.strftime('%Y-%m-%d'))

                    if result:
                        self.add_status_message(f"回测完成！")
                        self.add_status_message(f"总收益: {result['total_return']:.2f}%")
                        self.add_status_message(f"交易次数: {result['total_trades']}")
                        self.add_status_message(f"胜率: {result['win_rate']:.2f}%")
                        self.add_status_message(f"最大回撤: {result['max_drawdown']:.2f}%")
                        self.add_status_message(f"平均置信度: {result['avg_confidence']:.2f}")
                    else:
                        self.add_status_message("回测失败，无法获取历史数据")

                except Exception as e:
                    self.add_status_message(f"回测错误: {str(e)}")

            threading.Thread(target=backtest_thread, daemon=True).start()

        except Exception as e:
            messagebox.showerror("错误", f"回测失败: {str(e)}")

    def save_config(self):
        """保存配置"""
        config = {
            'symbol': self.symbol_var.get(),
            'fast_period': self._get_input_value('fast_period', 12),
            'slow_period': self._get_input_value('slow_period', 26),
            'signal_period': self._get_input_value('signal_period', 9),
            'amount': self._get_input_value('amount', 100),
            'stop_loss': self._get_input_value('stop_loss', 3),
            'take_profit': self._get_input_value('take_profit', 6),
            'retrain_days': self._get_input_value('retrain_days', 30),
            'confidence': self._get_input_value('confidence', 0.7)
        }

        self.main_app.config_manager.save_strategy_config('fmacd', config)
        messagebox.showinfo("成功", "FMACD策略配置已保存")

    def load_config(self):
        """加载配置"""
        config = self.main_app.config_manager.get_strategy_config('fmacd')
        if config:
            self.symbol_var.set(config.get('symbol', 'CFX/USDT'))

            # 设置参数值
            if hasattr(self, 'fast_period_input'):
                self.fast_period_input.set_value(str(config.get('fast_period', 12)))
                self.slow_period_input.set_value(str(config.get('slow_period', 26)))
                self.signal_period_input.set_value(str(config.get('signal_period', 9)))
                self.amount_input.set_value(str(config.get('amount', 100)))
                self.stop_loss_input.set_value(str(config.get('stop_loss', 3)))
                self.take_profit_input.set_value(str(config.get('take_profit', 6)))
                self.retrain_days_input.set_value(str(config.get('retrain_days', 30)))
                self.confidence_input.set_value(str(config.get('confidence', 0.7)))
            else:
                self.fast_period_var.set(str(config.get('fast_period', 12)))
                self.slow_period_var.set(str(config.get('slow_period', 26)))
                self.signal_period_var.set(str(config.get('signal_period', 9)))
                self.amount_var.set(str(config.get('amount', 100)))
                self.stop_loss_var.set(str(config.get('stop_loss', 3)))
                self.take_profit_var.set(str(config.get('take_profit', 6)))
                self.retrain_days_var.set(str(config.get('retrain_days', 30)))
                self.confidence_var.set(str(config.get('confidence', 0.7)))

            messagebox.showinfo("成功", "FMACD策略配置已加载")
        else:
            messagebox.showinfo("提示", "没有找到保存的FMACD策略配置")

    def add_status_message(self, message):
        """添加状态消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        full_message = f"[{timestamp}] {message}\n"

        self.status_text.config(state=tk.NORMAL)
        self.status_text.insert(tk.END, full_message)
        self.status_text.see(tk.END)
        self.status_text.config(state=tk.DISABLED)

    def update_display(self):
        """更新FMACD指标显示"""
        if self.running and self.strategy:
            try:
                status = self.strategy.get_strategy_status()
                if status:
                    position_text = "无仓位"
                    if status['position'] == 1:
                        position_text = f"多头 @ {status['entry_price']:.6f}"
                    elif status['position'] == -1:
                        position_text = f"空头 @ {status['entry_price']:.6f}"

                    latest_fmacd = status.get('latest_fmacd', 0)
                    latest_fdif = status.get('latest_fdif', 0)
                    latest_fdea = status.get('latest_fdea', 0)

                    signal_status = "金叉" if latest_fdif > latest_fdea else "死叉"

                    self.add_status_message(f"仓位: {position_text}")
                    self.add_status_message(f"FMACD: {latest_fmacd:.6f}, FDIF: {latest_fdif:.6f}, "
                                          f"FDEA: {latest_fdea:.6f} ({signal_status})")

                    # 显示模型状态
                    last_retrain = status.get('last_retrain_date', '未训练')
                    prediction_count = status.get('prediction_history_length', 0)
                    self.add_status_message(f"模型状态: 最后训练 {last_retrain}, 预测次数 {prediction_count}")

            except Exception as e:
                # 如果策略状态获取失败，尝试获取基本价格信息
                try:
                    current_price = exchange_manager.get_ticker(self.symbol_var.get()).get('last', 0)
                    if current_price > 0:
                        self.add_status_message(f"当前价格: {current_price:.6f} (FMACD计算中...)")
                except:
                    pass
