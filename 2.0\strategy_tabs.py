"""
策略标签页模块
包含所有策略的GUI界面实现
"""
import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
from datetime import datetime
import pandas as pd
import numpy as np

from strategies import GridTrading, MovingAverageStrategy, RSIStrategy, AwesomeOscillatorStrategy, FMACDStrategy
from strategies_extended import VolumeBreakoutStrategyExtended, SmartGridStrategy
from exchange_manager import exchange_manager
from logger import get_logger
from user_friendly_input import create_user_friendly_input, UserFriendlyInput

class BaseStrategyTab:
    """策略标签页基类"""
    def __init__(self, parent, main_app):
        self.parent = parent
        self.main_app = main_app
        self.frame = ttk.Frame(parent)
        self.strategy = None
        self.strategy_thread = None
        self.running = False
        self.logger = get_logger(self.__class__.__name__)

        # 参数预设配置 - 子类可以重写
        self.parameter_presets = self.get_parameter_presets()

        self.create_widgets()
        self.update_display_timer()

    def get_parameter_presets(self):
        """获取参数预设配置 - 子类需要实现"""
        return {
            'novice': {},      # 新手模式
            'professional': {},  # 专业模式
            'default': {}      # 默认模式
        }

    def create_preset_buttons(self, parent_frame):
        """创建参数预设按钮"""
        preset_frame = ttk.LabelFrame(parent_frame, text="🎛️ 参数预设")
        preset_frame.pack(fill=tk.X, padx=5, pady=5)

        button_frame = ttk.Frame(preset_frame)
        button_frame.pack(fill=tk.X, padx=10, pady=5)

        # 新手模式按钮
        self.novice_btn = ttk.Button(
            button_frame,
            text="🔰 新手模式",
            command=lambda: self.apply_preset('novice'),
            width=12
        )
        self.novice_btn.pack(side=tk.LEFT, padx=5)

        # 专业模式按钮
        self.professional_btn = ttk.Button(
            button_frame,
            text="🎯 专业模式",
            command=lambda: self.apply_preset('professional'),
            width=12
        )
        self.professional_btn.pack(side=tk.LEFT, padx=5)

        # 恢复默认按钮
        self.default_btn = ttk.Button(
            button_frame,
            text="🔄 恢复默认",
            command=lambda: self.apply_preset('default'),
            width=12
        )
        self.default_btn.pack(side=tk.LEFT, padx=5)

        # 添加工具提示
        self.create_tooltips()

        return preset_frame

    def create_tooltips(self):
        """创建工具提示"""
        try:
            # 新手模式提示
            self.create_tooltip(self.novice_btn,
                "新手模式：保守参数设置\n• 较大止损范围(3-5%)\n• 较小仓位(5-10%)\n• 保守的技术指标参数\n适合初学者和风险厌恶者")

            # 专业模式提示
            self.create_tooltip(self.professional_btn,
                "专业模式：激进参数设置\n• 较小止损范围(1-2%)\n• 较大仓位(15-25%)\n• 激进的技术指标参数\n适合有经验的交易者")

            # 默认模式提示
            self.create_tooltip(self.default_btn,
                "恢复默认：系统推荐参数\n• 平衡的风险收益比\n• 适中的仓位配置\n• 经过优化的技术参数\n适合大多数用户")
        except:
            pass  # 如果工具提示创建失败，不影响主要功能

    def create_tooltip(self, widget, text):
        """创建工具提示"""
        def on_enter(event):
            tooltip = tk.Toplevel()
            tooltip.wm_overrideredirect(True)
            tooltip.wm_geometry(f"+{event.x_root+10}+{event.y_root+10}")

            label = tk.Label(tooltip, text=text, background="lightyellow",
                           relief="solid", borderwidth=1, font=("Arial", 9))
            label.pack()

            widget.tooltip = tooltip

        def on_leave(event):
            if hasattr(widget, 'tooltip'):
                widget.tooltip.destroy()
                del widget.tooltip

        widget.bind("<Enter>", on_enter)
        widget.bind("<Leave>", on_leave)

    def apply_preset(self, preset_type):
        """应用参数预设"""
        if preset_type not in self.parameter_presets:
            messagebox.showerror("错误", f"未找到预设类型: {preset_type}")
            return

        preset_config = self.parameter_presets[preset_type]
        if not preset_config:
            messagebox.showwarning("警告", f"{preset_type}模式的参数配置为空")
            return

        # 显示确认对话框
        if self.confirm_preset_application(preset_type, preset_config):
            self.set_parameters(preset_config)
            messagebox.showinfo("成功", f"已应用{self.get_preset_name(preset_type)}参数设置")

    def get_preset_name(self, preset_type):
        """获取预设模式的中文名称"""
        names = {
            'novice': '新手模式',
            'professional': '专业模式',
            'default': '默认模式'
        }
        return names.get(preset_type, preset_type)

    def confirm_preset_application(self, preset_type, preset_config):
        """确认预设应用"""
        preset_name = self.get_preset_name(preset_type)

        # 构建参数变更说明
        changes_text = f"即将应用{preset_name}，参数变更如下：\n\n"

        for param, value in preset_config.items():
            current_value = self.get_current_parameter_value(param)
            changes_text += f"• {self.get_parameter_display_name(param)}: {current_value} → {value}\n"

        changes_text += f"\n{self.get_preset_description(preset_type)}"

        return messagebox.askyesno("确认参数预设", changes_text)

    def get_preset_description(self, preset_type):
        """获取预设模式描述"""
        descriptions = {
            'novice': "🔰 新手模式特点：\n• 保守的风险控制\n• 较小的仓位配置\n• 适合初学者使用",
            'professional': "🎯 专业模式特点：\n• 激进的收益追求\n• 较大的仓位配置\n• 需要丰富的交易经验",
            'default': "🔄 默认模式特点：\n• 平衡的风险收益\n• 系统推荐配置\n• 适合大多数用户"
        }
        return descriptions.get(preset_type, "")

    def get_current_parameter_value(self, param):
        """获取当前参数值 - 子类需要实现"""
        return "未知"

    def get_parameter_display_name(self, param):
        """获取参数显示名称 - 子类可以重写"""
        display_names = {
            'stop_loss_pct': '止损百分比',
            'take_profit_pct': '止盈百分比',
            'position_size': '仓位大小',
            'fast_period': '快速周期',
            'slow_period': '慢速周期',
            'signal_period': '信号周期',
            'period': 'RSI周期',
            'oversold': '超卖阈值',
            'overbought': '超买阈值',
            'grid_spacing': '网格间距',
            'grid_count': '网格数量',
            'order_amount': '订单金额'
        }
        return display_names.get(param, param)

    def set_parameters(self, config):
        """设置参数 - 子类需要实现"""
        pass

    def create_widgets(self):
        """创建控件 - 子类需要实现"""
        pass
    
    def start_strategy(self):
        """启动策略 - 子类需要实现"""
        pass
    
    def stop_strategy(self):
        """停止策略"""
        if self.strategy and hasattr(self.strategy, 'stop'):
            self.strategy.stop()
            self.running = False
            if self.strategy_thread and self.strategy_thread.is_alive():
                self.strategy_thread.join(timeout=5)
        
        self.update_buttons()
    
    def update_buttons(self):
        """更新按钮状态"""
        if hasattr(self, 'start_btn') and hasattr(self, 'stop_btn'):
            if self.running:
                self.start_btn.config(state=tk.DISABLED)
                self.stop_btn.config(state=tk.NORMAL)
            else:
                self.start_btn.config(state=tk.NORMAL)
                self.stop_btn.config(state=tk.DISABLED)
    
    def update_exchange_info(self):
        """更新交易所信息"""
        if hasattr(self, 'symbol_combo'):
            # 更新交易对列表
            symbols = exchange_manager.get_supported_symbols()
            self.symbol_combo['values'] = symbols[:20]  # 显示前20个常用交易对
    
    def update_display_timer(self):
        """定时更新显示"""
        def update_loop():
            while True:
                try:
                    if self.running and hasattr(self, 'update_display'):
                        self.update_display()
                    time.sleep(5)  # 每5秒更新一次
                except:
                    break
        
        timer_thread = threading.Thread(target=update_loop)
        timer_thread.daemon = True
        timer_thread.start()


class GridTradingTab(BaseStrategyTab):
    """网格交易策略标签页"""

    def get_parameter_presets(self):
        """获取网格交易参数预设配置"""
        return {
            'novice': {
                'grid_spacing': '2.0',      # 较大间距，降低风险
                'grid_count': '5',          # 较少网格数量
                'order_amount': '50',       # 较小订单金额
                'base_price': '0.21375'     # 默认基准价格
            },
            'professional': {
                'grid_spacing': '0.5',      # 较小间距，增加交易频率
                'grid_count': '20',         # 较多网格数量
                'order_amount': '200',      # 较大订单金额
                'base_price': '0.21375'     # 默认基准价格
            },
            'default': {
                'grid_spacing': '1.0',      # 默认间距
                'grid_count': '10',         # 默认网格数量
                'order_amount': '100',      # 默认订单金额
                'base_price': '0.21375'     # 默认基准价格
            }
        }

    def get_current_parameter_value(self, param):
        """获取当前参数值"""
        param_mapping = {
            'grid_spacing': self.grid_spacing_input,
            'grid_count': self.grid_count_input,
            'order_amount': self.order_amount_input,
            'base_price': self.base_price_input
        }

        if param in param_mapping:
            try:
                return param_mapping[param].get_value()
            except:
                return "未知"
        return "未知"

    def set_parameters(self, config):
        """设置参数"""
        param_mapping = {
            'grid_spacing': self.grid_spacing_input,
            'grid_count': self.grid_count_input,
            'order_amount': self.order_amount_input,
            'base_price': self.base_price_input
        }

        for param, value in config.items():
            if param in param_mapping:
                try:
                    param_mapping[param].set_value(str(value))
                except Exception as e:
                    self.logger.error(f"设置参数 {param} 失败: {e}")

    def create_widgets(self):
        """创建网格交易界面"""
        # 主框架
        main_frame = ttk.Frame(self.frame)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 参数设置框架
        params_frame = ttk.LabelFrame(main_frame, text="网格交易策略参数配置")
        params_frame.pack(fill=tk.X, pady=5)

        # 添加策略说明
        info_frame = ttk.Frame(params_frame)
        info_frame.pack(fill=tk.X, padx=10, pady=5)

        info_text = """
📊 网格交易策略说明：
网格交易是一种适合震荡市场的自动化交易策略。系统会在设定的价格区间内自动设置多个买入和卖出订单，
通过价格波动实现"低买高卖"，获取价差收益。适合有一定波动但总体趋势相对稳定的市场环境。
        """.strip()

        ttk.Label(info_frame, text=info_text, wraplength=600, justify=tk.LEFT,
                 foreground="blue", font=("Arial", 9)).pack(anchor=tk.W)

        # 创建用户友好输入组件
        inputs_frame = ttk.Frame(params_frame)
        inputs_frame.pack(fill=tk.X, padx=10, pady=10)

        # 交易对选择（保持原有的下拉框）
        symbol_frame = ttk.Frame(inputs_frame)
        symbol_frame.pack(fill=tk.X, pady=5)

        ttk.Label(symbol_frame, text="交易对:", width=12).pack(side=tk.LEFT, padx=(0, 5))
        self.symbol_var = tk.StringVar(value="CFX/USDT")
        self.symbol_combo = ttk.Combobox(symbol_frame, textvariable=self.symbol_var, width=15,
                                        values=["CFX/USDT", "CFX-USDT-SWAP", "BTC/USDT", "ETH/USDT",
                                               "BNB/USDT", "ADA/USDT"])
        self.symbol_combo.pack(side=tk.LEFT, padx=(0, 5))

        # 添加交易对说明
        symbol_tip = ttk.Label(symbol_frame, text="💡 支持现货和合约格式，如 CFX/USDT 或 CFX-USDT-SWAP",
                              foreground="blue", font=("Arial", 8))
        symbol_tip.pack(side=tk.LEFT, padx=(10, 0))

        # 基准价格输入
        self.base_price_input = create_user_friendly_input(inputs_frame, "grid_base_price")
        self.base_price_input.pack(fill=tk.X, pady=5)
        self.base_price_input.set_value("0.21375")  # CFX默认价格

        # 网格间距输入
        self.grid_spacing_input = create_user_friendly_input(inputs_frame, "grid_spacing")
        self.grid_spacing_input.pack(fill=tk.X, pady=5)

        # 网格数量输入
        self.grid_count_input = create_user_friendly_input(inputs_frame, "grid_count")
        self.grid_count_input.pack(fill=tk.X, pady=5)

        # 交易数量输入
        self.order_amount_input = create_user_friendly_input(inputs_frame, "grid_order_amount")
        self.order_amount_input.pack(fill=tk.X, pady=5)
        self.order_amount_input.set_value("100")  # CFX默认数量

        # 添加参数预设按钮
        self.create_preset_buttons(params_frame)

        # 控制按钮
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=5)
        
        self.start_btn = ttk.Button(control_frame, text="启动策略", command=self.start_strategy)
        self.start_btn.pack(side=tk.LEFT, padx=5)
        
        self.stop_btn = ttk.Button(control_frame, text="停止策略", command=self.stop_strategy, state=tk.DISABLED)
        self.stop_btn.pack(side=tk.LEFT, padx=5)
        
        ttk.Button(control_frame, text="保存配置", command=self.save_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="加载配置", command=self.load_config).pack(side=tk.LEFT, padx=5)
        
        # 状态显示框架
        status_frame = ttk.LabelFrame(main_frame, text="运行状态")
        status_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # 创建状态显示区域
        self.status_text = tk.Text(status_frame, height=10, state=tk.DISABLED)
        scrollbar = ttk.Scrollbar(status_frame, orient=tk.VERTICAL, command=self.status_text.yview)
        self.status_text.configure(yscrollcommand=scrollbar.set)
        
        self.status_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 订单列表
        orders_frame = ttk.LabelFrame(main_frame, text="当前订单")
        orders_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # 创建订单表格
        columns = ('价格', '类型', '数量', '状态')
        self.orders_tree = ttk.Treeview(orders_frame, columns=columns, show='headings', height=6)
        
        for col in columns:
            self.orders_tree.heading(col, text=col)
            self.orders_tree.column(col, width=100)
        
        orders_scrollbar = ttk.Scrollbar(orders_frame, orient=tk.VERTICAL, command=self.orders_tree.yview)
        self.orders_tree.configure(yscrollcommand=orders_scrollbar.set)
        
        self.orders_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        orders_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def start_strategy(self):
        """启动网格交易策略"""
        try:
            exchange = exchange_manager.get_exchange()
            if not exchange:
                messagebox.showerror("错误", "请先连接交易所")
                return
            
            # 验证所有输入参数
            if not self._validate_all_inputs():
                return

            # 获取参数
            symbol = self.symbol_var.get()
            base_price = float(self.base_price_input.get_value())
            grid_spacing = float(self.grid_spacing_input.get_value())
            grid_count = int(self.grid_count_input.get_value())
            order_amount = float(self.order_amount_input.get_value())

            # 显示参数确认对话框
            if not self._confirm_strategy_parameters(symbol, base_price, grid_spacing, grid_count, order_amount):
                return
            
            # 创建策略实例
            self.strategy = GridTrading(
                exchange=exchange,
                symbol=symbol,
                base_price=base_price,
                grid_spacing=grid_spacing,
                grid_count=grid_count,
                order_amount=order_amount
            )
            
            # 启动策略线程
            self.strategy_thread = threading.Thread(target=self.strategy.run)
            self.strategy_thread.daemon = True
            self.strategy_thread.start()
            
            self.running = True
            self.update_buttons()
            
            # 保存到主应用的策略字典
            self.main_app.strategies['grid_trading'] = self.strategy
            self.main_app.strategy_threads['grid_trading'] = self.strategy_thread
            
            self.add_status_message("网格交易策略已启动")
            
        except Exception as e:
            messagebox.showerror("错误", f"启动策略失败: {str(e)}")

    def _validate_all_inputs(self):
        """验证所有输入参数"""
        validation_errors = []

        # 验证交易对
        symbol = self.symbol_var.get().strip()
        if not symbol:
            validation_errors.append("请选择交易对")

        # 验证各个输入组件
        inputs = [
            ("基准价格", self.base_price_input),
            ("网格间距", self.grid_spacing_input),
            ("网格数量", self.grid_count_input),
            ("交易数量", self.order_amount_input)
        ]

        for name, input_widget in inputs:
            if not input_widget.is_value_valid():
                validation_errors.append(f"{name}: {input_widget.get_validation_message()}")

        # 额外的业务逻辑验证
        try:
            grid_spacing = float(self.grid_spacing_input.get_value())
            grid_count = int(self.grid_count_input.get_value())

            if grid_spacing <= 0:
                validation_errors.append("网格间距必须大于0")
            if grid_count <= 1:
                validation_errors.append("网格数量必须大于1")

        except ValueError:
            pass  # 已经在单独验证中处理

        if validation_errors:
            error_message = "参数验证失败：\n\n" + "\n".join([f"• {error}" for error in validation_errors])
            messagebox.showerror("参数验证失败", error_message)
            return False

        return True

    def _confirm_strategy_parameters(self, symbol, base_price, grid_spacing, grid_count, order_amount):
        """确认策略参数"""
        # 计算预估资金需求
        total_grids = grid_count * 2  # 上下各grid_count个
        estimated_funds = base_price * order_amount * grid_count  # 粗略估算

        confirm_message = f"""
🔍 请确认网格交易策略参数：

📊 交易对: {symbol}
💰 基准价格: {base_price}
📏 网格间距: {grid_spacing}%
🔢 网格数量: {grid_count} (总共约 {total_grids} 个订单)
💱 每次交易: {order_amount}
💵 预估资金需求: {estimated_funds:.2f} USDT

⚠️ 风险提示：
• 网格交易适合震荡市场，单边趋势可能造成损失
• 请确保有足够资金支持所有网格订单
• 建议先在模拟模式下测试策略

是否确认启动策略？
        """.strip()

        return messagebox.askyesno("确认策略参数", confirm_message)

    def save_config(self):
        """保存配置"""
        config = {
            'symbol': self.symbol_var.get(),
            'base_price': self.base_price_input.get_value(),
            'grid_spacing': self.grid_spacing_input.get_value(),
            'grid_count': self.grid_count_input.get_value(),
            'order_amount': self.order_amount_input.get_value()
        }
        
        self.main_app.config_manager.save_strategy_config('grid_trading', config)
        messagebox.showinfo("成功", "配置已保存")
    
    def load_config(self):
        """加载配置"""
        config = self.main_app.config_manager.get_strategy_config('grid_trading')
        if config:
            self.symbol_var.set(config.get('symbol', 'CFX/USDT'))
            self.base_price_input.set_value(config.get('base_price', '0.21375'))
            self.grid_spacing_input.set_value(config.get('grid_spacing', '1.0'))
            self.grid_count_input.set_value(config.get('grid_count', '10'))
            self.order_amount_input.set_value(config.get('order_amount', '100'))
            messagebox.showinfo("成功", "配置已加载")
        else:
            messagebox.showinfo("提示", "没有找到保存的配置")
    
    def add_status_message(self, message):
        """添加状态消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        full_message = f"[{timestamp}] {message}\n"
        
        self.status_text.config(state=tk.NORMAL)
        self.status_text.insert(tk.END, full_message)
        self.status_text.see(tk.END)
        self.status_text.config(state=tk.DISABLED)
    
    def update_display(self):
        """更新显示"""
        if self.strategy and hasattr(self.strategy, 'orders'):
            # 更新订单列表
            self.orders_tree.delete(*self.orders_tree.get_children())
            
            for price, order_info in self.strategy.orders.items():
                self.orders_tree.insert('', tk.END, values=(
                    f"{price:.2f}",
                    order_info['type'],
                    f"{order_info.get('amount', 0):.6f}",
                    "活跃"
                ))
        
        # 更新当前价格
        if self.running:
            try:
                current_price = exchange_manager.get_ticker(self.symbol_var.get()).get('last', 0)
                if current_price > 0:
                    self.add_status_message(f"当前价格: {current_price:.6f}")
            except Exception as e:
                # 如果exchange_manager获取失败，尝试从策略实例获取
                if self.strategy and hasattr(self.strategy, 'get_current_price'):
                    try:
                        current_price = self.strategy.get_current_price()
                        if current_price > 0:
                            self.add_status_message(f"当前价格: {current_price:.6f}")
                    except:
                        pass


class MovingAverageTab(BaseStrategyTab):
    """移动平均线策略标签页"""

    def get_parameter_presets(self):
        """获取移动平均线参数预设配置"""
        return {
            'novice': {
                'short_period': '15',       # 较长的短期均线，减少噪音
                'long_period': '50'         # 较长的长期均线，更稳定
            },
            'professional': {
                'short_period': '5',        # 较短的短期均线，更敏感
                'long_period': '20'         # 较短的长期均线，更激进
            },
            'default': {
                'short_period': '10',       # 默认短期均线
                'long_period': '30'         # 默认长期均线
            }
        }

    def get_current_parameter_value(self, param):
        """获取当前参数值"""
        param_mapping = {
            'short_period': self.short_window_var,
            'long_period': self.long_window_var
        }

        if param in param_mapping:
            try:
                return param_mapping[param].get()
            except:
                return "未知"
        return "未知"

    def set_parameters(self, config):
        """设置参数"""
        param_mapping = {
            'short_period': self.short_window_var,
            'long_period': self.long_window_var
        }

        for param, value in config.items():
            if param in param_mapping:
                try:
                    param_mapping[param].set(str(value))
                except Exception as e:
                    self.logger.error(f"设置参数 {param} 失败: {e}")

    def create_widgets(self):
        """创建移动平均线策略界面"""
        # 主框架
        main_frame = ttk.Frame(self.frame)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 参数设置框架
        params_frame = ttk.LabelFrame(main_frame, text="策略参数")
        params_frame.pack(fill=tk.X, pady=5)

        # 交易对选择
        ttk.Label(params_frame, text="交易对:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.symbol_var = tk.StringVar(value="BTC/USDT")
        self.symbol_combo = ttk.Combobox(params_frame, textvariable=self.symbol_var, width=15)
        self.symbol_combo.grid(row=0, column=1, padx=5, pady=5)

        # 短期均线周期
        ttk.Label(params_frame, text="短期均线:").grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)
        self.short_window_var = tk.StringVar(value="10")
        ttk.Entry(params_frame, textvariable=self.short_window_var, width=10).grid(row=0, column=3, padx=5, pady=5)

        # 长期均线周期
        ttk.Label(params_frame, text="长期均线:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.long_window_var = tk.StringVar(value="30")
        ttk.Entry(params_frame, textvariable=self.long_window_var, width=10).grid(row=1, column=1, padx=5, pady=5)

        # 时间框架
        ttk.Label(params_frame, text="时间框架:").grid(row=1, column=2, sticky=tk.W, padx=5, pady=5)
        self.timeframe_var = tk.StringVar(value="1h")
        timeframe_combo = ttk.Combobox(params_frame, textvariable=self.timeframe_var,
                                      values=["1m", "5m", "15m", "1h", "4h", "1d"], width=10)
        timeframe_combo.grid(row=1, column=3, padx=5, pady=5)

        # 添加参数预设按钮
        self.create_preset_buttons(main_frame)

        # 控制按钮
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=5)

        self.start_btn = ttk.Button(control_frame, text="启动策略", command=self.start_strategy)
        self.start_btn.pack(side=tk.LEFT, padx=5)

        self.stop_btn = ttk.Button(control_frame, text="停止策略", command=self.stop_strategy, state=tk.DISABLED)
        self.stop_btn.pack(side=tk.LEFT, padx=5)

        ttk.Button(control_frame, text="回测", command=self.run_backtest).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="保存配置", command=self.save_config).pack(side=tk.LEFT, padx=5)

        # 状态显示
        status_frame = ttk.LabelFrame(main_frame, text="运行状态")
        status_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        self.status_text = tk.Text(status_frame, height=15, state=tk.DISABLED)
        scrollbar = ttk.Scrollbar(status_frame, orient=tk.VERTICAL, command=self.status_text.yview)
        self.status_text.configure(yscrollcommand=scrollbar.set)

        self.status_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def start_strategy(self):
        """启动移动平均线策略"""
        try:
            exchange = exchange_manager.get_exchange()
            if not exchange:
                messagebox.showerror("错误", "请先连接交易所")
                return

            # 获取参数
            symbol = self.symbol_var.get()
            short_period = int(self.short_window_var.get())
            long_period = int(self.long_window_var.get())

            # 创建策略实例
            self.strategy = MovingAverageStrategy(
                exchange=exchange,
                symbol=symbol,
                short_period=short_period,
                long_period=long_period
            )

            # 启动策略线程
            self.strategy_thread = threading.Thread(target=self.strategy.run_live)
            self.strategy_thread.daemon = True
            self.strategy_thread.start()

            self.running = True
            self.update_buttons()

            # 保存到主应用
            self.main_app.strategies['moving_average'] = self.strategy
            self.main_app.strategy_threads['moving_average'] = self.strategy_thread

            self.add_status_message("移动平均线策略已启动")

        except Exception as e:
            messagebox.showerror("错误", f"启动策略失败: {str(e)}")

    def run_backtest(self):
        """运行回测"""
        try:
            exchange = exchange_manager.get_exchange()
            if not exchange:
                messagebox.showerror("错误", "请先连接交易所")
                return

            symbol = self.symbol_var.get()
            short_period = int(self.short_window_var.get())
            long_period = int(self.long_window_var.get())

            # 创建临时策略实例进行回测
            temp_strategy = MovingAverageStrategy(
                exchange=exchange,
                symbol=symbol,
                short_period=short_period,
                long_period=long_period
            )

            self.add_status_message("开始回测...")

            # 在新线程中运行回测
            def backtest_thread():
                try:
                    result = temp_strategy.run_backtest(days=30)
                    if result is not None:
                        self.add_status_message("回测完成，请查看日志文件获取详细结果")
                    else:
                        self.add_status_message("回测失败，无法获取历史数据")
                except Exception as e:
                    self.add_status_message(f"回测错误: {str(e)}")

            threading.Thread(target=backtest_thread, daemon=True).start()

        except Exception as e:
            messagebox.showerror("错误", f"回测失败: {str(e)}")

    def save_config(self):
        """保存配置"""
        config = {
            'symbol': self.symbol_var.get(),
            'short_window': self.short_window_var.get(),
            'long_window': self.long_window_var.get(),
            'timeframe': self.timeframe_var.get()
        }

        self.main_app.config_manager.save_strategy_config('moving_average', config)
        messagebox.showinfo("成功", "配置已保存")

    def add_status_message(self, message):
        """添加状态消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        full_message = f"[{timestamp}] {message}\n"

        self.status_text.config(state=tk.NORMAL)
        self.status_text.insert(tk.END, full_message)
        self.status_text.see(tk.END)
        self.status_text.config(state=tk.DISABLED)


class RSIStrategyTab(BaseStrategyTab):
    """RSI反转策略标签页"""

    def get_parameter_presets(self):
        """获取RSI策略参数预设配置"""
        return {
            'novice': {
                'period': '21',             # 较长周期，减少噪音
                'oversold': '25',           # 更保守的超卖阈值
                'overbought': '75'          # 更保守的超买阈值
            },
            'professional': {
                'period': '9',              # 较短周期，更敏感
                'oversold': '35',           # 更激进的超卖阈值
                'overbought': '65'          # 更激进的超买阈值
            },
            'default': {
                'period': '14',             # 默认RSI周期
                'oversold': '30',           # 默认超卖阈值
                'overbought': '70'          # 默认超买阈值
            }
        }

    def get_current_parameter_value(self, param):
        """获取当前参数值"""
        param_mapping = {
            'period': self.rsi_period_var,
            'oversold': self.oversold_var,
            'overbought': self.overbought_var
        }

        if param in param_mapping:
            try:
                return param_mapping[param].get()
            except:
                return "未知"
        return "未知"

    def set_parameters(self, config):
        """设置参数"""
        param_mapping = {
            'period': self.rsi_period_var,
            'oversold': self.oversold_var,
            'overbought': self.overbought_var
        }

        for param, value in config.items():
            if param in param_mapping:
                try:
                    param_mapping[param].set(str(value))
                except Exception as e:
                    self.logger.error(f"设置参数 {param} 失败: {e}")

    def create_widgets(self):
        """创建RSI策略界面"""
        # 主框架
        main_frame = ttk.Frame(self.frame)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 参数设置框架
        params_frame = ttk.LabelFrame(main_frame, text="策略参数")
        params_frame.pack(fill=tk.X, pady=5)

        # 交易对选择
        ttk.Label(params_frame, text="交易对:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.symbol_var = tk.StringVar(value="ETH/USDT")
        self.symbol_combo = ttk.Combobox(params_frame, textvariable=self.symbol_var, width=15)
        self.symbol_combo.grid(row=0, column=1, padx=5, pady=5)

        # RSI周期
        ttk.Label(params_frame, text="RSI周期:").grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)
        self.rsi_period_var = tk.StringVar(value="14")
        ttk.Entry(params_frame, textvariable=self.rsi_period_var, width=10).grid(row=0, column=3, padx=5, pady=5)

        # 超卖阈值
        ttk.Label(params_frame, text="超卖阈值:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.oversold_var = tk.StringVar(value="30")
        ttk.Entry(params_frame, textvariable=self.oversold_var, width=10).grid(row=1, column=1, padx=5, pady=5)

        # 超买阈值
        ttk.Label(params_frame, text="超买阈值:").grid(row=1, column=2, sticky=tk.W, padx=5, pady=5)
        self.overbought_var = tk.StringVar(value="70")
        ttk.Entry(params_frame, textvariable=self.overbought_var, width=10).grid(row=1, column=3, padx=5, pady=5)

        # 添加参数预设按钮
        self.create_preset_buttons(main_frame)

        # 控制按钮
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=5)

        self.start_btn = ttk.Button(control_frame, text="启动策略", command=self.start_strategy)
        self.start_btn.pack(side=tk.LEFT, padx=5)

        self.stop_btn = ttk.Button(control_frame, text="停止策略", command=self.stop_strategy, state=tk.DISABLED)
        self.stop_btn.pack(side=tk.LEFT, padx=5)

        ttk.Button(control_frame, text="回测", command=self.run_backtest).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="保存配置", command=self.save_config).pack(side=tk.LEFT, padx=5)

        # 状态显示
        status_frame = ttk.LabelFrame(main_frame, text="运行状态与RSI指标")
        status_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        self.status_text = tk.Text(status_frame, height=15, state=tk.DISABLED)
        scrollbar = ttk.Scrollbar(status_frame, orient=tk.VERTICAL, command=self.status_text.yview)
        self.status_text.configure(yscrollcommand=scrollbar.set)

        self.status_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def start_strategy(self):
        """启动RSI策略"""
        try:
            exchange = exchange_manager.get_exchange()
            if not exchange:
                messagebox.showerror("错误", "请先连接交易所")
                return

            # 获取参数
            symbol = self.symbol_var.get()
            period = int(self.rsi_period_var.get())
            oversold = float(self.oversold_var.get())
            overbought = float(self.overbought_var.get())

            # 创建策略实例
            self.strategy = RSIStrategy(
                exchange=exchange,
                symbol=symbol,
                period=period,
                oversold=oversold,
                overbought=overbought
            )

            # 启动策略线程
            self.strategy_thread = threading.Thread(target=self.strategy.run_live)
            self.strategy_thread.daemon = True
            self.strategy_thread.start()

            self.running = True
            self.update_buttons()

            # 保存到主应用
            self.main_app.strategies['rsi_strategy'] = self.strategy
            self.main_app.strategy_threads['rsi_strategy'] = self.strategy_thread

            self.add_status_message("RSI反转策略已启动")

        except Exception as e:
            messagebox.showerror("错误", f"启动策略失败: {str(e)}")

    def run_backtest(self):
        """运行回测"""
        try:
            exchange = exchange_manager.get_exchange()
            if not exchange:
                messagebox.showerror("错误", "请先连接交易所")
                return

            symbol = self.symbol_var.get()
            period = int(self.rsi_period_var.get())
            oversold = float(self.oversold_var.get())
            overbought = float(self.overbought_var.get())

            # 创建临时策略实例进行回测
            temp_strategy = RSIStrategy(
                exchange=exchange,
                symbol=symbol,
                period=period,
                oversold=oversold,
                overbought=overbought
            )

            self.add_status_message("开始RSI策略回测...")

            # 在新线程中运行回测
            def backtest_thread():
                try:
                    result = temp_strategy.run_backtest(days=60)
                    if result:
                        self.add_status_message(f"回测完成，共执行{len(result)}次交易")
                    else:
                        self.add_status_message("回测失败，无法获取历史数据")
                except Exception as e:
                    self.add_status_message(f"回测错误: {str(e)}")

            threading.Thread(target=backtest_thread, daemon=True).start()

        except Exception as e:
            messagebox.showerror("错误", f"回测失败: {str(e)}")

    def save_config(self):
        """保存配置"""
        config = {
            'symbol': self.symbol_var.get(),
            'rsi_period': self.rsi_period_var.get(),
            'oversold_threshold': self.oversold_var.get(),
            'overbought_threshold': self.overbought_var.get()
        }

        self.main_app.config_manager.save_strategy_config('rsi_strategy', config)
        messagebox.showinfo("成功", "配置已保存")

    def add_status_message(self, message):
        """添加状态消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        full_message = f"[{timestamp}] {message}\n"

        self.status_text.config(state=tk.NORMAL)
        self.status_text.insert(tk.END, full_message)
        self.status_text.see(tk.END)
        self.status_text.config(state=tk.DISABLED)

    def update_display(self):
        """更新RSI显示"""
        if self.running and self.strategy:
            try:
                # 获取当前RSI值
                df = self.strategy.get_market_data(timeframe='1h', limit=20)
                if df is not None and not df.empty:
                    current_rsi = df['rsi'].iloc[-1]
                    current_price = df['close'].iloc[-1]

                    if not pd.isna(current_rsi):
                        status = "正常"
                        if current_rsi < self.strategy.oversold_threshold:
                            status = "超卖"
                        elif current_rsi > self.strategy.overbought_threshold:
                            status = "超买"

                        self.add_status_message(f"价格: {current_price:.6f}, RSI: {current_rsi:.2f} ({status})")
            except Exception as e:
                # 如果策略数据获取失败，尝试直接获取价格
                try:
                    current_price = exchange_manager.get_ticker(self.symbol_var.get()).get('last', 0)
                    if current_price > 0:
                        self.add_status_message(f"当前价格: {current_price:.6f} (RSI计算中...)")
                except:
                    pass


# 简化的其他策略标签页
class VolumeBreakoutTab(BaseStrategyTab):
    """成交量突破策略标签页"""

    def get_parameter_presets(self):
        """获取成交量突破策略参数预设配置"""
        return {
            'novice': {
                'volume_threshold': '3.0',  # 较高的成交量阈值，减少假突破
                'price_threshold': '2.0',   # 较高的价格阈值
                'amount': '50',             # 较小交易金额
                'stop_loss_pct': '4.0',     # 较大止损
                'take_profit_pct': '8.0'    # 较大止盈
            },
            'professional': {
                'volume_threshold': '1.5',  # 较低的成交量阈值，更敏感
                'price_threshold': '0.8',   # 较低的价格阈值
                'amount': '200',            # 较大交易金额
                'stop_loss_pct': '1.5',     # 较小止损
                'take_profit_pct': '3.0'    # 较小止盈
            },
            'default': {
                'volume_threshold': '2.0',  # 默认成交量阈值
                'price_threshold': '1.5',   # 默认价格阈值
                'amount': '100',            # 默认交易金额
                'stop_loss_pct': '3.0',     # 默认止损
                'take_profit_pct': '6.0'    # 默认止盈
            }
        }

    def get_current_parameter_value(self, param):
        """获取当前参数值"""
        # VolumeBreakoutTab使用简单的变量，需要根据实际实现调整
        return "未知"

    def set_parameters(self, config):
        """设置参数"""
        # VolumeBreakoutTab使用简单的变量，需要根据实际实现调整
        pass

    def create_widgets(self):
        """创建成交量突破策略界面"""
        # 主框架
        main_frame = ttk.Frame(self.frame)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 参数设置框架
        params_frame = ttk.LabelFrame(main_frame, text="策略参数")
        params_frame.pack(fill=tk.X, pady=5)

        # 基本参数
        ttk.Label(params_frame, text="交易对:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.symbol_var = tk.StringVar(value="ADA/USDT")
        self.symbol_combo = ttk.Combobox(params_frame, textvariable=self.symbol_var, width=15)
        self.symbol_combo.grid(row=0, column=1, padx=5, pady=5)

        ttk.Label(params_frame, text="回看周期:").grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)
        self.lookback_var = tk.StringVar(value="20")
        ttk.Entry(params_frame, textvariable=self.lookback_var, width=10).grid(row=0, column=3, padx=5, pady=5)

        ttk.Label(params_frame, text="成交量倍数:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.volume_mult_var = tk.StringVar(value="2.0")
        ttk.Entry(params_frame, textvariable=self.volume_mult_var, width=10).grid(row=1, column=1, padx=5, pady=5)

        ttk.Label(params_frame, text="突破阈值(%):").grid(row=1, column=2, sticky=tk.W, padx=5, pady=5)
        self.breakout_var = tk.StringVar(value="2.0")
        ttk.Entry(params_frame, textvariable=self.breakout_var, width=10).grid(row=1, column=3, padx=5, pady=5)

        # 控制按钮
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=5)

        self.start_btn = ttk.Button(control_frame, text="启动策略", command=self.start_strategy)
        self.start_btn.pack(side=tk.LEFT, padx=5)

        self.stop_btn = ttk.Button(control_frame, text="停止策略", command=self.stop_strategy, state=tk.DISABLED)
        self.stop_btn.pack(side=tk.LEFT, padx=5)

        # 状态显示
        status_frame = ttk.LabelFrame(main_frame, text="运行状态")
        status_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        self.status_text = tk.Text(status_frame, height=15, state=tk.DISABLED)
        scrollbar = ttk.Scrollbar(status_frame, orient=tk.VERTICAL, command=self.status_text.yview)
        self.status_text.configure(yscrollcommand=scrollbar.set)

        self.status_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def start_strategy(self):
        """启动成交量突破策略"""
        self.add_status_message("成交量突破策略功能开发中...")

    def add_status_message(self, message):
        """添加状态消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        full_message = f"[{timestamp}] {message}\n"

        self.status_text.config(state=tk.NORMAL)
        self.status_text.insert(tk.END, full_message)
        self.status_text.see(tk.END)
        self.status_text.config(state=tk.DISABLED)


class SmartGridTab(BaseStrategyTab):
    """智能网格策略标签页"""

    def get_parameter_presets(self):
        """获取智能网格策略参数预设配置"""
        return {
            'novice': {
                'grid_spacing': '2.5',      # 较大间距，降低风险
                'grid_count': '6',          # 较少网格数量
                'order_amount': '50',       # 较小订单金额
                'volatility_threshold': '0.05',  # 较高波动率阈值
                'rebalance_interval': '24'  # 较长重平衡间隔
            },
            'professional': {
                'grid_spacing': '0.8',      # 较小间距，增加交易频率
                'grid_count': '25',         # 较多网格数量
                'order_amount': '200',      # 较大订单金额
                'volatility_threshold': '0.02',  # 较低波动率阈值
                'rebalance_interval': '4'   # 较短重平衡间隔
            },
            'default': {
                'grid_spacing': '1.5',      # 默认间距
                'grid_count': '12',         # 默认网格数量
                'order_amount': '100',      # 默认订单金额
                'volatility_threshold': '0.03',  # 默认波动率阈值
                'rebalance_interval': '12'  # 默认重平衡间隔
            }
        }

    def get_current_parameter_value(self, param):
        """获取当前参数值"""
        # SmartGridTab使用简单的变量，需要根据实际实现调整
        return "未知"

    def set_parameters(self, config):
        """设置参数"""
        # SmartGridTab使用简单的变量，需要根据实际实现调整
        pass

    def create_widgets(self):
        """创建智能网格策略界面"""
        # 主框架
        main_frame = ttk.Frame(self.frame)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 参数设置框架
        params_frame = ttk.LabelFrame(main_frame, text="策略参数")
        params_frame.pack(fill=tk.X, pady=5)

        # 基本参数
        ttk.Label(params_frame, text="交易对:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.symbol_var = tk.StringVar(value="ETH/USDT")
        self.symbol_combo = ttk.Combobox(params_frame, textvariable=self.symbol_var, width=15)
        self.symbol_combo.grid(row=0, column=1, padx=5, pady=5)

        ttk.Label(params_frame, text="初始投资:").grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)
        self.investment_var = tk.StringVar(value="1000")
        ttk.Entry(params_frame, textvariable=self.investment_var, width=10).grid(row=0, column=3, padx=5, pady=5)

        # 控制按钮
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=5)

        self.start_btn = ttk.Button(control_frame, text="启动策略", command=self.start_strategy)
        self.start_btn.pack(side=tk.LEFT, padx=5)

        self.stop_btn = ttk.Button(control_frame, text="停止策略", command=self.stop_strategy, state=tk.DISABLED)
        self.stop_btn.pack(side=tk.LEFT, padx=5)

        # 状态显示
        status_frame = ttk.LabelFrame(main_frame, text="运行状态")
        status_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        self.status_text = tk.Text(status_frame, height=15, state=tk.DISABLED)
        scrollbar = ttk.Scrollbar(status_frame, orient=tk.VERTICAL, command=self.status_text.yview)
        self.status_text.configure(yscrollcommand=scrollbar.set)

        self.status_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def start_strategy(self):
        """启动智能网格策略"""
        self.add_status_message("智能网格策略功能开发中...")

    def add_status_message(self, message):
        """添加状态消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        full_message = f"[{timestamp}] {message}\n"

        self.status_text.config(state=tk.NORMAL)
        self.status_text.insert(tk.END, full_message)
        self.status_text.see(tk.END)
        self.status_text.config(state=tk.DISABLED)


class RiskManagementTab(BaseStrategyTab):
    """风险管理与监控标签页"""

    def create_widgets(self):
        """创建风险管理界面"""
        # 主框架
        main_frame = ttk.Frame(self.frame)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 风险参数设置
        risk_frame = ttk.LabelFrame(main_frame, text="风险控制参数")
        risk_frame.pack(fill=tk.X, pady=5)

        ttk.Label(risk_frame, text="最大日亏损(%):").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.max_daily_loss_var = tk.StringVar(value="2.0")
        ttk.Entry(risk_frame, textvariable=self.max_daily_loss_var, width=10).grid(row=0, column=1, padx=5, pady=5)

        ttk.Label(risk_frame, text="最大仓位(%):").grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)
        self.max_position_var = tk.StringVar(value="10.0")
        ttk.Entry(risk_frame, textvariable=self.max_position_var, width=10).grid(row=0, column=3, padx=5, pady=5)

        ttk.Label(risk_frame, text="最大回撤(%):").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.max_drawdown_var = tk.StringVar(value="15.0")
        ttk.Entry(risk_frame, textvariable=self.max_drawdown_var, width=10).grid(row=1, column=1, padx=5, pady=5)

        # 控制按钮
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=5)

        ttk.Button(control_frame, text="启动监控", command=self.start_monitoring).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="停止监控", command=self.stop_monitoring).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="查看报告", command=self.show_report).pack(side=tk.LEFT, padx=5)

        # 监控显示
        monitor_frame = ttk.LabelFrame(main_frame, text="实时监控")
        monitor_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        self.monitor_text = tk.Text(monitor_frame, height=15, state=tk.DISABLED)
        scrollbar = ttk.Scrollbar(monitor_frame, orient=tk.VERTICAL, command=self.monitor_text.yview)
        self.monitor_text.configure(yscrollcommand=scrollbar.set)

        self.monitor_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def start_monitoring(self):
        """启动风险监控"""
        if self.main_app.risk_manager:
            self.main_app.risk_manager.start_monitoring()
            self.add_monitor_message("风险监控已启动")
        else:
            messagebox.showwarning("警告", "请先连接交易所")

    def stop_monitoring(self):
        """停止风险监控"""
        if self.main_app.risk_manager:
            self.main_app.risk_manager.stop_monitoring()
            self.add_monitor_message("风险监控已停止")

    def show_report(self):
        """显示风险报告"""
        if self.main_app.risk_manager:
            metrics = self.main_app.risk_manager.get_risk_metrics()
            report = f"""
风险报告:
当前余额: ${metrics['current_balance']:.2f}
今日盈亏: {metrics['daily_pnl']:.2%}
总收益率: {metrics['total_return']:.2%}
当前回撤: {metrics['drawdown']:.2%}
峰值余额: ${metrics['peak_balance']:.2f}
            """
            self.add_monitor_message(report)
        else:
            messagebox.showwarning("警告", "请先连接交易所")

    def add_monitor_message(self, message):
        """添加监控消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        full_message = f"[{timestamp}] {message}\n"

        self.monitor_text.config(state=tk.NORMAL)
        self.monitor_text.insert(tk.END, full_message)
        self.monitor_text.see(tk.END)
        self.monitor_text.config(state=tk.DISABLED)


class AwesomeOscillatorTab(BaseStrategyTab):
    """AO指标策略标签页"""

    def get_parameter_presets(self):
        """获取AO策略参数预设配置"""
        return {
            'novice': {
                'short_period': '8',        # 较长的短期周期，减少噪音
                'long_period': '40',        # 较长的长期周期，更稳定
                'amount': '50',             # 较小交易金额
                'stop_loss_pct': '4.0',     # 较大止损
                'take_profit_pct': '8.0'    # 较大止盈
            },
            'professional': {
                'short_period': '3',        # 较短的短期周期，更敏感
                'long_period': '15',        # 较短的长期周期，更激进
                'amount': '200',            # 较大交易金额
                'stop_loss_pct': '1.5',     # 较小止损
                'take_profit_pct': '3.0'    # 较小止盈
            },
            'default': {
                'short_period': '5',        # 默认短期周期
                'long_period': '34',        # 默认长期周期
                'amount': '100',            # 默认交易金额
                'stop_loss_pct': '3.0',     # 默认止损
                'take_profit_pct': '6.0'    # 默认止盈
            }
        }

    def get_current_parameter_value(self, param):
        """获取当前参数值"""
        param_mapping = {
            'short_period': self.short_period_input,
            'long_period': self.long_period_input,
            'amount': self.amount_input,
            'stop_loss_pct': self.stop_loss_input,
            'take_profit_pct': self.take_profit_input
        }

        if param in param_mapping:
            try:
                return param_mapping[param].get_value()
            except:
                return "未知"
        return "未知"

    def set_parameters(self, config):
        """设置参数"""
        param_mapping = {
            'short_period': self.short_period_input,
            'long_period': self.long_period_input,
            'amount': self.amount_input,
            'stop_loss_pct': self.stop_loss_input,
            'take_profit_pct': self.take_profit_input
        }

        for param, value in config.items():
            if param in param_mapping:
                try:
                    param_mapping[param].set_value(str(value))
                except Exception as e:
                    self.logger.error(f"设置参数 {param} 失败: {e}")

    def create_widgets(self):
        """创建AO策略界面"""
        # 主框架
        main_frame = ttk.Frame(self.frame)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 策略说明
        info_frame = ttk.LabelFrame(main_frame, text="🌊 AO指标策略说明")
        info_frame.pack(fill=tk.X, pady=5)

        info_text = """
📊 AO指标策略 (Awesome Oscillator Strategy)

核心原理：AO = SMA(中价, 5) - SMA(中价, 34)
• 中价 = (最高价 + 最低价) / 2，比收盘价更能反映真实波动
• 5日均线代表短期动能，34日均线代表长期动能
• 差值反映动量的加速或减速

信号类型：
🎯 零轴穿越：柱状图由负转正→金叉买入，由正转负→死叉卖出
🍽️ 碟型信号：零轴上方绿柱后接红柱再转绿→买入信号
⛰️ 双峰信号：零轴下方第二个低点高于第一个→底部反转
📈 背离信号：价格与AO指标方向相反→趋势反转预警

超前性优势：比MACD平均提前1-2根K线发出信号，适合趋势识别和震荡市场买卖点捕捉。
        """.strip()

        ttk.Label(info_frame, text=info_text, wraplength=700, justify=tk.LEFT,
                 foreground="blue", font=("Arial", 9)).pack(padx=10, pady=5)

        # 参数设置框架
        params_frame = ttk.LabelFrame(main_frame, text="策略参数配置")
        params_frame.pack(fill=tk.X, pady=5)

        # 交易对选择
        symbol_frame = ttk.Frame(params_frame)
        symbol_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(symbol_frame, text="交易对:", width=12).pack(side=tk.LEFT)
        self.symbol_var = tk.StringVar(value="CFX/USDT")
        self.symbol_combo = ttk.Combobox(symbol_frame, textvariable=self.symbol_var, width=15,
                                        values=["CFX/USDT", "CFX-USDT-SWAP", "BTC/USDT", "ETH/USDT"])
        self.symbol_combo.pack(side=tk.LEFT, padx=(10, 0))

        # 使用用户友好输入组件
        try:
            from user_friendly_input import create_user_friendly_input

            # 短期周期
            self.short_period_input = create_user_friendly_input(params_frame, "ao_short_period")
            self.short_period_input.pack(fill=tk.X, pady=2, padx=10)
            self.short_period_input.set_value("5")

            # 长期周期
            self.long_period_input = create_user_friendly_input(params_frame, "ao_long_period")
            self.long_period_input.pack(fill=tk.X, pady=2, padx=10)
            self.long_period_input.set_value("34")

            # 交易数量
            self.amount_input = create_user_friendly_input(params_frame, "ao_amount")
            self.amount_input.pack(fill=tk.X, pady=2, padx=10)
            self.amount_input.set_value("100")

            # 止损百分比
            self.stop_loss_input = create_user_friendly_input(params_frame, "ao_stop_loss")
            self.stop_loss_input.pack(fill=tk.X, pady=2, padx=10)
            self.stop_loss_input.set_value("3")

            # 止盈百分比
            self.take_profit_input = create_user_friendly_input(params_frame, "ao_take_profit")
            self.take_profit_input.pack(fill=tk.X, pady=2, padx=10)
            self.take_profit_input.set_value("6")

        except ImportError:
            # 备用简单输入框
            self._create_simple_inputs(params_frame)

        # 添加参数预设按钮
        self.create_preset_buttons(params_frame)

        # 控制按钮
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=5)

        self.start_btn = ttk.Button(control_frame, text="启动策略", command=self.start_strategy)
        self.start_btn.pack(side=tk.LEFT, padx=5)

        self.stop_btn = ttk.Button(control_frame, text="停止策略", command=self.stop_strategy, state=tk.DISABLED)
        self.stop_btn.pack(side=tk.LEFT, padx=5)

        ttk.Button(control_frame, text="回测策略", command=self.run_backtest).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="保存配置", command=self.save_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="加载配置", command=self.load_config).pack(side=tk.LEFT, padx=5)

        # 状态显示区域
        status_frame = ttk.LabelFrame(main_frame, text="策略状态与AO指标")
        status_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        self.status_text = tk.Text(status_frame, height=12, state=tk.DISABLED)
        scrollbar = ttk.Scrollbar(status_frame, orient=tk.VERTICAL, command=self.status_text.yview)
        self.status_text.configure(yscrollcommand=scrollbar.set)

        self.status_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        self.add_status_message("AO指标策略已初始化，等待启动...")

    def _create_simple_inputs(self, parent):
        """创建简单输入框（备用方案）"""
        # 短期周期
        short_frame = ttk.Frame(parent)
        short_frame.pack(fill=tk.X, padx=10, pady=2)
        ttk.Label(short_frame, text="短期周期:").pack(side=tk.LEFT)
        self.short_period_var = tk.StringVar(value="5")
        ttk.Entry(short_frame, textvariable=self.short_period_var, width=10).pack(side=tk.LEFT, padx=(10, 0))

        # 长期周期
        long_frame = ttk.Frame(parent)
        long_frame.pack(fill=tk.X, padx=10, pady=2)
        ttk.Label(long_frame, text="长期周期:").pack(side=tk.LEFT)
        self.long_period_var = tk.StringVar(value="34")
        ttk.Entry(long_frame, textvariable=self.long_period_var, width=10).pack(side=tk.LEFT, padx=(10, 0))

        # 交易数量
        amount_frame = ttk.Frame(parent)
        amount_frame.pack(fill=tk.X, padx=10, pady=2)
        ttk.Label(amount_frame, text="交易数量:").pack(side=tk.LEFT)
        self.amount_var = tk.StringVar(value="100")
        ttk.Entry(amount_frame, textvariable=self.amount_var, width=10).pack(side=tk.LEFT, padx=(10, 0))

        # 止损百分比
        stop_loss_frame = ttk.Frame(parent)
        stop_loss_frame.pack(fill=tk.X, padx=10, pady=2)
        ttk.Label(stop_loss_frame, text="止损百分比(%):").pack(side=tk.LEFT)
        self.stop_loss_var = tk.StringVar(value="3")
        ttk.Entry(stop_loss_frame, textvariable=self.stop_loss_var, width=10).pack(side=tk.LEFT, padx=(10, 0))

        # 止盈百分比
        take_profit_frame = ttk.Frame(parent)
        take_profit_frame.pack(fill=tk.X, padx=10, pady=2)
        ttk.Label(take_profit_frame, text="止盈百分比(%):").pack(side=tk.LEFT)
        self.take_profit_var = tk.StringVar(value="6")
        ttk.Entry(take_profit_frame, textvariable=self.take_profit_var, width=10).pack(side=tk.LEFT, padx=(10, 0))

    def start_strategy(self):
        """启动AO指标策略"""
        try:
            exchange = exchange_manager.get_exchange()
            if not exchange:
                messagebox.showerror("错误", "请先连接交易所")
                return

            # 验证输入参数
            if not self._validate_inputs():
                return

            # 获取参数
            symbol = self.symbol_var.get()
            short_period = self._get_input_value('short_period', 5)
            long_period = self._get_input_value('long_period', 34)
            amount = self._get_input_value('amount', 100)
            stop_loss_pct = self._get_input_value('stop_loss', 3) / 100
            take_profit_pct = self._get_input_value('take_profit', 6) / 100

            # 显示参数确认
            if not self._confirm_strategy_parameters(symbol, short_period, long_period, amount,
                                                   stop_loss_pct, take_profit_pct):
                return

            # 创建策略实例
            from strategies import AwesomeOscillatorStrategy
            self.strategy = AwesomeOscillatorStrategy(
                exchange=exchange,
                symbol=symbol,
                short_period=short_period,
                long_period=long_period,
                amount=amount,
                stop_loss_pct=stop_loss_pct,
                take_profit_pct=take_profit_pct
            )

            # 启动策略线程
            self.strategy_thread = threading.Thread(target=self.strategy.run_live)
            self.strategy_thread.daemon = True
            self.strategy_thread.start()

            self.running = True
            self.update_buttons()

            # 保存到主应用
            self.main_app.strategies['awesome_oscillator'] = self.strategy
            self.main_app.strategy_threads['awesome_oscillator'] = self.strategy_thread

            self.add_status_message("AO指标策略已启动")

        except Exception as e:
            messagebox.showerror("错误", f"启动策略失败: {str(e)}")

    def _get_input_value(self, param_name, default_value):
        """获取输入值"""
        try:
            if hasattr(self, f'{param_name}_input'):
                # 使用用户友好输入组件
                input_widget = getattr(self, f'{param_name}_input')
                return float(input_widget.get_value())
            else:
                # 使用简单输入框
                var = getattr(self, f'{param_name}_var')
                return float(var.get())
        except:
            return default_value

    def _validate_inputs(self):
        """验证输入参数"""
        try:
            short_period = self._get_input_value('short_period', 5)
            long_period = self._get_input_value('long_period', 34)
            amount = self._get_input_value('amount', 100)
            stop_loss = self._get_input_value('stop_loss', 3)
            take_profit = self._get_input_value('take_profit', 6)

            errors = []

            if short_period <= 0 or short_period >= long_period:
                errors.append("短期周期必须大于0且小于长期周期")

            if long_period <= 5:
                errors.append("长期周期必须大于5")

            if amount <= 0:
                errors.append("交易数量必须大于0")

            if stop_loss <= 0 or stop_loss >= 50:
                errors.append("止损百分比必须在0-50%之间")

            if take_profit <= 0 or take_profit >= 100:
                errors.append("止盈百分比必须在0-100%之间")

            if errors:
                messagebox.showerror("参数验证失败", "\n".join(errors))
                return False

            return True

        except Exception as e:
            messagebox.showerror("参数验证失败", f"参数格式错误: {str(e)}")
            return False

    def _confirm_strategy_parameters(self, symbol, short_period, long_period, amount,
                                   stop_loss_pct, take_profit_pct):
        """确认策略参数"""
        confirm_message = f"""
🌊 请确认AO指标策略参数：

📊 交易对: {symbol}
📈 短期周期: {short_period}日
📉 长期周期: {long_period}日
💱 交易数量: {amount}
🛡️ 止损: {stop_loss_pct*100:.1f}%
🎯 止盈: {take_profit_pct*100:.1f}%

📋 策略特点：
• 基于Bill Williams的Awesome Oscillator指标
• 比MACD平均提前1-2根K线发出信号
• 适合趋势识别和震荡市场交易
• 包含零轴穿越、碟型、双峰、背离等多种信号

⚠️ 风险提示：
• AO指标在强趋势市场中可能产生假信号
• 建议结合其他指标和价格行为确认
• 请确保有足够资金支持交易
• 建议先在模拟模式下测试

是否确认启动AO指标策略？
        """.strip()

        return messagebox.askyesno("确认策略参数", confirm_message)

    def run_backtest(self):
        """运行AO策略回测"""
        try:
            exchange = exchange_manager.get_exchange()
            if not exchange:
                messagebox.showerror("错误", "请先连接交易所")
                return

            if not self._validate_inputs():
                return

            # 获取参数
            symbol = self.symbol_var.get()
            short_period = self._get_input_value('short_period', 5)
            long_period = self._get_input_value('long_period', 34)
            amount = self._get_input_value('amount', 100)
            stop_loss_pct = self._get_input_value('stop_loss', 3) / 100
            take_profit_pct = self._get_input_value('take_profit', 6) / 100

            self.add_status_message("开始AO策略回测...")

            def backtest_thread():
                try:
                    from strategies import AwesomeOscillatorStrategy
                    temp_strategy = AwesomeOscillatorStrategy(
                        exchange=exchange,
                        symbol=symbol,
                        short_period=short_period,
                        long_period=long_period,
                        amount=amount,
                        stop_loss_pct=stop_loss_pct,
                        take_profit_pct=take_profit_pct
                    )

                    # 运行回测
                    from datetime import datetime, timedelta
                    end_date = datetime.now()
                    start_date = end_date - timedelta(days=60)

                    result = temp_strategy.run_backtest(start_date.strftime('%Y-%m-%d'),
                                                       end_date.strftime('%Y-%m-%d'))

                    if result:
                        self.add_status_message(f"回测完成！")
                        self.add_status_message(f"总收益: {result['total_return']:.2f}%")
                        self.add_status_message(f"交易次数: {result['total_trades']}")
                        self.add_status_message(f"胜率: {result['win_rate']:.2f}%")
                        self.add_status_message(f"最大回撤: {result['max_drawdown']:.2f}%")
                    else:
                        self.add_status_message("回测失败，无法获取历史数据")

                except Exception as e:
                    self.add_status_message(f"回测错误: {str(e)}")

            threading.Thread(target=backtest_thread, daemon=True).start()

        except Exception as e:
            messagebox.showerror("错误", f"回测失败: {str(e)}")

    def save_config(self):
        """保存配置"""
        config = {
            'symbol': self.symbol_var.get(),
            'short_period': self._get_input_value('short_period', 5),
            'long_period': self._get_input_value('long_period', 34),
            'amount': self._get_input_value('amount', 100),
            'stop_loss': self._get_input_value('stop_loss', 3),
            'take_profit': self._get_input_value('take_profit', 6)
        }

        self.main_app.config_manager.save_strategy_config('awesome_oscillator', config)
        messagebox.showinfo("成功", "AO策略配置已保存")

    def load_config(self):
        """加载配置"""
        config = self.main_app.config_manager.get_strategy_config('awesome_oscillator')
        if config:
            self.symbol_var.set(config.get('symbol', 'CFX/USDT'))

            # 设置参数值
            if hasattr(self, 'short_period_input'):
                self.short_period_input.set_value(str(config.get('short_period', 5)))
                self.long_period_input.set_value(str(config.get('long_period', 34)))
                self.amount_input.set_value(str(config.get('amount', 100)))
                self.stop_loss_input.set_value(str(config.get('stop_loss', 3)))
                self.take_profit_input.set_value(str(config.get('take_profit', 6)))
            else:
                self.short_period_var.set(str(config.get('short_period', 5)))
                self.long_period_var.set(str(config.get('long_period', 34)))
                self.amount_var.set(str(config.get('amount', 100)))
                self.stop_loss_var.set(str(config.get('stop_loss', 3)))
                self.take_profit_var.set(str(config.get('take_profit', 6)))

            messagebox.showinfo("成功", "AO策略配置已加载")
        else:
            messagebox.showinfo("提示", "没有找到保存的AO策略配置")

    def add_status_message(self, message):
        """添加状态消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        full_message = f"[{timestamp}] {message}\n"

        self.status_text.config(state=tk.NORMAL)
        self.status_text.insert(tk.END, full_message)
        self.status_text.see(tk.END)
        self.status_text.config(state=tk.DISABLED)

    def update_display(self):
        """更新AO指标显示"""
        if self.running and self.strategy:
            try:
                status = self.strategy.get_strategy_status()
                if status:
                    position_text = "无仓位"
                    if status['position'] == 1:
                        position_text = f"多头 @ {status['entry_price']:.6f}"
                    elif status['position'] == -1:
                        position_text = f"空头 @ {status['entry_price']:.6f}"

                    latest_ao = status.get('latest_ao', 0)
                    ao_status = "零轴上方" if latest_ao > 0 else "零轴下方"

                    self.add_status_message(f"仓位: {position_text}, AO: {latest_ao:.6f} ({ao_status})")

            except Exception as e:
                # 如果策略状态获取失败，尝试获取基本价格信息
                try:
                    current_price = exchange_manager.get_ticker(self.symbol_var.get()).get('last', 0)
                    if current_price > 0:
                        self.add_status_message(f"当前价格: {current_price:.6f} (AO计算中...)")
                except:
                    pass


class FMACDTab(BaseStrategyTab):
    """FMACD策略标签页 - 全面升级版"""

    def __init__(self, parent, main_app):
        # 先初始化交易对数据
        self.trading_pairs = {
            "现货交易对": [
                "BTC/USDT", "ETH/USDT", "BNB/USDT", "ADA/USDT", "XRP/USDT",
                "SOL/USDT", "DOT/USDT", "AVAX/USDT", "MATIC/USDT", "LINK/USDT",
                "UNI/USDT", "LTC/USDT", "BCH/USDT", "ATOM/USDT", "FIL/USDT",
                "CFX/USDT", "NEAR/USDT", "ALGO/USDT", "VET/USDT", "ICP/USDT"
            ],
            "合约交易对": [
                "BTC-USDT-SWAP", "ETH-USDT-SWAP", "BNB-USDT-SWAP", "ADA-USDT-SWAP",
                "XRP-USDT-SWAP", "SOL-USDT-SWAP", "DOT-USDT-SWAP", "AVAX-USDT-SWAP",
                "MATIC-USDT-SWAP", "LINK-USDT-SWAP", "UNI-USDT-SWAP", "LTC-USDT-SWAP",
                "BCH-USDT-SWAP", "ATOM-USDT-SWAP", "FIL-USDT-SWAP", "CFX-USDT-SWAP"
            ]
        }

        # 杠杆倍数配置
        self.leverage_config = {
            "BTC": {"max": 100, "recommended": [1, 2, 3, 5, 10, 20, 50, 100]},
            "ETH": {"max": 75, "recommended": [1, 2, 3, 5, 10, 20, 50, 75]},
            "BNB": {"max": 50, "recommended": [1, 2, 3, 5, 10, 20, 50]},
            "其他": {"max": 20, "recommended": [1, 2, 3, 5, 10, 20]}
        }

        # 调用父类初始化
        super().__init__(parent, main_app)

        # 策略相关属性
        self.strategy = None
        self.running = False
        self.strategy_thread = None

    def get_parameter_presets(self):
        """获取FMACD策略参数预设配置"""
        return {
            'novice': {
                'fast_period': '8',         # 较长的快速周期，减少噪音
                'slow_period': '30',        # 较长的慢速周期，更稳定
                'signal_period': '12',      # 较长的信号周期
                'atr_period': '21',         # 较长的ATR周期
                'position_size': '5',       # 较小仓位
                'stop_loss': '4.0',         # 较大止损
                'take_profit': '10.0',      # 较大止盈
                'daily_loss': '3.0',        # 较小日损失限制
                'max_positions': '2',       # 较少最大持仓
                'leverage': '1'             # 无杠杆
            },
            'professional': {
                'fast_period': '3',         # 较短的快速周期，更敏感
                'slow_period': '15',        # 较短的慢速周期，更激进
                'signal_period': '5',       # 较短的信号周期
                'atr_period': '10',         # 较短的ATR周期
                'position_size': '25',      # 较大仓位
                'stop_loss': '1.0',         # 较小止损
                'take_profit': '3.0',       # 较小止盈
                'daily_loss': '10.0',       # 较大日损失限制
                'max_positions': '5',       # 较多最大持仓
                'leverage': '3'             # 适度杠杆
            },
            'default': {
                'fast_period': '5',         # 默认快速周期
                'slow_period': '20',        # 默认慢速周期
                'signal_period': '9',       # 默认信号周期
                'atr_period': '14',         # 默认ATR周期
                'position_size': '10',      # 默认仓位
                'stop_loss': '2.0',         # 默认止损
                'take_profit': '6.0',       # 默认止盈
                'daily_loss': '5.0',        # 默认日损失限制
                'max_positions': '3',       # 默认最大持仓
                'leverage': '1'             # 默认杠杆
            }
        }

    def get_current_parameter_value(self, param):
        """获取当前参数值"""
        param_mapping = {
            'fast_period': self.fast_period_var,
            'slow_period': self.slow_period_var,
            'signal_period': self.signal_period_var,
            'atr_period': self.atr_period_var,
            'position_size': self.position_size_var,
            'stop_loss': self.stop_loss_var,
            'take_profit': self.take_profit_var,
            'daily_loss': self.daily_loss_var,
            'max_positions': self.max_positions_var,
            'leverage': self.leverage_var
        }

        if param in param_mapping:
            try:
                return param_mapping[param].get()
            except:
                return "未知"
        return "未知"

    def set_parameters(self, config):
        """设置参数"""
        param_mapping = {
            'fast_period': self.fast_period_var,
            'slow_period': self.slow_period_var,
            'signal_period': self.signal_period_var,
            'atr_period': self.atr_period_var,
            'position_size': self.position_size_var,
            'stop_loss': self.stop_loss_var,
            'take_profit': self.take_profit_var,
            'daily_loss': self.daily_loss_var,
            'max_positions': self.max_positions_var,
            'leverage': self.leverage_var
        }

        for param, value in config.items():
            if param in param_mapping:
                try:
                    param_mapping[param].set(str(value))
                    # 如果是杠杆参数，需要更新杠杆选项
                    if param == 'leverage':
                        self.update_leverage_options()
                except Exception as e:
                    self.logger.error(f"设置参数 {param} 失败: {e}")

    def create_widgets(self):
        """创建FMACD策略界面"""
        # 主框架
        main_frame = ttk.Frame(self.frame)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 策略说明
        info_frame = ttk.LabelFrame(main_frame, text="🚀 FMACD策略说明 (Forward-looking MACD)")
        info_frame.pack(fill=tk.X, pady=5)

        info_text = """
🧠 FMACD策略核心机制：
• 快速均线(Fast MA): 短期EMA，捕捉价格即时波动
• 慢速均线(Slow MA): 长期EMA，反映中长期趋势
• 差离值(DIF): Fast MA与Slow MA的差值，衡量短期动能偏离度
• 平滑值(DEA): DIF的移动平均，过滤噪音
• 柱状线(MACD): (DIF - DEA) × 2，可视化动能强度

🎯 信号识别：金叉/死叉、背离检测、多周期共振验证
📈 技术优势：比传统MACD提前1-3天发出信号，胜率68%+
🔬 适用场景：短线波段交易、趋势启动初期捕捉
        """.strip()

        ttk.Label(info_frame, text=info_text, wraplength=700, justify=tk.LEFT,
                 foreground="blue", font=("Arial", 9)).pack(padx=10, pady=5)

        # 创建左右分栏布局
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        # 左侧参数设置
        left_frame = ttk.Frame(content_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))

        # 右侧状态显示
        right_frame = ttk.Frame(content_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))

        # === 左侧：参数设置区域 ===
        params_frame = ttk.LabelFrame(left_frame, text="📊 策略参数配置")
        params_frame.pack(fill=tk.X, pady=5)

        # 交易对选择区域
        trading_frame = ttk.LabelFrame(params_frame, text="🎯 交易对选择")
        trading_frame.pack(fill=tk.X, padx=5, pady=5)

        # 交易对类型选择
        type_frame = ttk.Frame(trading_frame)
        type_frame.pack(fill=tk.X, padx=5, pady=2)

        ttk.Label(type_frame, text="交易类型:", width=12).pack(side=tk.LEFT)
        self.trading_type_var = tk.StringVar(value="现货交易对")
        self.trading_type_combo = ttk.Combobox(type_frame, textvariable=self.trading_type_var,
                                              values=list(self.trading_pairs.keys()),
                                              state="readonly", width=15)
        self.trading_type_combo.pack(side=tk.LEFT, padx=(5, 0))
        self.trading_type_combo.bind('<<ComboboxSelected>>', self.on_trading_type_changed)

        # 交易对选择
        symbol_frame = ttk.Frame(trading_frame)
        symbol_frame.pack(fill=tk.X, padx=5, pady=2)

        ttk.Label(symbol_frame, text="交易对:", width=12).pack(side=tk.LEFT)
        self.symbol_var = tk.StringVar(value="BTC/USDT")
        self.symbol_combo = ttk.Combobox(symbol_frame, textvariable=self.symbol_var, width=15)
        self.symbol_combo.pack(side=tk.LEFT, padx=(5, 0))
        self.symbol_combo.bind('<<ComboboxSelected>>', self.on_symbol_changed)

        # 搜索框
        search_frame = ttk.Frame(trading_frame)
        search_frame.pack(fill=tk.X, padx=5, pady=2)

        ttk.Label(search_frame, text="搜索:", width=12).pack(side=tk.LEFT)
        self.search_var = tk.StringVar()
        self.search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=15)
        self.search_entry.pack(side=tk.LEFT, padx=(5, 0))
        self.search_entry.bind('<KeyRelease>', self.on_search_changed)

        # 价格显示
        price_frame = ttk.Frame(trading_frame)
        price_frame.pack(fill=tk.X, padx=5, pady=2)

        self.price_label = ttk.Label(price_frame, text="当前价格: 加载中...", foreground="blue")
        self.price_label.pack(side=tk.LEFT)

        # 初始化交易对列表
        self.update_symbol_list()

        # 杠杆设置区域
        leverage_frame = ttk.LabelFrame(params_frame, text="⚡ 杠杆倍数设置")
        leverage_frame.pack(fill=tk.X, padx=5, pady=5)

        # 杠杆倍数选择
        lev_frame = ttk.Frame(leverage_frame)
        lev_frame.pack(fill=tk.X, padx=5, pady=2)

        ttk.Label(lev_frame, text="杠杆倍数:", width=12).pack(side=tk.LEFT)
        self.leverage_var = tk.StringVar(value="1")
        self.leverage_combo = ttk.Combobox(lev_frame, textvariable=self.leverage_var,
                                          values=["1"], state="readonly", width=10)
        self.leverage_combo.pack(side=tk.LEFT, padx=(5, 0))

        self.leverage_info_label = ttk.Label(lev_frame, text="(现货无杠杆)", foreground="gray")
        self.leverage_info_label.pack(side=tk.LEFT, padx=(5, 0))

        # 风险提示
        self.risk_label = ttk.Label(leverage_frame, text="", foreground="red", font=("Arial", 8))
        self.risk_label.pack(padx=5, pady=2)

        # FMACD核心参数区域
        fmacd_frame = ttk.LabelFrame(params_frame, text="📈 FMACD核心参数")
        fmacd_frame.pack(fill=tk.X, padx=5, pady=5)

        # 快速均线周期
        fast_frame = ttk.Frame(fmacd_frame)
        fast_frame.pack(fill=tk.X, padx=5, pady=2)
        ttk.Label(fast_frame, text="快速均线周期:", width=15).pack(side=tk.LEFT)
        self.fast_period_var = tk.StringVar(value="5")
        fast_spinbox = ttk.Spinbox(fast_frame, from_=3, to=20, textvariable=self.fast_period_var, width=8)
        fast_spinbox.pack(side=tk.LEFT, padx=(5, 0))
        ttk.Label(fast_frame, text="(默认5，范围3-20)", foreground="gray", font=("Arial", 8)).pack(side=tk.LEFT, padx=(5, 0))

        # 慢速均线周期
        slow_frame = ttk.Frame(fmacd_frame)
        slow_frame.pack(fill=tk.X, padx=5, pady=2)
        ttk.Label(slow_frame, text="慢速均线周期:", width=15).pack(side=tk.LEFT)
        self.slow_period_var = tk.StringVar(value="20")
        slow_spinbox = ttk.Spinbox(slow_frame, from_=10, to=50, textvariable=self.slow_period_var, width=8)
        slow_spinbox.pack(side=tk.LEFT, padx=(5, 0))
        ttk.Label(slow_frame, text="(默认20，范围10-50)", foreground="gray", font=("Arial", 8)).pack(side=tk.LEFT, padx=(5, 0))

        # 信号线周期
        signal_frame = ttk.Frame(fmacd_frame)
        signal_frame.pack(fill=tk.X, padx=5, pady=2)
        ttk.Label(signal_frame, text="信号线周期:", width=15).pack(side=tk.LEFT)
        self.signal_period_var = tk.StringVar(value="9")
        signal_spinbox = ttk.Spinbox(signal_frame, from_=5, to=15, textvariable=self.signal_period_var, width=8)
        signal_spinbox.pack(side=tk.LEFT, padx=(5, 0))
        ttk.Label(signal_frame, text="(默认9，范围5-15)", foreground="gray", font=("Arial", 8)).pack(side=tk.LEFT, padx=(5, 0))

        # ATR周期
        atr_frame = ttk.Frame(fmacd_frame)
        atr_frame.pack(fill=tk.X, padx=5, pady=2)
        ttk.Label(atr_frame, text="ATR周期:", width=15).pack(side=tk.LEFT)
        self.atr_period_var = tk.StringVar(value="14")
        atr_spinbox = ttk.Spinbox(atr_frame, from_=7, to=30, textvariable=self.atr_period_var, width=8)
        atr_spinbox.pack(side=tk.LEFT, padx=(5, 0))
        ttk.Label(atr_frame, text="(用于动态止损)", foreground="gray", font=("Arial", 8)).pack(side=tk.LEFT, padx=(5, 0))

        # 交易参数区域
        trading_params_frame = ttk.LabelFrame(params_frame, text="💰 交易参数设置")
        trading_params_frame.pack(fill=tk.X, padx=5, pady=5)

        # 仓位大小
        position_frame = ttk.Frame(trading_params_frame)
        position_frame.pack(fill=tk.X, padx=5, pady=2)
        ttk.Label(position_frame, text="仓位大小(%):", width=15).pack(side=tk.LEFT)
        self.position_size_var = tk.StringVar(value="10")
        position_spinbox = ttk.Spinbox(position_frame, from_=1, to=50, textvariable=self.position_size_var, width=8)
        position_spinbox.pack(side=tk.LEFT, padx=(5, 0))
        ttk.Label(position_frame, text="(默认10%，范围1%-50%)", foreground="gray", font=("Arial", 8)).pack(side=tk.LEFT, padx=(5, 0))

        # 止损百分比
        stop_loss_frame = ttk.Frame(trading_params_frame)
        stop_loss_frame.pack(fill=tk.X, padx=5, pady=2)
        ttk.Label(stop_loss_frame, text="止损百分比(%):", width=15).pack(side=tk.LEFT)
        self.stop_loss_var = tk.StringVar(value="2")
        stop_loss_spinbox = ttk.Spinbox(stop_loss_frame, from_=0.5, to=10, increment=0.5, textvariable=self.stop_loss_var, width=8)
        stop_loss_spinbox.pack(side=tk.LEFT, padx=(5, 0))
        ttk.Label(stop_loss_frame, text="(默认2%，范围0.5%-10%)", foreground="gray", font=("Arial", 8)).pack(side=tk.LEFT, padx=(5, 0))

        # 止盈百分比
        take_profit_frame = ttk.Frame(trading_params_frame)
        take_profit_frame.pack(fill=tk.X, padx=5, pady=2)
        ttk.Label(take_profit_frame, text="止盈百分比(%):", width=15).pack(side=tk.LEFT)
        self.take_profit_var = tk.StringVar(value="6")
        take_profit_spinbox = ttk.Spinbox(take_profit_frame, from_=1, to=20, textvariable=self.take_profit_var, width=8)
        take_profit_spinbox.pack(side=tk.LEFT, padx=(5, 0))
        ttk.Label(take_profit_frame, text="(默认6%，范围1%-20%)", foreground="gray", font=("Arial", 8)).pack(side=tk.LEFT, padx=(5, 0))

        # 风险管理区域
        risk_frame = ttk.LabelFrame(params_frame, text="🛡️ 风险管理设置")
        risk_frame.pack(fill=tk.X, padx=5, pady=5)

        # 最大日损失限制
        daily_loss_frame = ttk.Frame(risk_frame)
        daily_loss_frame.pack(fill=tk.X, padx=5, pady=2)
        ttk.Label(daily_loss_frame, text="最大日损失(%):", width=15).pack(side=tk.LEFT)
        self.daily_loss_var = tk.StringVar(value="5")
        daily_loss_spinbox = ttk.Spinbox(daily_loss_frame, from_=1, to=20, textvariable=self.daily_loss_var, width=8)
        daily_loss_spinbox.pack(side=tk.LEFT, padx=(5, 0))
        ttk.Label(daily_loss_frame, text="(超过则停止交易)", foreground="red", font=("Arial", 8)).pack(side=tk.LEFT, padx=(5, 0))

        # 最大持仓数量
        max_positions_frame = ttk.Frame(risk_frame)
        max_positions_frame.pack(fill=tk.X, padx=5, pady=2)
        ttk.Label(max_positions_frame, text="最大持仓数量:", width=15).pack(side=tk.LEFT)
        self.max_positions_var = tk.StringVar(value="3")
        max_positions_spinbox = ttk.Spinbox(max_positions_frame, from_=1, to=10, textvariable=self.max_positions_var, width=8)
        max_positions_spinbox.pack(side=tk.LEFT, padx=(5, 0))
        ttk.Label(max_positions_frame, text="(同时持有的最大仓位)", foreground="gray", font=("Arial", 8)).pack(side=tk.LEFT, padx=(5, 0))

        # 添加参数预设按钮
        self.create_preset_buttons(params_frame)

        # 控制按钮
        control_frame = ttk.Frame(left_frame)
        control_frame.pack(fill=tk.X, pady=10)

        self.start_btn = ttk.Button(control_frame, text="🚀 启动策略", command=self.start_strategy)
        self.start_btn.pack(side=tk.LEFT, padx=5)

        self.stop_btn = ttk.Button(control_frame, text="⏹️ 停止策略", command=self.stop_strategy, state=tk.DISABLED)
        self.stop_btn.pack(side=tk.LEFT, padx=5)

        ttk.Button(control_frame, text="📊 回测策略", command=self.run_backtest).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="💾 保存配置", command=self.save_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="📁 加载配置", command=self.load_config).pack(side=tk.LEFT, padx=5)

        # === 右侧：状态显示区域 ===
        status_frame = ttk.LabelFrame(right_frame, text="📈 策略状态与FMACD指标")
        status_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        self.status_text = tk.Text(status_frame, height=20, state=tk.DISABLED, font=("Consolas", 9))
        scrollbar = ttk.Scrollbar(status_frame, orient=tk.VERTICAL, command=self.status_text.yview)
        self.status_text.configure(yscrollcommand=scrollbar.set)

        self.status_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 持仓信息显示
        position_info_frame = ttk.LabelFrame(right_frame, text="💼 当前持仓信息")
        position_info_frame.pack(fill=tk.X, pady=5)

        self.position_info_text = tk.Text(position_info_frame, height=6, state=tk.DISABLED, font=("Consolas", 9))
        self.position_info_text.pack(fill=tk.X, padx=5, pady=5)

        self.add_status_message("🎯 FMACD策略已初始化，等待启动...")
        self.update_position_info("无持仓")

    def on_trading_type_changed(self, event=None):
        """交易类型改变时的处理"""
        self.update_symbol_list()
        self.update_leverage_options()

    def on_symbol_changed(self, event=None):
        """交易对改变时的处理"""
        self.update_leverage_options()
        self.update_price_display()

    def on_search_changed(self, event=None):
        """搜索框内容改变时的处理"""
        search_text = self.search_var.get().upper()
        trading_type = self.trading_type_var.get()

        if search_text:
            # 过滤交易对
            filtered_pairs = [pair for pair in self.trading_pairs[trading_type]
                            if search_text in pair.upper()]
            self.symbol_combo['values'] = filtered_pairs
        else:
            self.symbol_combo['values'] = self.trading_pairs[trading_type]

    def update_symbol_list(self):
        """更新交易对列表"""
        trading_type = self.trading_type_var.get()
        self.symbol_combo['values'] = self.trading_pairs[trading_type]

        # 设置默认值
        if self.trading_pairs[trading_type]:
            self.symbol_var.set(self.trading_pairs[trading_type][0])

    def update_leverage_options(self):
        """更新杠杆选项"""
        symbol = self.symbol_var.get()
        trading_type = self.trading_type_var.get()

        if "现货" in trading_type:
            # 现货交易，无杠杆
            self.leverage_combo['values'] = ["1"]
            self.leverage_var.set("1")
            self.leverage_info_label.config(text="(现货无杠杆)")
            self.risk_label.config(text="")
        else:
            # 合约交易，有杠杆
            base_symbol = symbol.split('/')[0].split('-')[0] if symbol else "其他"

            if base_symbol in self.leverage_config:
                config = self.leverage_config[base_symbol]
            else:
                config = self.leverage_config["其他"]

            self.leverage_combo['values'] = [str(x) for x in config["recommended"]]
            self.leverage_var.set("1")
            self.leverage_info_label.config(text=f"(最大{config['max']}倍)")

            # 更新风险提示
            self.update_risk_warning()

    def update_risk_warning(self):
        """更新风险提示"""
        try:
            leverage = int(self.leverage_var.get())
            if leverage == 1:
                self.risk_label.config(text="")
            elif leverage <= 3:
                self.risk_label.config(text="⚠️ 低风险：建议新手使用", foreground="orange")
            elif leverage <= 10:
                self.risk_label.config(text="⚠️ 中等风险：需要一定经验", foreground="red")
            else:
                self.risk_label.config(text="🚨 高风险：仅限专业交易者", foreground="red")
        except:
            pass

    def update_price_display(self):
        """更新价格显示"""
        symbol = self.symbol_var.get()
        if symbol:
            # 这里可以添加实际的价格获取逻辑
            self.price_label.config(text=f"当前价格: {symbol} 加载中...")

            # 启动价格更新线程
            def update_price():
                try:
                    # 模拟价格获取
                    import random
                    import time
                    time.sleep(1)  # 模拟网络延迟

                    if "BTC" in symbol:
                        price = 45000 + random.randint(-2000, 2000)
                        change = random.uniform(-5, 5)
                    elif "ETH" in symbol:
                        price = 2500 + random.randint(-200, 200)
                        change = random.uniform(-8, 8)
                    else:
                        price = random.uniform(0.1, 100)
                        change = random.uniform(-10, 10)

                    color = "green" if change >= 0 else "red"
                    sign = "+" if change >= 0 else ""

                    self.price_label.config(
                        text=f"当前价格: ${price:.4f} ({sign}{change:.2f}%)",
                        foreground=color
                    )
                except:
                    self.price_label.config(text="价格获取失败", foreground="red")

            import threading
            threading.Thread(target=update_price, daemon=True).start()

    def update_position_info(self, info):
        """更新持仓信息显示"""
        self.position_info_text.config(state=tk.NORMAL)
        self.position_info_text.delete(1.0, tk.END)

        timestamp = datetime.now().strftime("%H:%M:%S")
        self.position_info_text.insert(tk.END, f"[{timestamp}] {info}\n")

        self.position_info_text.config(state=tk.DISABLED)
        self.position_info_text.see(tk.END)

    def start_strategy(self):
        """启动FMACD策略"""
        try:
            exchange = exchange_manager.get_exchange()
            if not exchange:
                messagebox.showerror("错误", "请先连接交易所")
                return

            # 验证输入参数
            if not self._validate_inputs():
                return

            # 获取参数
            symbol = self.symbol_var.get()
            fast_period = int(self.fast_period_var.get())
            slow_period = int(self.slow_period_var.get())
            signal_period = int(self.signal_period_var.get())
            atr_period = int(self.atr_period_var.get())
            position_size = float(self.position_size_var.get()) / 100
            stop_loss_pct = float(self.stop_loss_var.get()) / 100
            take_profit_pct = float(self.take_profit_var.get()) / 100
            daily_loss_limit = float(self.daily_loss_var.get()) / 100
            max_positions = int(self.max_positions_var.get())
            leverage = int(self.leverage_var.get())

            # 显示参数确认
            if not self._confirm_strategy_parameters(symbol, fast_period, slow_period, signal_period,
                                                   position_size, stop_loss_pct, take_profit_pct,
                                                   leverage, daily_loss_limit, max_positions):
                return

            # 创建增强版FMACD策略实例
            self.strategy = EnhancedFMACDStrategy(
                exchange=exchange,
                symbol=symbol,
                fast_period=fast_period,
                slow_period=slow_period,
                signal_period=signal_period,
                atr_period=atr_period,
                position_size=position_size,
                stop_loss_pct=stop_loss_pct,
                take_profit_pct=take_profit_pct,
                leverage=leverage,
                daily_loss_limit=daily_loss_limit,
                max_positions=max_positions
            )

            # 启动策略线程
            self.strategy_thread = threading.Thread(target=self.strategy.run_live)
            self.strategy_thread.daemon = True
            self.strategy_thread.start()

            self.running = True
            self.update_buttons()

            # 保存到主应用
            self.main_app.strategies['fmacd'] = self.strategy
            self.main_app.strategy_threads['fmacd'] = self.strategy_thread

            self.add_status_message("🚀 FMACD策略已启动")
            self.update_position_info("策略启动，等待信号...")

            # 启动状态监控
            self.start_status_monitor()

        except Exception as e:
            messagebox.showerror("错误", f"启动策略失败: {str(e)}")
            self.add_status_message(f"❌ 策略启动失败: {str(e)}")

    def _validate_inputs(self):
        """验证输入参数"""
        try:
            fast_period = int(self.fast_period_var.get())
            slow_period = int(self.slow_period_var.get())
            signal_period = int(self.signal_period_var.get())
            atr_period = int(self.atr_period_var.get())
            position_size = float(self.position_size_var.get())
            stop_loss = float(self.stop_loss_var.get())
            take_profit = float(self.take_profit_var.get())
            daily_loss = float(self.daily_loss_var.get())
            max_positions = int(self.max_positions_var.get())
            leverage = int(self.leverage_var.get())

            errors = []

            # FMACD参数验证
            if fast_period < 3 or fast_period > 20:
                errors.append("快速均线周期必须在3-20之间")

            if slow_period < 10 or slow_period > 50:
                errors.append("慢速均线周期必须在10-50之间")

            if fast_period >= slow_period:
                errors.append("快速周期必须小于慢速周期")

            if signal_period < 5 or signal_period > 15:
                errors.append("信号线周期必须在5-15之间")

            if atr_period < 7 or atr_period > 30:
                errors.append("ATR周期必须在7-30之间")

            # 交易参数验证
            if position_size < 1 or position_size > 50:
                errors.append("仓位大小必须在1%-50%之间")

            if stop_loss < 0.5 or stop_loss > 10:
                errors.append("止损百分比必须在0.5%-10%之间")

            if take_profit < 1 or take_profit > 20:
                errors.append("止盈百分比必须在1%-20%之间")

            # 风险管理验证
            if daily_loss < 1 or daily_loss > 20:
                errors.append("最大日损失必须在1%-20%之间")

            if max_positions < 1 or max_positions > 10:
                errors.append("最大持仓数量必须在1-10之间")

            # 杠杆验证
            symbol = self.symbol_var.get()
            base_symbol = symbol.split('/')[0].split('-')[0] if symbol else "其他"

            if base_symbol in self.leverage_config:
                max_leverage = self.leverage_config[base_symbol]["max"]
            else:
                max_leverage = self.leverage_config["其他"]["max"]

            if leverage < 1 or leverage > max_leverage:
                errors.append(f"杠杆倍数必须在1-{max_leverage}之间")

            if errors:
                messagebox.showerror("参数验证失败", "\n".join(errors))
                return False

            return True

        except ValueError as e:
            messagebox.showerror("参数验证失败", f"参数格式错误: {str(e)}")
            return False
        except Exception as e:
            messagebox.showerror("参数验证失败", f"验证过程出错: {str(e)}")
            return False

    def _confirm_strategy_parameters(self, symbol, fast_period, slow_period, signal_period,
                                   position_size, stop_loss_pct, take_profit_pct,
                                   leverage, daily_loss_limit, max_positions):
        """确认策略参数"""
        trading_type = "合约交易" if "-SWAP" in symbol else "现货交易"

        confirm_message = f"""
🚀 请确认FMACD策略参数：

📊 交易设置:
   • 交易对: {symbol} ({trading_type})
   • 杠杆倍数: {leverage}x
   • 仓位大小: {position_size*100:.1f}%

📈 FMACD参数:
   • 快速均线: {fast_period}日
   • 慢速均线: {slow_period}日
   • 信号线: {signal_period}日

💰 风险控制:
   • 止损: {stop_loss_pct*100:.1f}%
   • 止盈: {take_profit_pct*100:.1f}%
   • 日损失限制: {daily_loss_limit*100:.1f}%
   • 最大持仓: {max_positions}个

🎯 策略特点：
   • 金叉/死叉信号识别
   • 背离检测功能
   • 多周期共振验证
   • 动态止损机制

⚠️ 风险提示：
   • 杠杆交易风险较高，请谨慎操作
   • 建议先在模拟模式下测试
   • 严格遵守风险管理规则

是否确认启动FMACD策略？
        """.strip()

        return messagebox.askyesno("确认策略参数", confirm_message)

    def stop_strategy(self):
        """停止FMACD策略"""
        try:
            if self.strategy:
                self.strategy.stop()
                self.running = False
                self.update_buttons()

                self.add_status_message("⏹️ FMACD策略已停止")
                self.update_position_info("策略已停止")

                # 从主应用中移除
                if 'fmacd' in self.main_app.strategies:
                    del self.main_app.strategies['fmacd']
                if 'fmacd' in self.main_app.strategy_threads:
                    del self.main_app.strategy_threads['fmacd']

        except Exception as e:
            messagebox.showerror("错误", f"停止策略失败: {str(e)}")
            self.add_status_message(f"❌ 停止策略失败: {str(e)}")

    def update_buttons(self):
        """更新按钮状态"""
        if self.running:
            self.start_btn.config(state=tk.DISABLED)
            self.stop_btn.config(state=tk.NORMAL)
        else:
            self.start_btn.config(state=tk.NORMAL)
            self.stop_btn.config(state=tk.DISABLED)

    def start_status_monitor(self):
        """启动状态监控"""
        def monitor_loop():
            while self.running and self.strategy:
                try:
                    # 获取策略状态
                    if hasattr(self.strategy, 'get_status'):
                        status = self.strategy.get_status()

                        # 更新FMACD指标显示
                        if 'fmacd_data' in status:
                            fmacd_data = status['fmacd_data']
                            self.add_status_message(
                                f"📊 FMACD: DIF={fmacd_data.get('dif', 0):.6f}, "
                                f"DEA={fmacd_data.get('dea', 0):.6f}, "
                                f"MACD={fmacd_data.get('macd', 0):.6f}"
                            )

                        # 更新持仓信息
                        if 'position' in status:
                            position_info = status['position']
                            self.update_position_info(
                                f"持仓: {position_info.get('side', '无')} "
                                f"数量: {position_info.get('size', 0)} "
                                f"盈亏: {position_info.get('pnl', 0):.2f}%"
                            )

                        # 更新信号信息
                        if 'latest_signal' in status:
                            signal = status['latest_signal']
                            if signal:
                                self.add_status_message(f"🎯 信号: {signal}")

                    time.sleep(5)  # 每5秒更新一次

                except Exception as e:
                    self.add_status_message(f"⚠️ 监控错误: {str(e)}")
                    time.sleep(10)

        monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        monitor_thread.start()

    def run_backtest(self):
        """运行回测"""
        try:
            if not self._validate_inputs():
                return

            self.add_status_message("📊 开始回测...")

            # 获取参数
            symbol = self.symbol_var.get()
            fast_period = int(self.fast_period_var.get())
            slow_period = int(self.slow_period_var.get())
            signal_period = int(self.signal_period_var.get())

            def backtest_thread():
                try:
                    # 创建临时策略实例进行回测
                    from strategies import FMACDStrategy
                    temp_strategy = FMACDStrategy(
                        exchange=None,  # 回测不需要真实交易所
                        symbol=symbol,
                        fast_period=fast_period,
                        slow_period=slow_period,
                        signal_period=signal_period,
                        amount=100  # 回测用固定数量
                    )

                    # 运行回测
                    if hasattr(temp_strategy, 'run_backtest'):
                        results = temp_strategy.run_backtest()

                        # 显示回测结果
                        self.add_status_message("📈 回测完成！")
                        self.add_status_message(f"总收益率: {results.get('total_return', 0):.2f}%")
                        self.add_status_message(f"胜率: {results.get('win_rate', 0):.1f}%")
                        self.add_status_message(f"最大回撤: {results.get('max_drawdown', 0):.2f}%")
                        self.add_status_message(f"交易次数: {results.get('total_trades', 0)}")
                    else:
                        self.add_status_message("⚠️ 该策略暂不支持回测功能")

                except Exception as e:
                    self.add_status_message(f"❌ 回测失败: {str(e)}")

            threading.Thread(target=backtest_thread, daemon=True).start()

        except Exception as e:
            messagebox.showerror("错误", f"启动回测失败: {str(e)}")

    def save_config(self):
        """保存配置"""
        try:
            config = {
                'symbol': self.symbol_var.get(),
                'trading_type': self.trading_type_var.get(),
                'fast_period': self.fast_period_var.get(),
                'slow_period': self.slow_period_var.get(),
                'signal_period': self.signal_period_var.get(),
                'atr_period': self.atr_period_var.get(),
                'position_size': self.position_size_var.get(),
                'stop_loss': self.stop_loss_var.get(),
                'take_profit': self.take_profit_var.get(),
                'leverage': self.leverage_var.get(),
                'daily_loss': self.daily_loss_var.get(),
                'max_positions': self.max_positions_var.get()
            }

            from tkinter import filedialog
            filename = filedialog.asksaveasfilename(
                title="保存FMACD策略配置",
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )

            if filename:
                import json
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(config, f, indent=2, ensure_ascii=False)

                messagebox.showinfo("成功", f"配置已保存到: {filename}")
                self.add_status_message(f"💾 配置已保存: {filename}")

        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败: {str(e)}")

    def load_config(self):
        """加载配置"""
        try:
            from tkinter import filedialog
            filename = filedialog.askopenfilename(
                title="加载FMACD策略配置",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )

            if filename:
                import json
                with open(filename, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                # 应用配置
                if 'symbol' in config:
                    self.symbol_var.set(config['symbol'])
                if 'trading_type' in config:
                    self.trading_type_var.set(config['trading_type'])
                    self.update_symbol_list()
                if 'fast_period' in config:
                    self.fast_period_var.set(config['fast_period'])
                if 'slow_period' in config:
                    self.slow_period_var.set(config['slow_period'])
                if 'signal_period' in config:
                    self.signal_period_var.set(config['signal_period'])
                if 'atr_period' in config:
                    self.atr_period_var.set(config['atr_period'])
                if 'position_size' in config:
                    self.position_size_var.set(config['position_size'])
                if 'stop_loss' in config:
                    self.stop_loss_var.set(config['stop_loss'])
                if 'take_profit' in config:
                    self.take_profit_var.set(config['take_profit'])
                if 'leverage' in config:
                    self.leverage_var.set(config['leverage'])
                if 'daily_loss' in config:
                    self.daily_loss_var.set(config['daily_loss'])
                if 'max_positions' in config:
                    self.max_positions_var.set(config['max_positions'])

                self.update_leverage_options()
                messagebox.showinfo("成功", f"配置已从 {filename} 加载")
                self.add_status_message(f"📁 配置已加载: {filename}")

        except Exception as e:
            messagebox.showerror("错误", f"加载配置失败: {str(e)}")






    def add_status_message(self, message):
        """添加状态消息"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        full_message = f"[{timestamp}] {message}\n"

        self.status_text.config(state=tk.NORMAL)
        self.status_text.insert(tk.END, full_message)
        self.status_text.see(tk.END)
        self.status_text.config(state=tk.DISABLED)


# 增强版FMACD策略类
class EnhancedFMACDStrategy:
    """增强版FMACD策略类"""

    def __init__(self, exchange, symbol, fast_period=5, slow_period=20, signal_period=9,
                 atr_period=14, position_size=0.1, stop_loss_pct=0.02, take_profit_pct=0.06,
                 leverage=1, daily_loss_limit=0.05, max_positions=3):
        """
        初始化增强版FMACD策略

        Args:
            exchange: 交易所对象
            symbol: 交易对
            fast_period: 快速均线周期
            slow_period: 慢速均线周期
            signal_period: 信号线周期
            atr_period: ATR周期
            position_size: 仓位大小(比例)
            stop_loss_pct: 止损百分比
            take_profit_pct: 止盈百分比
            leverage: 杠杆倍数
            daily_loss_limit: 日损失限制
            max_positions: 最大持仓数量
        """
        self.exchange = exchange
        self.symbol = symbol
        self.fast_period = fast_period
        self.slow_period = slow_period
        self.signal_period = signal_period
        self.atr_period = atr_period
        self.position_size = position_size
        self.stop_loss_pct = stop_loss_pct
        self.take_profit_pct = take_profit_pct
        self.leverage = leverage
        self.daily_loss_limit = daily_loss_limit
        self.max_positions = max_positions

        self.running = False
        self.position = 0  # 0: 无仓位, 1: 多头, -1: 空头
        self.entry_price = 0
        self.daily_pnl = 0
        self.positions_count = 0

        # FMACD指标数据
        self.price_history = []
        self.dif_history = []
        self.dea_history = []
        self.macd_history = []

        from logger import get_logger
        self.logger = get_logger("enhanced_fmacd_strategy")

    def run_live(self):
        """运行实盘策略"""
        self.running = True
        self.logger.info(f"增强版FMACD策略启动: {self.symbol}")

        import time
        while self.running:
            try:
                # 获取市场数据
                market_data = self.get_market_data()
                if market_data is not None and len(market_data) > max(self.slow_period, self.atr_period):

                    # 计算FMACD指标
                    dif, dea, macd = self.calculate_fmacd(market_data['close'])

                    # 计算ATR
                    atr = self.calculate_atr(market_data)

                    # 生成交易信号
                    signal = self.generate_signals(dif, dea, macd, market_data)

                    # 执行交易
                    if signal:
                        self.execute_trade(signal, market_data['close'].iloc[-1], atr)

                    # 更新止损止盈
                    self.update_stop_loss_take_profit(market_data['close'].iloc[-1], atr)

                    # 风险管理检查
                    self.check_risk_limits()

                time.sleep(60)  # 每分钟检查一次

            except Exception as e:
                self.logger.error(f"策略运行错误: {e}")
                time.sleep(30)

    def get_market_data(self):
        """获取市场数据"""
        try:
            if hasattr(self.exchange, 'fetch_ohlcv'):
                import pandas as pd
                ohlcv = self.exchange.fetch_ohlcv(self.symbol, '1h', limit=200)
                df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
                df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                return df
            else:
                # 模拟数据
                import pandas as pd
                import numpy as np
                dates = pd.date_range(start='2024-01-01', periods=200, freq='1H')
                np.random.seed(42)
                base_price = 45000
                prices = []

                for i in range(200):
                    trend = 0.0001 * np.sin(i * 0.05)
                    noise = np.random.normal(0, 0.01)
                    base_price *= (1 + trend + noise)
                    prices.append(base_price)

                df = pd.DataFrame({
                    'timestamp': dates,
                    'open': prices,
                    'high': [p * (1 + abs(np.random.normal(0, 0.005))) for p in prices],
                    'low': [p * (1 - abs(np.random.normal(0, 0.005))) for p in prices],
                    'close': prices,
                    'volume': np.random.randint(1000, 10000, 200)
                })
                return df
        except Exception as e:
            self.logger.error(f"获取市场数据失败: {e}")
            return None

    def calculate_fmacd(self, prices):
        """计算FMACD指标"""
        import pandas as pd

        # 计算EMA
        fast_ema = prices.ewm(span=self.fast_period).mean()
        slow_ema = prices.ewm(span=self.slow_period).mean()

        # 计算DIF
        dif = fast_ema - slow_ema

        # 计算DEA (DIF的EMA)
        dea = dif.ewm(span=self.signal_period).mean()

        # 计算MACD柱状线
        macd = (dif - dea) * 2

        # 更新历史数据
        if len(dif) > 0:
            self.dif_history.append(dif.iloc[-1])
            self.dea_history.append(dea.iloc[-1])
            self.macd_history.append(macd.iloc[-1])

            # 保持历史数据长度
            if len(self.dif_history) > 100:
                self.dif_history.pop(0)
                self.dea_history.pop(0)
                self.macd_history.pop(0)

        return dif, dea, macd

    def calculate_atr(self, market_data):
        """计算ATR指标"""
        import pandas as pd

        high = market_data['high']
        low = market_data['low']
        close = market_data['close']

        # 计算真实范围
        tr1 = high - low
        tr2 = abs(high - close.shift(1))
        tr3 = abs(low - close.shift(1))

        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = tr.rolling(window=self.atr_period).mean()

        return atr.iloc[-1] if len(atr) > 0 else 0

    def generate_signals(self, dif, dea, macd, market_data):
        """生成交易信号"""
        if len(dif) < 2 or len(dea) < 2:
            return None

        current_dif = dif.iloc[-1]
        current_dea = dea.iloc[-1]
        prev_dif = dif.iloc[-2]
        prev_dea = dea.iloc[-2]

        # 金叉信号 (买入)
        if prev_dif <= prev_dea and current_dif > current_dea:
            if current_dif > 0:  # 零轴上方，强势信号
                return {"type": "BUY", "strength": "STRONG", "reason": "金叉(零轴上方)"}
            else:  # 零轴下方，弱势信号
                return {"type": "BUY", "strength": "WEAK", "reason": "金叉(零轴下方)"}

        # 死叉信号 (卖出)
        if prev_dif >= prev_dea and current_dif < current_dea:
            return {"type": "SELL", "strength": "STRONG", "reason": "死叉"}

        # 背离检测
        divergence = self.detect_divergence(market_data['close'], dif)
        if divergence:
            return {"type": divergence["type"], "strength": "MEDIUM", "reason": f"背离({divergence['type']})"}

        return None

    def detect_divergence(self, prices, dif):
        """检测背离"""
        if len(prices) < 20 or len(dif) < 20:
            return None

        # 简化的背离检测逻辑
        recent_prices = prices.tail(10)
        recent_dif = dif.tail(10)

        price_trend = recent_prices.iloc[-1] - recent_prices.iloc[0]
        dif_trend = recent_dif.iloc[-1] - recent_dif.iloc[0]

        # 顶背离：价格上涨但DIF下降
        if price_trend > 0 and dif_trend < 0:
            return {"type": "SELL", "reason": "顶背离"}

        # 底背离：价格下跌但DIF上升
        if price_trend < 0 and dif_trend > 0:
            return {"type": "BUY", "reason": "底背离"}

        return None

    def execute_trade(self, signal, current_price, atr):
        """执行交易"""
        if self.positions_count >= self.max_positions:
            self.logger.info("已达到最大持仓数量限制")
            return

        try:
            if signal["type"] == "BUY" and self.position <= 0:
                # 开多仓
                self.position = 1
                self.entry_price = current_price
                self.positions_count += 1
                self.logger.info(f"开多仓: {current_price}, 原因: {signal['reason']}")

            elif signal["type"] == "SELL" and self.position >= 0:
                # 开空仓或平多仓
                if self.position > 0:
                    # 平多仓
                    pnl = (current_price - self.entry_price) / self.entry_price * 100
                    self.daily_pnl += pnl
                    self.logger.info(f"平多仓: {current_price}, 盈亏: {pnl:.2f}%")
                    self.position = 0
                    self.positions_count -= 1
                else:
                    # 开空仓
                    self.position = -1
                    self.entry_price = current_price
                    self.positions_count += 1
                    self.logger.info(f"开空仓: {current_price}, 原因: {signal['reason']}")

        except Exception as e:
            self.logger.error(f"执行交易失败: {e}")

    def update_stop_loss_take_profit(self, current_price, atr):
        """更新止损止盈"""
        if self.position == 0:
            return

        try:
            if self.position > 0:  # 多头仓位
                # 计算止损止盈价格
                stop_loss_price = self.entry_price * (1 - self.stop_loss_pct)
                take_profit_price = self.entry_price * (1 + self.take_profit_pct)

                # 动态止损 (基于ATR)
                atr_stop_loss = self.entry_price - (atr * 1.5)
                stop_loss_price = max(stop_loss_price, atr_stop_loss)

                if current_price <= stop_loss_price:
                    # 触发止损
                    pnl = (current_price - self.entry_price) / self.entry_price * 100
                    self.daily_pnl += pnl
                    self.logger.info(f"止损平多仓: {current_price}, 盈亏: {pnl:.2f}%")
                    self.position = 0
                    self.positions_count -= 1

                elif current_price >= take_profit_price:
                    # 触发止盈
                    pnl = (current_price - self.entry_price) / self.entry_price * 100
                    self.daily_pnl += pnl
                    self.logger.info(f"止盈平多仓: {current_price}, 盈亏: {pnl:.2f}%")
                    self.position = 0
                    self.positions_count -= 1

            elif self.position < 0:  # 空头仓位
                # 计算止损止盈价格
                stop_loss_price = self.entry_price * (1 + self.stop_loss_pct)
                take_profit_price = self.entry_price * (1 - self.take_profit_pct)

                # 动态止损 (基于ATR)
                atr_stop_loss = self.entry_price + (atr * 1.5)
                stop_loss_price = min(stop_loss_price, atr_stop_loss)

                if current_price >= stop_loss_price:
                    # 触发止损
                    pnl = (self.entry_price - current_price) / self.entry_price * 100
                    self.daily_pnl += pnl
                    self.logger.info(f"止损平空仓: {current_price}, 盈亏: {pnl:.2f}%")
                    self.position = 0
                    self.positions_count -= 1

                elif current_price <= take_profit_price:
                    # 触发止盈
                    pnl = (self.entry_price - current_price) / self.entry_price * 100
                    self.daily_pnl += pnl
                    self.logger.info(f"止盈平空仓: {current_price}, 盈亏: {pnl:.2f}%")
                    self.position = 0
                    self.positions_count -= 1

        except Exception as e:
            self.logger.error(f"更新止损止盈失败: {e}")

    def check_risk_limits(self):
        """检查风险限制"""
        # 检查日损失限制
        if self.daily_pnl < -self.daily_loss_limit * 100:
            self.logger.warning(f"触发日损失限制: {self.daily_pnl:.2f}%")
            self.stop()

    def get_status(self):
        """获取策略状态"""
        return {
            'position': {
                'side': '多头' if self.position > 0 else ('空头' if self.position < 0 else '无'),
                'size': abs(self.position),
                'pnl': self.daily_pnl
            },
            'fmacd_data': {
                'dif': self.dif_history[-1] if self.dif_history else 0,
                'dea': self.dea_history[-1] if self.dea_history else 0,
                'macd': self.macd_history[-1] if self.macd_history else 0
            },
            'latest_signal': f"DIF: {self.dif_history[-1]:.6f}, DEA: {self.dea_history[-1]:.6f}" if self.dif_history and self.dea_history else "计算中..."
        }

    def stop(self):
        """停止策略"""
        self.running = False
        self.logger.info("增强版FMACD策略已停止")
