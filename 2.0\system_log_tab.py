#!/usr/bin/env python3
"""
系统日志标签页
集中显示和管理系统运行日志
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import queue
import time
from datetime import datetime
import os
import logging
from logger import add_gui_log_handler, remove_gui_log_handler

class SystemLogTab:
    """系统日志标签页"""
    
    def __init__(self, parent, main_app):
        self.parent = parent
        self.main_app = main_app
        
        # 日志相关属性
        self.log_queue = queue.Queue()
        self.log_entries = []
        self.max_log_entries = 1000
        self.auto_scroll = True
        
        # 日志级别过滤
        self.log_levels = {
            'DEBUG': {'show': True, 'color': 'gray'},
            'INFO': {'show': True, 'color': 'black'},
            'WARNING': {'show': True, 'color': 'orange'},
            'ERROR': {'show': True, 'color': 'red'},
            'CRITICAL': {'show': True, 'color': 'purple'}
        }
        
        # 统计信息
        self.log_stats = {
            'total': 0,
            'info': 0,
            'warning': 0,
            'error': 0,
            'critical': 0
        }
        
        self.create_widgets()

        # 延迟设置日志处理器，确保GUI完全初始化
        self.parent.after(100, self.delayed_setup)

    def delayed_setup(self):
        """延迟设置，确保GUI完全初始化后再设置日志处理器"""
        try:
            self.setup_log_handler()
            self.start_log_processor()
            # 注册到全局日志系统
            add_gui_log_handler(self.log_handler)
        except Exception as e:
            print(f"日志系统初始化错误: {e}")

    def create_widgets(self):
        """创建界面控件"""
        # 主框架
        self.frame = ttk.Frame(self.parent)
        
        # 顶部控制区域
        control_frame = ttk.Frame(self.frame)
        control_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 左侧按钮组
        left_buttons = ttk.Frame(control_frame)
        left_buttons.pack(side=tk.LEFT)
        
        ttk.Button(left_buttons, text="清空日志", command=self.clear_logs).pack(side=tk.LEFT, padx=2)
        ttk.Button(left_buttons, text="保存日志", command=self.save_logs).pack(side=tk.LEFT, padx=2)
        ttk.Button(left_buttons, text="刷新", command=self.refresh_logs).pack(side=tk.LEFT, padx=2)
        
        # 分隔符
        ttk.Separator(left_buttons, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
        
        # 自动滚动选项
        self.auto_scroll_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(left_buttons, text="自动滚动", variable=self.auto_scroll_var,
                       command=self.toggle_auto_scroll).pack(side=tk.LEFT, padx=2)
        
        # 右侧过滤区域
        filter_frame = ttk.LabelFrame(control_frame, text="日志级别过滤")
        filter_frame.pack(side=tk.RIGHT, padx=5)
        
        # 日志级别复选框
        self.level_vars = {}
        for level, config in self.log_levels.items():
            var = tk.BooleanVar(value=config['show'])
            self.level_vars[level] = var
            cb = ttk.Checkbutton(filter_frame, text=level, variable=var,
                               command=self.apply_filter)
            cb.pack(side=tk.LEFT, padx=2)
        
        # 日志显示区域
        log_frame = ttk.LabelFrame(self.frame, text="系统日志")
        log_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建Text控件和滚动条
        text_frame = ttk.Frame(log_frame)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.log_text = tk.Text(text_frame, wrap=tk.WORD, state=tk.DISABLED,
                               font=('Consolas', 9), bg='white', fg='black')
        
        # 垂直滚动条
        v_scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=v_scrollbar.set)
        
        # 水平滚动条
        h_scrollbar = ttk.Scrollbar(text_frame, orient=tk.HORIZONTAL, command=self.log_text.xview)
        self.log_text.configure(xscrollcommand=h_scrollbar.set)
        
        # 布局
        self.log_text.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')
        
        text_frame.grid_rowconfigure(0, weight=1)
        text_frame.grid_columnconfigure(0, weight=1)
        
        # 配置文本标签颜色
        for level, config in self.log_levels.items():
            self.log_text.tag_configure(level.lower(), foreground=config['color'])
        
        # 底部状态栏
        status_frame = ttk.Frame(self.frame)
        status_frame.pack(fill=tk.X, padx=5, pady=2)
        
        # 统计信息标签
        self.stats_label = ttk.Label(status_frame, text="日志统计: 总计 0 条")
        self.stats_label.pack(side=tk.LEFT)
        
        # 时间标签
        self.time_label = ttk.Label(status_frame, text="")
        self.time_label.pack(side=tk.RIGHT)
        
        # 更新时间显示
        self.update_time_display()
    
    def setup_log_handler(self):
        """设置日志处理器"""
        # 创建自定义日志处理器
        self.log_handler = QueueLogHandler(self.log_queue)
        self.log_handler.setLevel(logging.DEBUG)
        
        # 设置日志格式
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%H:%M:%S'
        )
        self.log_handler.setFormatter(formatter)
        
        # 不在这里直接添加到根日志记录器
        # 将在delayed_setup中通过add_gui_log_handler添加
    
    def start_log_processor(self):
        """启动日志处理线程"""
        def process_logs():
            while True:
                try:
                    # 从队列获取日志记录
                    record = self.log_queue.get(timeout=1)
                    if record is None:
                        break
                    
                    # 在主线程中更新GUI
                    self.frame.after(0, self.add_log_entry, record)
                    
                except queue.Empty:
                    continue
                except Exception as e:
                    print(f"日志处理错误: {e}")
        
        self.log_thread = threading.Thread(target=process_logs, daemon=True)
        self.log_thread.start()
    
    def add_log_entry(self, record):
        """添加日志条目"""
        try:
            # 格式化日志消息
            timestamp = datetime.fromtimestamp(record.created).strftime('%H:%M:%S')
            level = record.levelname
            logger_name = record.name
            message = record.getMessage()
            
            # 创建日志条目
            log_entry = {
                'timestamp': timestamp,
                'level': level,
                'logger': logger_name,
                'message': message,
                'full_text': f"[{timestamp}] {level} - {logger_name}: {message}"
            }
            
            # 添加到日志列表
            self.log_entries.append(log_entry)
            
            # 限制日志条数
            if len(self.log_entries) > self.max_log_entries:
                self.log_entries.pop(0)
            
            # 更新统计信息
            self.update_stats(level)
            
            # 如果该级别的日志被过滤，则不显示
            if not self.level_vars.get(level, tk.BooleanVar(value=True)).get():
                return
            
            # 在Text控件中显示
            self.log_text.config(state=tk.NORMAL)
            
            # 插入日志文本
            self.log_text.insert(tk.END, log_entry['full_text'] + '\n')
            
            # 应用颜色标签
            line_start = self.log_text.index(f"{tk.END}-2l linestart")
            line_end = self.log_text.index(f"{tk.END}-2l lineend")
            self.log_text.tag_add(level.lower(), line_start, line_end)
            
            self.log_text.config(state=tk.DISABLED)
            
            # 自动滚动到底部
            if self.auto_scroll_var.get():
                self.log_text.see(tk.END)
            
        except Exception as e:
            print(f"添加日志条目错误: {e}")
    
    def update_stats(self, level):
        """更新统计信息"""
        self.log_stats['total'] += 1
        
        if level == 'INFO':
            self.log_stats['info'] += 1
        elif level == 'WARNING':
            self.log_stats['warning'] += 1
        elif level == 'ERROR':
            self.log_stats['error'] += 1
        elif level == 'CRITICAL':
            self.log_stats['critical'] += 1
        
        # 更新状态栏
        stats_text = (f"日志统计: 总计 {self.log_stats['total']} 条 | "
                     f"信息 {self.log_stats['info']} | "
                     f"警告 {self.log_stats['warning']} | "
                     f"错误 {self.log_stats['error']}")
        
        if self.log_stats['critical'] > 0:
            stats_text += f" | 严重 {self.log_stats['critical']}"
        
        self.stats_label.config(text=stats_text)
    
    def clear_logs(self):
        """清空日志"""
        if messagebox.askyesno("确认清空", "确定要清空所有日志吗？"):
            self.log_text.config(state=tk.NORMAL)
            self.log_text.delete(1.0, tk.END)
            self.log_text.config(state=tk.DISABLED)
            
            # 清空日志列表和统计
            self.log_entries.clear()
            self.log_stats = {key: 0 for key in self.log_stats}
            self.stats_label.config(text="日志统计: 总计 0 条")
    
    def save_logs(self):
        """保存日志到文件"""
        if not self.log_entries:
            messagebox.showinfo("提示", "没有日志可保存")
            return
        
        # 选择保存文件
        filename = filedialog.asksaveasfilename(
            title="保存日志文件",
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")],
            initialname=f"system_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        )
        
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(f"量化交易系统日志\n")
                    f.write(f"导出时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write(f"日志条数: {len(self.log_entries)}\n")
                    f.write("=" * 80 + "\n\n")
                    
                    for entry in self.log_entries:
                        f.write(entry['full_text'] + '\n')
                
                messagebox.showinfo("保存成功", f"日志已保存到: {filename}")
                
            except Exception as e:
                messagebox.showerror("保存失败", f"保存日志文件失败: {str(e)}")
    
    def refresh_logs(self):
        """刷新日志显示"""
        self.apply_filter()
    
    def apply_filter(self):
        """应用日志级别过滤"""
        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete(1.0, tk.END)
        
        # 重新显示符合过滤条件的日志
        for entry in self.log_entries:
            level = entry['level']
            if self.level_vars.get(level, tk.BooleanVar(value=True)).get():
                self.log_text.insert(tk.END, entry['full_text'] + '\n')
                
                # 应用颜色标签
                line_start = self.log_text.index(f"{tk.END}-2l linestart")
                line_end = self.log_text.index(f"{tk.END}-2l lineend")
                self.log_text.tag_add(level.lower(), line_start, line_end)
        
        self.log_text.config(state=tk.DISABLED)
        
        # 滚动到底部
        if self.auto_scroll_var.get():
            self.log_text.see(tk.END)
    
    def toggle_auto_scroll(self):
        """切换自动滚动"""
        self.auto_scroll = self.auto_scroll_var.get()
        if self.auto_scroll:
            self.log_text.see(tk.END)
    
    def update_time_display(self):
        """更新时间显示"""
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        self.time_label.config(text=f"当前时间: {current_time}")
        
        # 每秒更新一次
        self.frame.after(1000, self.update_time_display)

    def cleanup(self):
        """清理资源"""
        # 从全局日志系统中移除处理器
        remove_gui_log_handler(self.log_handler)

        # 停止日志处理线程
        self.log_queue.put(None)


class QueueLogHandler(logging.Handler):
    """队列日志处理器"""
    
    def __init__(self, log_queue):
        super().__init__()
        self.log_queue = log_queue
    
    def emit(self, record):
        """发送日志记录到队列"""
        try:
            # 检查队列是否可用，避免在GUI未初始化时出错
            if self.log_queue is not None:
                self.log_queue.put(record)
        except Exception as e:
            # 避免递归错误，直接打印到控制台
            print(f"日志处理错误: {e}")
            pass
