#!/usr/bin/env python3
"""
错误处理中心
统一管理错误消息和处理逻辑，提供中文错误提示
"""

import traceback
from typing import Dict, List, Optional, Any
from datetime import datetime
from logger import get_logger

class ErrorCode:
    """错误代码常量"""
    
    # 网络相关错误
    NETWORK_CONNECTION_FAILED = "NETWORK_001"
    NETWORK_TIMEOUT = "NETWORK_002"
    NETWORK_INTERRUPTED = "NETWORK_003"
    DNS_RESOLUTION_FAILED = "NETWORK_004"
    
    # API相关错误
    API_KEY_INVALID = "API_001"
    API_SECRET_INVALID = "API_002"
    API_RATE_LIMIT = "API_003"
    API_MAINTENANCE = "API_004"
    API_PERMISSION_DENIED = "API_005"
    
    # 交易相关错误
    INSUFFICIENT_BALANCE = "TRADE_001"
    ORDER_NOT_FOUND = "TRADE_002"
    ORDER_ALREADY_FILLED = "TRADE_003"
    ORDER_CREATION_FAILED = "TRADE_004"
    INVALID_SYMBOL = "TRADE_005"
    MARKET_CLOSED = "TRADE_006"
    
    # 参数验证错误
    INVALID_PARAMETER = "PARAM_001"
    PARAMETER_OUT_OF_RANGE = "PARAM_002"
    MISSING_PARAMETER = "PARAM_003"
    PARAMETER_TYPE_ERROR = "PARAM_004"
    
    # 策略相关错误
    STRATEGY_INIT_FAILED = "STRATEGY_001"
    STRATEGY_RUNTIME_ERROR = "STRATEGY_002"
    STRATEGY_CONFIG_ERROR = "STRATEGY_003"
    
    # 系统相关错误
    SYSTEM_ERROR = "SYSTEM_001"
    MEMORY_ERROR = "SYSTEM_002"
    FILE_ERROR = "SYSTEM_003"
    DATABASE_ERROR = "SYSTEM_004"

class ErrorHandler:
    """错误处理中心"""
    
    def __init__(self):
        self.logger = get_logger("error_handler")
        
        # 错误消息模板
        self.error_messages = {
            # 网络错误
            ErrorCode.NETWORK_CONNECTION_FAILED: {
                "title": "网络连接失败",
                "message": "无法连接到交易所服务器",
                "suggestions": [
                    "检查网络连接是否正常",
                    "确认交易所服务器状态",
                    "尝试切换网络或使用VPN",
                    "稍后重试连接"
                ]
            },
            ErrorCode.NETWORK_TIMEOUT: {
                "title": "网络连接超时",
                "message": "连接交易所服务器超时",
                "suggestions": [
                    "检查网络速度是否正常",
                    "尝试增加超时时间设置",
                    "切换到更稳定的网络环境",
                    "联系网络服务提供商"
                ]
            },
            ErrorCode.NETWORK_INTERRUPTED: {
                "title": "网络连接中断",
                "message": "与交易所的连接被中断",
                "suggestions": [
                    "检查网络连接稳定性",
                    "系统将自动尝试重连",
                    "如持续出现，请检查防火墙设置",
                    "考虑使用有线网络连接"
                ]
            },
            ErrorCode.DNS_RESOLUTION_FAILED: {
                "title": "域名解析失败",
                "message": "无法解析交易所域名",
                "suggestions": [
                    "检查DNS设置",
                    "尝试使用公共DNS (8.8.8.8)",
                    "检查网络防火墙设置",
                    "联系网络管理员"
                ]
            },
            
            # API错误
            ErrorCode.API_KEY_INVALID: {
                "title": "API密钥无效",
                "message": "提供的API密钥无效或已过期",
                "suggestions": [
                    "检查API密钥是否正确输入",
                    "确认API密钥未过期",
                    "在交易所重新生成API密钥",
                    "检查API密钥权限设置"
                ]
            },
            ErrorCode.API_SECRET_INVALID: {
                "title": "API密钥验证失败",
                "message": "API密钥验证失败，请检查密钥配置",
                "suggestions": [
                    "检查API Secret是否正确",
                    "确认API密钥对应关系",
                    "检查时间同步设置",
                    "重新配置API密钥"
                ]
            },
            ErrorCode.API_RATE_LIMIT: {
                "title": "API调用频率限制",
                "message": "API调用过于频繁，触发限流保护",
                "suggestions": [
                    "降低策略运行频率",
                    "等待限流解除后重试",
                    "优化API调用逻辑",
                    "考虑升级API权限等级"
                ]
            },
            ErrorCode.API_MAINTENANCE: {
                "title": "交易所维护中",
                "message": "交易所正在进行系统维护",
                "suggestions": [
                    "等待维护完成后重试",
                    "关注交易所官方公告",
                    "暂停自动交易策略",
                    "考虑使用其他交易所"
                ]
            },
            
            # 交易错误
            ErrorCode.INSUFFICIENT_BALANCE: {
                "title": "余额不足",
                "message": "账户余额不足以完成此次交易",
                "suggestions": [
                    "检查账户余额是否充足",
                    "减少交易数量",
                    "充值到交易账户",
                    "检查资金是否被其他订单占用"
                ]
            },
            ErrorCode.ORDER_NOT_FOUND: {
                "title": "订单不存在",
                "message": "指定的订单不存在或已被删除",
                "suggestions": [
                    "检查订单ID是否正确",
                    "确认订单未被手动取消",
                    "刷新订单状态",
                    "检查订单历史记录"
                ]
            },
            ErrorCode.ORDER_ALREADY_FILLED: {
                "title": "订单已成交",
                "message": "订单已完全成交，无法取消",
                "suggestions": [
                    "检查订单当前状态",
                    "确认交易是否符合预期",
                    "更新策略状态",
                    "记录交易结果"
                ]
            },
            ErrorCode.INVALID_SYMBOL: {
                "title": "交易对无效",
                "message": "指定的交易对不存在或不支持",
                "suggestions": [
                    "检查交易对拼写是否正确",
                    "确认交易所支持该交易对",
                    "查看可用交易对列表",
                    "使用标准格式 (如 CFX/USDT)"
                ]
            },
            
            # 参数错误
            ErrorCode.INVALID_PARAMETER: {
                "title": "参数无效",
                "message": "提供的参数格式或值无效",
                "suggestions": [
                    "检查参数格式是否正确",
                    "确认参数值在有效范围内",
                    "参考参数说明文档",
                    "使用推荐的参数值"
                ]
            },
            ErrorCode.PARAMETER_OUT_OF_RANGE: {
                "title": "参数超出范围",
                "message": "参数值超出允许的范围",
                "suggestions": [
                    "调整参数到有效范围内",
                    "查看参数限制说明",
                    "使用系统推荐值",
                    "联系技术支持获取帮助"
                ]
            },
            
            # 策略错误
            ErrorCode.STRATEGY_INIT_FAILED: {
                "title": "策略初始化失败",
                "message": "策略初始化过程中发生错误",
                "suggestions": [
                    "检查策略参数配置",
                    "确认交易所连接正常",
                    "检查账户权限设置",
                    "重新启动策略"
                ]
            },
            ErrorCode.STRATEGY_RUNTIME_ERROR: {
                "title": "策略运行错误",
                "message": "策略运行过程中发生异常",
                "suggestions": [
                    "检查市场数据是否正常",
                    "确认网络连接稳定",
                    "检查策略逻辑设置",
                    "暂停策略并检查日志"
                ]
            },
            
            # 系统错误
            ErrorCode.SYSTEM_ERROR: {
                "title": "系统错误",
                "message": "系统发生未知错误",
                "suggestions": [
                    "重启应用程序",
                    "检查系统资源使用情况",
                    "查看详细错误日志",
                    "联系技术支持"
                ]
            }
        }
    
    def handle_error(self, error: Exception, error_code: str = None, 
                    context: Dict[str, Any] = None) -> Dict[str, Any]:
        """处理错误并返回用户友好的错误信息"""
        
        # 确定错误代码
        if error_code is None:
            error_code = self._determine_error_code(error)
        
        # 获取错误信息模板
        error_info = self.error_messages.get(error_code, {
            "title": "未知错误",
            "message": str(error),
            "suggestions": ["请联系技术支持获取帮助"]
        })
        
        # 构建完整的错误信息
        error_details = {
            "error_code": error_code,
            "title": error_info["title"],
            "message": error_info["message"],
            "original_error": str(error),
            "suggestions": error_info["suggestions"],
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "context": context or {}
        }
        
        # 记录错误日志
        self._log_error(error_details, error)
        
        return error_details
    
    def _determine_error_code(self, error: Exception) -> str:
        """根据异常类型确定错误代码"""
        error_str = str(error).lower()
        error_type = type(error).__name__
        
        # 网络相关错误
        if "connection" in error_str and "failed" in error_str:
            return ErrorCode.NETWORK_CONNECTION_FAILED
        elif "timeout" in error_str:
            return ErrorCode.NETWORK_TIMEOUT
        elif "connection" in error_str and ("reset" in error_str or "interrupted" in error_str):
            return ErrorCode.NETWORK_INTERRUPTED
        elif "resolution" in error_str or "dns" in error_str:
            return ErrorCode.DNS_RESOLUTION_FAILED
        
        # API相关错误
        elif "api key" in error_str or "invalid key" in error_str:
            return ErrorCode.API_KEY_INVALID
        elif "authentication" in error_str or "unauthorized" in error_str:
            return ErrorCode.API_SECRET_INVALID
        elif "rate limit" in error_str or "too many requests" in error_str:
            return ErrorCode.API_RATE_LIMIT
        elif "maintenance" in error_str or "unavailable" in error_str:
            return ErrorCode.API_MAINTENANCE
        
        # 交易相关错误
        elif "insufficient" in error_str and "balance" in error_str:
            return ErrorCode.INSUFFICIENT_BALANCE
        elif "order not found" in error_str:
            return ErrorCode.ORDER_NOT_FOUND
        elif "already filled" in error_str or "already executed" in error_str:
            return ErrorCode.ORDER_ALREADY_FILLED
        elif "invalid symbol" in error_str:
            return ErrorCode.INVALID_SYMBOL
        
        # 参数相关错误
        elif error_type in ["ValueError", "TypeError"]:
            return ErrorCode.INVALID_PARAMETER
        
        # 默认系统错误
        else:
            return ErrorCode.SYSTEM_ERROR
    
    def _log_error(self, error_details: Dict[str, Any], original_error: Exception):
        """记录错误日志"""
        self.logger.error(f"错误处理: {error_details['title']} - {error_details['message']}")
        self.logger.error(f"错误代码: {error_details['error_code']}")
        self.logger.error(f"原始错误: {original_error}")
        
        if error_details['context']:
            self.logger.error(f"错误上下文: {error_details['context']}")
        
        # 记录堆栈跟踪
        self.logger.debug(f"堆栈跟踪: {traceback.format_exc()}")
    
    def format_user_message(self, error_details: Dict[str, Any]) -> str:
        """格式化用户友好的错误消息"""
        message = f"❌ {error_details['title']}\n\n"
        message += f"📝 详细信息: {error_details['message']}\n\n"
        
        if error_details['suggestions']:
            message += "💡 解决建议:\n"
            for i, suggestion in enumerate(error_details['suggestions'], 1):
                message += f"   {i}. {suggestion}\n"
        
        message += f"\n🕒 发生时间: {error_details['timestamp']}"
        message += f"\n🔍 错误代码: {error_details['error_code']}"
        
        return message
    
    def get_error_statistics(self) -> Dict[str, int]:
        """获取错误统计信息"""
        # 这里可以实现错误统计逻辑
        # 暂时返回空字典
        return {}

# 全局错误处理器实例
error_handler = ErrorHandler()
