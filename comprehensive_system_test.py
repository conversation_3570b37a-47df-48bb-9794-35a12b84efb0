#!/usr/bin/env python3
"""
量化交易系统全面系统测试
作为资深Python测试工程师，执行完整的系统测试
"""

import sys
import os
import time
import subprocess
from datetime import datetime
from pathlib import Path

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from test_framework import test_framework

class ComprehensiveSystemTester:
    """全面系统测试器"""
    
    def __init__(self):
        self.framework = test_framework
        self.test_modules = [
            {
                'name': '策略功能测试',
                'module': 'test_strategies',
                'description': '测试所有5个策略标签页的完整功能',
                'priority': 'HIGH',
                'estimated_time': 120  # 秒
            },
            {
                'name': '交易流程测试',
                'module': 'test_trading_flow',
                'description': '测试完整的交易流程',
                'priority': 'HIGH',
                'estimated_time': 180
            },
            {
                'name': '环境切换测试',
                'module': 'test_environment_modes',
                'description': '测试模拟/测试网/实盘环境切换',
                'priority': 'HIGH',
                'estimated_time': 90
            },
            {
                'name': '异常处理测试',
                'module': 'test_exception_handling',
                'description': '测试各种异常情况的处理',
                'priority': 'HIGH',
                'estimated_time': 150
            },
            {
                'name': '性能压力测试',
                'module': 'test_performance_stress',
                'description': '进行性能和压力测试',
                'priority': 'MEDIUM',
                'estimated_time': 300
            },
            {
                'name': '安全性测试',
                'module': 'test_security',
                'description': '测试安全机制和风险控制',
                'priority': 'HIGH',
                'estimated_time': 120
            }
        ]
        
        self.test_results = {}
        self.start_time = None
        self.end_time = None
        
    def run_single_test_module(self, module_info):
        """运行单个测试模块"""
        module_name = module_info['name']
        module_file = module_info['module']
        
        print(f"\n{'='*60}")
        print(f"开始执行: {module_name}")
        print(f"描述: {module_info['description']}")
        print(f"优先级: {module_info['priority']}")
        print(f"预估时间: {module_info['estimated_time']}秒")
        print(f"{'='*60}")
        
        start_time = time.time()
        
        try:
            # 执行测试模块
            result = subprocess.run(
                [sys.executable, f"{module_file}.py"],
                capture_output=True,
                text=True,
                timeout=module_info['estimated_time'] + 60  # 额外60秒缓冲
            )
            
            end_time = time.time()
            execution_time = end_time - start_time
            
            # 分析结果
            success = result.returncode == 0
            output = result.stdout
            error_output = result.stderr
            
            # 提取测试统计信息
            test_stats = self.extract_test_stats(output)
            
            self.test_results[module_name] = {
                'success': success,
                'execution_time': execution_time,
                'output': output,
                'error_output': error_output,
                'stats': test_stats,
                'priority': module_info['priority']
            }
            
            # 显示结果
            if success:
                print(f"✅ {module_name} 执行成功")
                if test_stats:
                    print(f"   测试统计: {test_stats}")
            else:
                print(f"❌ {module_name} 执行失败")
                if error_output:
                    print(f"   错误信息: {error_output[:200]}...")
            
            print(f"   执行时间: {execution_time:.1f}秒")
            
        except subprocess.TimeoutExpired:
            end_time = time.time()
            execution_time = end_time - start_time
            
            self.test_results[module_name] = {
                'success': False,
                'execution_time': execution_time,
                'output': '',
                'error_output': f'测试超时 (>{module_info["estimated_time"]}秒)',
                'stats': {},
                'priority': module_info['priority']
            }
            
            print(f"⏰ {module_name} 执行超时")
            
        except Exception as e:
            end_time = time.time()
            execution_time = end_time - start_time
            
            self.test_results[module_name] = {
                'success': False,
                'execution_time': execution_time,
                'output': '',
                'error_output': str(e),
                'stats': {},
                'priority': module_info['priority']
            }
            
            print(f"🔥 {module_name} 执行异常: {e}")
    
    def extract_test_stats(self, output):
        """从输出中提取测试统计信息"""
        stats = {}
        
        try:
            lines = output.split('\n')
            for line in lines:
                if '总测试用例:' in line:
                    stats['total'] = int(line.split(':')[1].strip())
                elif '通过:' in line:
                    stats['passed'] = int(line.split(':')[1].strip())
                elif '失败:' in line:
                    stats['failed'] = int(line.split(':')[1].strip())
                elif '错误:' in line:
                    stats['errors'] = int(line.split(':')[1].strip())
                elif '通过率:' in line:
                    stats['pass_rate'] = float(line.split(':')[1].strip().replace('%', ''))
        except:
            pass
        
        return stats
    
    def run_all_tests(self):
        """运行所有测试"""
        self.start_time = datetime.now()
        
        print("🚀 量化交易系统全面系统测试开始")
        print(f"测试开始时间: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"计划执行 {len(self.test_modules)} 个测试模块")
        
        total_estimated_time = sum(module['estimated_time'] for module in self.test_modules)
        print(f"预估总时间: {total_estimated_time // 60}分{total_estimated_time % 60}秒")
        
        # 按优先级排序执行
        high_priority_modules = [m for m in self.test_modules if m['priority'] == 'HIGH']
        medium_priority_modules = [m for m in self.test_modules if m['priority'] == 'MEDIUM']
        
        print(f"\n高优先级测试: {len(high_priority_modules)} 个")
        print(f"中优先级测试: {len(medium_priority_modules)} 个")
        
        # 执行高优先级测试
        for module in high_priority_modules:
            self.run_single_test_module(module)
        
        # 执行中优先级测试
        for module in medium_priority_modules:
            self.run_single_test_module(module)
        
        self.end_time = datetime.now()
        
        print(f"\n🏁 所有测试执行完成")
        print(f"测试结束时间: {self.end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"总执行时间: {(self.end_time - self.start_time).total_seconds():.1f}秒")
    
    def generate_comprehensive_report(self):
        """生成全面测试报告"""
        if not self.test_results:
            print("❌ 没有测试结果可生成报告")
            return
        
        # 计算总体统计
        total_modules = len(self.test_results)
        successful_modules = sum(1 for result in self.test_results.values() if result['success'])
        failed_modules = total_modules - successful_modules
        
        total_test_cases = sum(result['stats'].get('total', 0) for result in self.test_results.values())
        total_passed = sum(result['stats'].get('passed', 0) for result in self.test_results.values())
        total_failed = sum(result['stats'].get('failed', 0) for result in self.test_results.values())
        total_errors = sum(result['stats'].get('errors', 0) for result in self.test_results.values())
        
        overall_pass_rate = (total_passed / total_test_cases * 100) if total_test_cases > 0 else 0
        
        # 生成报告
        report = f"""
# 量化交易系统全面系统测试报告

## 测试概览
- **测试执行人**: 资深Python测试工程师
- **测试开始时间**: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}
- **测试结束时间**: {self.end_time.strftime('%Y-%m-%d %H:%M:%S')}
- **总执行时间**: {(self.end_time - self.start_time).total_seconds():.1f} 秒

## 测试模块统计
- **测试模块总数**: {total_modules}
- **成功模块**: {successful_modules} ({successful_modules/total_modules*100:.1f}%)
- **失败模块**: {failed_modules} ({failed_modules/total_modules*100:.1f}%)

## 测试用例统计
- **测试用例总数**: {total_test_cases}
- **通过**: {total_passed} ({overall_pass_rate:.1f}%)
- **失败**: {total_failed}
- **错误**: {total_errors}

## 详细测试结果

"""
        
        # 按优先级分组显示结果
        high_priority_results = {k: v for k, v in self.test_results.items() if v['priority'] == 'HIGH'}
        medium_priority_results = {k: v for k, v in self.test_results.items() if v['priority'] == 'MEDIUM'}
        
        report += "### 高优先级测试结果\n\n"
        for module_name, result in high_priority_results.items():
            status_icon = "✅" if result['success'] else "❌"
            report += f"- {status_icon} **{module_name}**\n"
            report += f"  - 执行时间: {result['execution_time']:.1f}秒\n"
            
            if result['stats']:
                stats = result['stats']
                report += f"  - 测试统计: 总计{stats.get('total', 0)} 通过{stats.get('passed', 0)} 失败{stats.get('failed', 0)}\n"
                if 'pass_rate' in stats:
                    report += f"  - 通过率: {stats['pass_rate']:.1f}%\n"
            
            if not result['success'] and result['error_output']:
                report += f"  - 错误信息: {result['error_output'][:100]}...\n"
            
            report += "\n"
        
        report += "### 中优先级测试结果\n\n"
        for module_name, result in medium_priority_results.items():
            status_icon = "✅" if result['success'] else "❌"
            report += f"- {status_icon} **{module_name}**\n"
            report += f"  - 执行时间: {result['execution_time']:.1f}秒\n"
            
            if result['stats']:
                stats = result['stats']
                report += f"  - 测试统计: 总计{stats.get('total', 0)} 通过{stats.get('passed', 0)} 失败{stats.get('failed', 0)}\n"
                if 'pass_rate' in stats:
                    report += f"  - 通过率: {stats['pass_rate']:.1f}%\n"
            
            if not result['success'] and result['error_output']:
                report += f"  - 错误信息: {result['error_output'][:100]}...\n"
            
            report += "\n"
        
        # 添加问题分析和建议
        report += self.generate_analysis_and_recommendations()
        
        # 保存报告
        report_filename = f"comprehensive_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        
        try:
            with open(report_filename, 'w', encoding='utf-8') as f:
                f.write(report)
            
            print(f"📋 全面测试报告已生成: {report_filename}")
            
        except Exception as e:
            print(f"❌ 报告生成失败: {e}")
        
        return report
    
    def generate_analysis_and_recommendations(self):
        """生成问题分析和修复建议"""
        analysis = "\n## 问题分析与修复建议\n\n"
        
        # 分析失败的测试
        failed_tests = {k: v for k, v in self.test_results.items() if not v['success']}
        
        if not failed_tests:
            analysis += "🎉 **所有测试均通过，系统质量优秀！**\n\n"
            analysis += "### 系统优势\n"
            analysis += "- 所有核心功能正常工作\n"
            analysis += "- 异常处理机制完善\n"
            analysis += "- 安全机制有效\n"
            analysis += "- 性能表现良好\n\n"
        else:
            analysis += f"⚠️ **发现 {len(failed_tests)} 个模块存在问题**\n\n"
            
            for module_name, result in failed_tests.items():
                analysis += f"### {module_name} 问题分析\n"
                analysis += f"- **错误信息**: {result['error_output']}\n"
                analysis += f"- **优先级**: {result['priority']}\n"
                
                # 根据模块类型提供具体建议
                if "策略" in module_name:
                    analysis += "- **修复建议**: 检查策略参数验证逻辑，确保边界条件处理正确\n"
                elif "交易流程" in module_name:
                    analysis += "- **修复建议**: 验证API连接配置，检查网络连接和认证参数\n"
                elif "环境切换" in module_name:
                    analysis += "- **修复建议**: 检查环境管理器的状态切换逻辑和安全验证\n"
                elif "异常处理" in module_name:
                    analysis += "- **修复建议**: 完善异常捕获和处理机制，添加更多边界条件测试\n"
                elif "性能" in module_name:
                    analysis += "- **修复建议**: 优化算法性能，检查内存泄漏和资源管理\n"
                elif "安全" in module_name:
                    analysis += "- **修复建议**: 加强安全验证机制，完善风险控制逻辑\n"
                
                analysis += "\n"
        
        # 总体建议
        analysis += "## 总体建议\n\n"
        analysis += "### 立即修复（HIGH优先级）\n"
        high_priority_failed = [k for k, v in failed_tests.items() if v['priority'] == 'HIGH']
        
        if high_priority_failed:
            for module in high_priority_failed:
                analysis += f"- 🔥 {module}: 影响核心功能，需要立即修复\n"
        else:
            analysis += "- ✅ 无高优先级问题\n"
        
        analysis += "\n### 计划修复（MEDIUM优先级）\n"
        medium_priority_failed = [k for k, v in failed_tests.items() if v['priority'] == 'MEDIUM']
        
        if medium_priority_failed:
            for module in medium_priority_failed:
                analysis += f"- ⚠️ {module}: 影响系统性能，建议在下个版本修复\n"
        else:
            analysis += "- ✅ 无中优先级问题\n"
        
        analysis += "\n### 质量保证建议\n"
        analysis += "1. **持续集成**: 将测试集成到CI/CD流程中\n"
        analysis += "2. **定期测试**: 每周执行一次全面测试\n"
        analysis += "3. **监控告警**: 在生产环境中添加实时监控\n"
        analysis += "4. **文档更新**: 根据测试结果更新用户文档\n"
        analysis += "5. **培训计划**: 为用户提供系统使用培训\n\n"
        
        # 实盘交易特别提醒
        analysis += "## ⚠️ 实盘交易特别提醒\n\n"
        analysis += "**在启用实盘交易前，请确保：**\n"
        analysis += "1. 所有HIGH优先级测试100%通过\n"
        analysis += "2. 在测试网环境充分验证策略\n"
        analysis += "3. 设置合理的风险控制参数\n"
        analysis += "4. 从小资金开始测试\n"
        analysis += "5. 密切监控系统运行状态\n\n"
        
        return analysis
    
    def print_summary(self):
        """打印测试摘要"""
        if not self.test_results:
            return
        
        successful_modules = sum(1 for result in self.test_results.values() if result['success'])
        total_modules = len(self.test_results)
        
        print(f"\n{'='*80}")
        print("📊 测试执行摘要")
        print(f"{'='*80}")
        print(f"测试模块: {successful_modules}/{total_modules} 成功")
        print(f"执行时间: {(self.end_time - self.start_time).total_seconds():.1f} 秒")
        
        if successful_modules == total_modules:
            print("🎉 所有测试模块执行成功！")
        else:
            failed_count = total_modules - successful_modules
            print(f"⚠️ {failed_count} 个测试模块执行失败")
        
        print(f"{'='*80}")

def main():
    """主函数"""
    print("🔬 量化交易系统全面系统测试")
    print("作为资深Python测试工程师，执行完整的系统测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tester = ComprehensiveSystemTester()
    
    try:
        # 执行所有测试
        tester.run_all_tests()
        
        # 打印摘要
        tester.print_summary()
        
        # 生成报告
        tester.generate_comprehensive_report()
        
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n🔥 测试执行异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
