2025-08-05 20:06:01 - ERROR - 错误处理: 系统错误 - 系统发生未知错误
2025-08-05 20:06:01 - ERROR - 错误代码: SYSTEM_001
2025-08-05 20:06:01 - ERROR - 原始错误: 交易对格式不正确: INVALID_SYMBOL
2025-08-05 20:06:01 - ERROR - 错误上下文: {'strategy': 'GridTrading', 'symbol': 'INVALID_SYMBOL', 'base_price': -1, 'grid_spacing': 0, 'grid_count': 0, 'order_amount': -100}
2025-08-05 20:06:01 - DEBUG - 堆栈跟踪: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\yl\2.0\strategies.py", line 115, in __init__
    self._validate_parameters(symbol, base_price, grid_spacing, grid_count, order_amount)
  File "C:\Users\<USER>\Desktop\yl\2.0\strategies.py", line 176, in _validate_parameters
    validator.validate_symbol(symbol)
  File "C:\Users\<USER>\Desktop\yl\2.0\parameter_validator.py", line 193, in validate_symbol
    raise ValidationError(
parameter_validator.ValidationError: 交易对格式不正确: INVALID_SYMBOL

2025-08-05 22:11:53 - ERROR - 错误处理: 系统错误 - 系统发生未知错误
2025-08-05 22:11:53 - ERROR - 错误代码: SYSTEM_001
2025-08-05 22:11:53 - ERROR - 原始错误: 交易对格式不正确: INVALID_SYMBOL
2025-08-05 22:11:53 - ERROR - 错误上下文: {'strategy': 'GridTrading', 'symbol': 'INVALID_SYMBOL', 'base_price': -1, 'grid_spacing': 0, 'grid_count': 0, 'order_amount': -100}
2025-08-05 22:11:53 - DEBUG - 堆栈跟踪: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\yl\2.0\strategies.py", line 115, in __init__
    self._validate_parameters(symbol, base_price, grid_spacing, grid_count, order_amount)
  File "C:\Users\<USER>\Desktop\yl\2.0\strategies.py", line 176, in _validate_parameters
    validator.validate_symbol(symbol)
  File "C:\Users\<USER>\Desktop\yl\2.0\parameter_validator.py", line 193, in validate_symbol
    raise ValidationError(
parameter_validator.ValidationError: 交易对格式不正确: INVALID_SYMBOL

2025-08-05 22:18:52 - ERROR - 错误处理: 系统错误 - 系统发生未知错误
2025-08-05 22:18:52 - ERROR - 错误代码: SYSTEM_001
2025-08-05 22:18:52 - ERROR - 原始错误: 交易对格式不正确: INVALID_SYMBOL
2025-08-05 22:18:52 - ERROR - 错误上下文: {'strategy': 'GridTrading', 'symbol': 'INVALID_SYMBOL', 'base_price': -1, 'grid_spacing': 0, 'grid_count': 0, 'order_amount': -100}
2025-08-05 22:18:52 - DEBUG - 堆栈跟踪: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\yl\2.0\strategies.py", line 115, in __init__
    self._validate_parameters(symbol, base_price, grid_spacing, grid_count, order_amount)
  File "C:\Users\<USER>\Desktop\yl\2.0\strategies.py", line 176, in _validate_parameters
    validator.validate_symbol(symbol)
  File "C:\Users\<USER>\Desktop\yl\2.0\parameter_validator.py", line 193, in validate_symbol
    raise ValidationError(
parameter_validator.ValidationError: 交易对格式不正确: INVALID_SYMBOL

2025-08-05 22:21:02 - ERROR - 错误处理: 系统错误 - 系统发生未知错误
2025-08-05 22:21:02 - ERROR - 错误代码: SYSTEM_001
2025-08-05 22:21:02 - ERROR - 原始错误: 交易对格式不正确: INVALID_SYMBOL
2025-08-05 22:21:02 - ERROR - 错误上下文: {'strategy': 'GridTrading', 'symbol': 'INVALID_SYMBOL', 'base_price': -1, 'grid_spacing': 0, 'grid_count': 0, 'order_amount': -100}
2025-08-05 22:21:02 - DEBUG - 堆栈跟踪: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\yl\2.0\strategies.py", line 115, in __init__
    self._validate_parameters(symbol, base_price, grid_spacing, grid_count, order_amount)
  File "C:\Users\<USER>\Desktop\yl\2.0\strategies.py", line 176, in _validate_parameters
    validator.validate_symbol(symbol)
  File "C:\Users\<USER>\Desktop\yl\2.0\parameter_validator.py", line 193, in validate_symbol
    raise ValidationError(
parameter_validator.ValidationError: 交易对格式不正确: INVALID_SYMBOL

2025-08-05 22:23:46 - ERROR - 错误处理: 系统错误 - 系统发生未知错误
2025-08-05 22:23:46 - ERROR - 错误代码: SYSTEM_001
2025-08-05 22:23:46 - ERROR - 原始错误: 交易对格式不正确: INVALID_SYMBOL
2025-08-05 22:23:46 - ERROR - 错误上下文: {'strategy': 'GridTrading', 'symbol': 'INVALID_SYMBOL', 'base_price': -1, 'grid_spacing': 0, 'grid_count': 0, 'order_amount': -100}
2025-08-05 22:23:46 - DEBUG - 堆栈跟踪: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\yl\2.0\strategies.py", line 115, in __init__
    self._validate_parameters(symbol, base_price, grid_spacing, grid_count, order_amount)
  File "C:\Users\<USER>\Desktop\yl\2.0\strategies.py", line 176, in _validate_parameters
    validator.validate_symbol(symbol)
  File "C:\Users\<USER>\Desktop\yl\2.0\parameter_validator.py", line 193, in validate_symbol
    raise ValidationError(
parameter_validator.ValidationError: 交易对格式不正确: INVALID_SYMBOL

2025-08-05 22:33:28 - ERROR - 错误处理: 系统错误 - 系统发生未知错误
2025-08-05 22:33:28 - ERROR - 错误代码: SYSTEM_001
2025-08-05 22:33:28 - ERROR - 原始错误: 总投资金额过大: 1068750.00 USDT
2025-08-05 22:33:28 - ERROR - 错误上下文: {'strategy': 'GridTrading', 'symbol': 'CFX/USDT', 'base_price': 0.21375, 'grid_spacing': 1.0, 'grid_count': 10, 'order_amount': 1000000}
2025-08-05 22:33:28 - DEBUG - 堆栈跟踪: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\yl\2.0\strategies.py", line 115, in __init__
    self._validate_parameters(symbol, base_price, grid_spacing, grid_count, order_amount)
  File "C:\Users\<USER>\Desktop\yl\2.0\strategies.py", line 178, in _validate_parameters
    validator.validate_grid_parameters(base_price, grid_spacing/100, grid_count, order_amount)
  File "C:\Users\<USER>\Desktop\yl\2.0\parameter_validator.py", line 464, in validate_grid_parameters
    raise ValidationError(
parameter_validator.ValidationError: 总投资金额过大: 1068750.00 USDT

2025-08-05 22:33:28 - ERROR - 错误处理: 系统错误 - 系统发生未知错误
2025-08-05 22:33:28 - ERROR - 错误代码: SYSTEM_001
2025-08-05 22:33:28 - ERROR - 原始错误: API密钥错误: sk-1234567890abcdef
2025-08-05 22:33:28 - DEBUG - 堆栈跟踪: NoneType: None

2025-08-05 22:51:51 - ERROR - 错误处理: 系统错误 - 系统发生未知错误
2025-08-05 22:51:51 - ERROR - 错误代码: SYSTEM_001
2025-08-05 22:51:51 - ERROR - 原始错误: 交易对格式不正确: INVALID_SYMBOL
2025-08-05 22:51:51 - ERROR - 错误上下文: {'strategy': 'GridTrading', 'symbol': 'INVALID_SYMBOL', 'base_price': -1, 'grid_spacing': 0, 'grid_count': 0, 'order_amount': -100}
2025-08-05 22:51:51 - DEBUG - 堆栈跟踪: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\yl\2.0\strategies.py", line 115, in __init__
    self._validate_parameters(symbol, base_price, grid_spacing, grid_count, order_amount)
  File "C:\Users\<USER>\Desktop\yl\2.0\strategies.py", line 176, in _validate_parameters
    validator.validate_symbol(symbol)
  File "C:\Users\<USER>\Desktop\yl\2.0\parameter_validator.py", line 193, in validate_symbol
    raise ValidationError(
parameter_validator.ValidationError: 交易对格式不正确: INVALID_SYMBOL

2025-08-05 23:04:48 - ERROR - 错误处理: 系统错误 - 系统发生未知错误
2025-08-05 23:04:48 - ERROR - 错误代码: SYSTEM_001
2025-08-05 23:04:48 - ERROR - 原始错误: 交易对格式不正确: INVALID_SYMBOL
2025-08-05 23:04:48 - ERROR - 错误上下文: {'strategy': 'GridTrading', 'symbol': 'INVALID_SYMBOL', 'base_price': -1, 'grid_spacing': 0, 'grid_count': 0, 'order_amount': -100}
2025-08-05 23:04:48 - DEBUG - 堆栈跟踪: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\yl\2.0\strategies.py", line 115, in __init__
    self._validate_parameters(symbol, base_price, grid_spacing, grid_count, order_amount)
  File "C:\Users\<USER>\Desktop\yl\2.0\strategies.py", line 176, in _validate_parameters
    validator.validate_symbol(symbol)
  File "C:\Users\<USER>\Desktop\yl\2.0\parameter_validator.py", line 193, in validate_symbol
    raise ValidationError(
parameter_validator.ValidationError: 交易对格式不正确: INVALID_SYMBOL

