@echo off
echo ========================================
echo 量化交易系统打包修复工具
echo ========================================

echo [1/4] 清理环境...
if exist build rmdir /s /q build >nul 2>&1
if exist dist rmdir /s /q dist >nul 2>&1
if exist __pycache__ rmdir /s /q __pycache__ >nul 2>&1
echo 清理完成

echo [2/4] 重新安装PyInstaller...
pip uninstall pyinstaller -y >nul 2>&1
pip install pyinstaller >nul 2>&1
echo PyInstaller重新安装完成

echo [3/4] 开始打包...
echo 这可能需要几分钟，请耐心等待...

python -m PyInstaller --onefile --windowed --name="QuantTradingSystem" main.py

echo [4/4] 检查结果...
if exist "dist\QuantTradingSystem.exe" (
    echo ========================================
    echo          打包成功！
    echo ========================================
    echo 可执行文件: dist\QuantTradingSystem.exe
    dir "dist\QuantTradingSystem.exe"
) else (
    echo ========================================
    echo          打包失败！
    echo ========================================
    echo 请查看上面的错误信息
    echo 或参考 PYINSTALLER_FIX.md 文档
)

echo ========================================
pause
