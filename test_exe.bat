@echo off
echo 测试量化交易系统...
echo ========================

if exist "dist\QuantTradingSystem.exe" (
    echo 测试可执行文件...
    "dist\QuantTradingSystem.exe" --help 2>nul
    if %ERRORLEVEL% EQU 0 (
        echo ✓ 可执行文件正常
    ) else (
        echo - 可执行文件可能需要GUI环境
    )
) else (
    echo ✗ 找不到可执行文件
)

echo.
echo 文件信息:
if exist "dist\QuantTradingSystem.exe" dir "dist\QuantTradingSystem.exe"
if exist "dist\MyApp.exe" dir "dist\MyApp.exe"

pause
