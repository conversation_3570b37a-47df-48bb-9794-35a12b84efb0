#!/usr/bin/env python3
"""
GUI策略创建测试
专门测试GUI标签页中策略创建的参数一致性
"""

import sys
import os
from unittest.mock import Mock
from datetime import datetime

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class GUIStrategyCreationTest:
    """GUI策略创建测试类"""
    
    def __init__(self):
        self.test_results = []
        self.bugs_found = []
        
    def log_test(self, test_name, passed, details=""):
        """记录测试结果"""
        if passed:
            print(f"✅ {test_name}")
        else:
            print(f"❌ {test_name}: {details}")
            self.bugs_found.append({
                'test': test_name,
                'details': details,
                'timestamp': datetime.now()
            })
        
        self.test_results.append({
            'test': test_name,
            'passed': passed,
            'details': details
        })
    
    def test_moving_average_strategy_creation(self):
        """测试移动平均线策略创建"""
        print("\n📈 测试移动平均线策略GUI创建...")
        
        try:
            from strategies import MovingAverageStrategy
            
            # 创建模拟交易所
            mock_exchange = Mock()
            mock_exchange.fetch_ticker.return_value = {'last': 0.21375}
            
            # 测试使用正确的参数名称创建策略
            strategy = MovingAverageStrategy(
                exchange=mock_exchange,
                symbol='BTC/USDT',
                short_period=10,
                long_period=30,
                amount=100
            )
            
            # 验证策略属性
            assert strategy.short_period == 10
            assert strategy.long_period == 30
            assert strategy.symbol == 'BTC/USDT'
            
            self.log_test("移动平均线策略GUI参数一致性", True, "参数名称匹配正确")
            
        except Exception as e:
            self.log_test("移动平均线策略GUI参数一致性", False, str(e))
    
    def test_rsi_strategy_creation(self):
        """测试RSI策略创建"""
        print("\n📊 测试RSI策略GUI创建...")
        
        try:
            from strategies import RSIStrategy
            
            # 创建模拟交易所
            mock_exchange = Mock()
            mock_exchange.fetch_ticker.return_value = {'last': 0.21375}
            
            # 测试使用正确的参数名称创建策略
            strategy = RSIStrategy(
                exchange=mock_exchange,
                symbol='ETH/USDT',
                period=14,
                oversold=30,
                overbought=70,
                amount=100
            )
            
            # 验证策略属性
            assert strategy.period == 14
            assert strategy.oversold == 30
            assert strategy.overbought == 70
            assert strategy.symbol == 'ETH/USDT'
            
            self.log_test("RSI策略GUI参数一致性", True, "参数名称匹配正确")
            
        except Exception as e:
            self.log_test("RSI策略GUI参数一致性", False, str(e))
    
    def test_grid_trading_strategy_creation(self):
        """测试网格交易策略创建"""
        print("\n🔲 测试网格交易策略GUI创建...")
        
        try:
            from strategies import GridTrading
            
            # 创建模拟交易所
            mock_exchange = Mock()
            mock_exchange.fetch_ticker.return_value = {'last': 0.21375}
            
            # 测试使用正确的参数名称创建策略
            strategy = GridTrading(
                exchange=mock_exchange,
                symbol='CFX/USDT',
                base_price=0.21375,
                grid_spacing=1.0,
                grid_count=10,
                order_amount=100
            )
            
            # 验证策略属性
            assert strategy.base_price == 0.21375
            assert strategy.grid_spacing == 1.0
            assert strategy.grid_count == 10
            assert strategy.order_amount == 100
            assert strategy.symbol == 'CFX/USDT'
            
            self.log_test("网格交易策略GUI参数一致性", True, "参数名称匹配正确")
            
        except Exception as e:
            self.log_test("网格交易策略GUI参数一致性", False, str(e))
    
    def test_strategy_tabs_import(self):
        """测试策略标签页导入"""
        print("\n🖥️ 测试策略标签页导入...")
        
        try:
            from strategy_tabs import (GridTradingTab, MovingAverageTab, 
                                     RSIStrategyTab, AwesomeOscillatorTab, FMACDTab)
            
            # 验证所有标签页类都能正常导入
            tab_classes = [GridTradingTab, MovingAverageTab, RSIStrategyTab, 
                          AwesomeOscillatorTab, FMACDTab]
            
            for tab_class in tab_classes:
                assert hasattr(tab_class, 'create_widgets')
                assert hasattr(tab_class, 'start_strategy')
                assert hasattr(tab_class, 'stop_strategy')
            
            self.log_test("策略标签页导入", True, f"成功导入{len(tab_classes)}个标签页类")
            
        except Exception as e:
            self.log_test("策略标签页导入", False, str(e))
    
    def test_parameter_compatibility(self):
        """测试参数兼容性"""
        print("\n🔧 测试参数兼容性...")
        
        try:
            from strategies import MovingAverageStrategy, RSIStrategy
            
            # 创建模拟交易所
            mock_exchange = Mock()
            mock_exchange.fetch_ticker.return_value = {'last': 0.21375}
            
            # 测试RSI策略的兼容性属性
            rsi_strategy = RSIStrategy(
                exchange=mock_exchange,
                symbol='BTC/USDT',
                period=14,
                oversold=30,
                overbought=70
            )
            
            # 验证兼容性属性存在
            assert hasattr(rsi_strategy, 'rsi_period')
            assert hasattr(rsi_strategy, 'oversold_threshold')
            assert hasattr(rsi_strategy, 'overbought_threshold')
            assert rsi_strategy.rsi_period == 14
            assert rsi_strategy.oversold_threshold == 30
            assert rsi_strategy.overbought_threshold == 70
            
            self.log_test("参数兼容性", True, "兼容性属性正常工作")
            
        except Exception as e:
            self.log_test("参数兼容性", False, str(e))
    
    def run_all_tests(self):
        """运行所有GUI策略创建测试"""
        print("🖥️ 开始GUI策略创建测试")
        print("=" * 60)
        
        # 执行所有测试
        self.test_moving_average_strategy_creation()
        self.test_rsi_strategy_creation()
        self.test_grid_trading_strategy_creation()
        self.test_strategy_tabs_import()
        self.test_parameter_compatibility()
        
        # 计算结果
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['passed'])
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        print("\n" + "=" * 60)
        print(f"🖥️ GUI策略创建测试结果:")
        print(f"   总测试数: {total_tests}")
        print(f"   通过数: {passed_tests}")
        print(f"   失败数: {total_tests - passed_tests}")
        print(f"   成功率: {success_rate:.1f}%")
        
        if success_rate == 100:
            print("🎉 所有GUI策略创建测试通过！参数一致性修复成功！")
            print("✅ GUI界面可以正常创建所有策略")
        else:
            print("⚠️ 发现GUI策略创建问题:")
            for bug in self.bugs_found:
                print(f"   🐛 {bug['test']}: {bug['details']}")
        
        return success_rate == 100

def main():
    """主函数"""
    tester = GUIStrategyCreationTest()
    success = tester.run_all_tests()
    
    if success:
        print("\n🚀 GUI策略创建功能完全正常！")
    else:
        print("\n⛔ GUI策略创建存在问题，需要进一步修复！")
    
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
