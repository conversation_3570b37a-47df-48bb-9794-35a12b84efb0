@echo off
echo 量化交易系统打包脚本
echo ========================

echo 1. 清理缓存...
rmdir /s /q build 2>nul
rmdir /s /q dist 2>nul
rmdir /s /q __pycache__ 2>nul

echo 2. 开始打包...
python -m PyInstaller ^
    --onefile ^
    --windowed ^
    --name="QuantTradingSystem" ^
    --add-data="*.py;." ^
    --hidden-import=tkinter ^
    --hidden-import=tkinter.ttk ^
    --hidden-import=ccxt ^
    --hidden-import=pandas ^
    --hidden-import=numpy ^
    --hidden-import=requests ^
    --hidden-import=cryptography ^
    --exclude-module=matplotlib ^
    --exclude-module=scipy ^
    main.py

if %ERRORLEVEL% EQU 0 (
    echo [SUCCESS] 打包成功!
    echo 可执行文件: dist\QuantTradingSystem.exe
) else (
    echo [ERROR] 打包失败!
)

pause
