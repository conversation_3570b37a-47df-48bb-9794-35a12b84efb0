#!/usr/bin/env python3
"""
压力测试与性能测试
进行长时间运行测试、并发测试、内存泄漏检测
"""

import sys
import os
import time
import threading
import psutil
import gc
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from test_framework import test_framework, TestResult
from strategies import GridTrading, MovingAverageStrategy
from exchange_manager import ExchangeManager

class PerformanceStressTester:
    """性能压力测试器"""
    
    def __init__(self):
        self.framework = test_framework
        self.mock_exchange = self.framework.create_mock_exchange()
        self.process = psutil.Process()
        
    def test_memory_usage(self):
        """测试内存使用情况"""
        
        def test_memory_leak_detection():
            """测试内存泄漏检测"""
            initial_memory = self.process.memory_info().rss / 1024 / 1024  # MB
            
            # 创建大量策略对象
            strategies = []
            for i in range(100):
                strategy = GridTrading(
                    exchange=self.mock_exchange,
                    symbol='CFX/USDT',
                    base_price=0.213456,
                    grid_spacing=2.0,
                    grid_count=10,
                    order_amount=100
                )
                strategies.append(strategy)
            
            # 删除策略对象
            del strategies
            gc.collect()
            
            final_memory = self.process.memory_info().rss / 1024 / 1024  # MB
            memory_increase = final_memory - initial_memory
            
            self.framework.assert_true(memory_increase < 50, 
                                     f"内存增长应小于50MB，实际增长: {memory_increase:.2f}MB")
            
            self.framework.logger.info(f"内存使用: 初始 {initial_memory:.2f}MB, "
                                     f"最终 {final_memory:.2f}MB, "
                                     f"增长 {memory_increase:.2f}MB")
        
        def test_memory_usage_monitoring():
            """测试内存使用监控"""
            memory_samples = []
            
            # 监控5秒内的内存使用
            start_time = time.time()
            while time.time() - start_time < 5:
                memory_mb = self.process.memory_info().rss / 1024 / 1024
                memory_samples.append(memory_mb)
                time.sleep(0.5)
            
            avg_memory = sum(memory_samples) / len(memory_samples)
            max_memory = max(memory_samples)
            min_memory = min(memory_samples)
            
            self.framework.assert_true(max_memory - min_memory < 100, 
                                     "内存波动应小于100MB")
            
            self.framework.logger.info(f"内存监控: 平均 {avg_memory:.2f}MB, "
                                     f"最大 {max_memory:.2f}MB, "
                                     f"最小 {min_memory:.2f}MB")
        
        # 运行内存测试
        self.framework.run_test_case(
            test_memory_leak_detection,
            "内存泄漏检测测试",
            "检测系统是否存在内存泄漏",
            "性能压力测试",
            "HIGH"
        )
        
        self.framework.run_test_case(
            test_memory_usage_monitoring,
            "内存使用监控测试",
            "监控系统运行时的内存使用情况",
            "性能压力测试",
            "MEDIUM"
        )
    
    def test_concurrent_operations(self):
        """测试并发操作"""
        
        def test_concurrent_strategy_execution():
            """测试并发策略执行"""
            def run_strategy(strategy_id):
                strategy = GridTrading(
                    exchange=self.mock_exchange,
                    symbol='CFX/USDT',
                    base_price=0.213456,
                    grid_spacing=2.0,
                    grid_count=10,
                    order_amount=100
                )
                
                # 模拟策略运行
                for _ in range(10):
                    try:
                        price = strategy.get_current_price()
                        time.sleep(0.1)
                    except Exception as e:
                        return f"Strategy {strategy_id} failed: {e}"
                
                return f"Strategy {strategy_id} completed"
            
            # 并发运行多个策略
            with ThreadPoolExecutor(max_workers=5) as executor:
                futures = [executor.submit(run_strategy, i) for i in range(5)]
                results = [future.result() for future in as_completed(futures)]
            
            # 验证所有策略都成功完成
            completed_count = sum(1 for result in results if "completed" in result)
            self.framework.assert_equal(completed_count, 5, "所有策略都应该成功完成")
        
        def test_concurrent_api_calls():
            """测试并发API调用"""
            def make_api_call(call_id):
                try:
                    ticker = self.mock_exchange.fetch_ticker('CFX/USDT')
                    balance = self.mock_exchange.fetch_balance()
                    return f"Call {call_id} success"
                except Exception as e:
                    return f"Call {call_id} failed: {e}"
            
            # 并发进行API调用
            with ThreadPoolExecutor(max_workers=10) as executor:
                futures = [executor.submit(make_api_call, i) for i in range(20)]
                results = [future.result() for future in as_completed(futures)]
            
            # 验证API调用成功率
            success_count = sum(1 for result in results if "success" in result)
            success_rate = success_count / len(results) * 100
            
            self.framework.assert_true(success_rate >= 90, 
                                     f"API调用成功率应大于90%，实际: {success_rate:.1f}%")
        
        def test_thread_safety():
            """测试线程安全性"""
            shared_data = {'counter': 0}
            lock = threading.Lock()
            
            def increment_counter(thread_id):
                for _ in range(100):
                    with lock:
                        shared_data['counter'] += 1
                    time.sleep(0.001)
            
            # 多线程操作共享数据
            threads = []
            for i in range(5):
                thread = threading.Thread(target=increment_counter, args=(i,))
                threads.append(thread)
                thread.start()
            
            for thread in threads:
                thread.join()
            
            expected_count = 5 * 100
            self.framework.assert_equal(shared_data['counter'], expected_count, 
                                      f"计数器应为{expected_count}，实际: {shared_data['counter']}")
        
        # 运行并发测试
        self.framework.run_test_case(
            test_concurrent_strategy_execution,
            "并发策略执行测试",
            "测试多个策略并发执行的稳定性",
            "性能压力测试",
            "HIGH"
        )
        
        self.framework.run_test_case(
            test_concurrent_api_calls,
            "并发API调用测试",
            "测试并发API调用的性能和稳定性",
            "性能压力测试",
            "HIGH"
        )
        
        self.framework.run_test_case(
            test_thread_safety,
            "线程安全性测试",
            "验证系统的线程安全性",
            "性能压力测试",
            "HIGH"
        )
    
    def test_long_running_stability(self):
        """测试长时间运行稳定性"""
        
        def test_extended_operation():
            """测试长时间运行操作"""
            start_time = time.time()
            operation_count = 0
            error_count = 0
            
            # 运行30秒的连续操作
            while time.time() - start_time < 30:
                try:
                    # 模拟策略操作
                    ticker = self.mock_exchange.fetch_ticker('CFX/USDT')
                    balance = self.mock_exchange.fetch_balance()
                    operation_count += 1
                    
                    if operation_count % 100 == 0:
                        self.framework.logger.info(f"已完成 {operation_count} 次操作")
                    
                    time.sleep(0.1)
                    
                except Exception as e:
                    error_count += 1
                    self.framework.logger.warning(f"操作失败: {e}")
            
            total_time = time.time() - start_time
            error_rate = error_count / operation_count * 100 if operation_count > 0 else 100
            
            self.framework.assert_true(error_rate < 5, 
                                     f"错误率应小于5%，实际: {error_rate:.2f}%")
            
            self.framework.logger.info(f"长时间运行测试: {total_time:.1f}秒, "
                                     f"操作次数: {operation_count}, "
                                     f"错误次数: {error_count}, "
                                     f"错误率: {error_rate:.2f}%")
        
        def test_resource_cleanup():
            """测试资源清理"""
            initial_threads = threading.active_count()
            
            # 创建和销毁多个策略
            for i in range(10):
                strategy = MovingAverageStrategy(
                    exchange=self.mock_exchange,
                    symbol='CFX/USDT',
                    short_period=5,
                    long_period=20,
                    amount=100
                )
                
                # 模拟策略运行
                try:
                    price = strategy.get_current_price()
                except:
                    pass
                
                # 删除策略
                del strategy
            
            # 强制垃圾回收
            gc.collect()
            time.sleep(1)
            
            final_threads = threading.active_count()
            thread_increase = final_threads - initial_threads
            
            self.framework.assert_true(thread_increase <= 2, 
                                     f"线程数增长应小于等于2，实际增长: {thread_increase}")
        
        # 运行长时间稳定性测试
        self.framework.run_test_case(
            test_extended_operation,
            "长时间运行操作测试",
            "测试系统长时间运行的稳定性",
            "性能压力测试",
            "HIGH"
        )
        
        self.framework.run_test_case(
            test_resource_cleanup,
            "资源清理测试",
            "验证系统资源的正确清理",
            "性能压力测试",
            "MEDIUM"
        )
    
    def test_performance_benchmarks(self):
        """测试性能基准"""
        
        def test_api_call_performance():
            """测试API调用性能"""
            call_times = []
            
            for _ in range(100):
                start_time = time.time()
                try:
                    ticker = self.mock_exchange.fetch_ticker('CFX/USDT')
                    end_time = time.time()
                    call_times.append(end_time - start_time)
                except Exception:
                    pass
            
            if call_times:
                avg_time = sum(call_times) / len(call_times)
                max_time = max(call_times)
                min_time = min(call_times)
                
                self.framework.assert_true(avg_time < 0.1, 
                                         f"平均API调用时间应小于0.1秒，实际: {avg_time:.4f}秒")
                
                self.framework.logger.info(f"API调用性能: 平均 {avg_time:.4f}秒, "
                                         f"最大 {max_time:.4f}秒, "
                                         f"最小 {min_time:.4f}秒")
        
        def test_strategy_initialization_performance():
            """测试策略初始化性能"""
            init_times = []
            
            for _ in range(50):
                start_time = time.time()
                strategy = GridTrading(
                    exchange=self.mock_exchange,
                    symbol='CFX/USDT',
                    base_price=0.213456,
                    grid_spacing=2.0,
                    grid_count=10,
                    order_amount=100
                )
                end_time = time.time()
                init_times.append(end_time - start_time)
                del strategy
            
            avg_init_time = sum(init_times) / len(init_times)
            max_init_time = max(init_times)
            
            self.framework.assert_true(avg_init_time < 0.01, 
                                     f"平均策略初始化时间应小于0.01秒，实际: {avg_init_time:.4f}秒")
            
            self.framework.logger.info(f"策略初始化性能: 平均 {avg_init_time:.4f}秒, "
                                     f"最大 {max_init_time:.4f}秒")
        
        # 运行性能基准测试
        self.framework.run_test_case(
            test_api_call_performance,
            "API调用性能测试",
            "测试API调用的性能基准",
            "性能压力测试",
            "MEDIUM"
        )
        
        self.framework.run_test_case(
            test_strategy_initialization_performance,
            "策略初始化性能测试",
            "测试策略初始化的性能基准",
            "性能压力测试",
            "MEDIUM"
        )
    
    def run_all_performance_tests(self):
        """运行所有性能压力测试"""
        print("开始性能压力测试...")
        
        # 记录系统信息
        cpu_count = psutil.cpu_count()
        memory_total = psutil.virtual_memory().total / 1024 / 1024 / 1024  # GB
        
        self.framework.logger.info(f"系统信息: CPU核心数 {cpu_count}, 总内存 {memory_total:.1f}GB")
        
        # 测试所有性能压力功能
        self.test_memory_usage()
        self.test_concurrent_operations()
        self.test_long_running_stability()
        self.test_performance_benchmarks()
        
        print("性能压力测试完成")

def main():
    """主测试函数"""
    print("=" * 80)
    print("量化交易系统 - 性能压力全面测试")
    print("=" * 80)
    
    tester = PerformanceStressTester()
    tester.run_all_performance_tests()
    
    # 生成测试报告
    summary = test_framework.reporter.generate_summary()
    print(f"\n性能压力测试摘要:")
    print(f"总测试用例: {summary['total']}")
    print(f"通过: {summary['passed']}")
    print(f"失败: {summary['failed']}")
    print(f"错误: {summary['errors']}")
    print(f"通过率: {summary['pass_rate']:.1f}%")

if __name__ == "__main__":
    main()
