#!/usr/bin/env python3
"""
策略环境适配器
为策略提供环境感知能力，支持实盘/模拟环境切换
"""

import time
import random
from datetime import datetime, timedelta
from environment_manager import environment_manager
from logger import get_logger

class StrategyEnvironmentAdapter:
    """策略环境适配器"""
    
    def __init__(self, strategy_name, exchange, symbol):
        self.strategy_name = strategy_name
        self.exchange = exchange
        self.symbol = symbol
        self.logger = get_logger(f"env_adapter_{strategy_name}")
        
        # 模拟数据缓存
        self.simulated_balance = {
            'USDT': {'free': 10000.0, 'used': 0.0, 'total': 10000.0},
            'CFX': {'free': 0.0, 'used': 0.0, 'total': 0.0},
            'BTC': {'free': 0.0, 'used': 0.0, 'total': 0.0},
            'ETH': {'free': 0.0, 'used': 0.0, 'total': 0.0}
        }
        
        self.simulated_orders = {}
        self.order_id_counter = 1000
        
        # 价格模拟参数
        self.base_price = 0.213456  # CFX/USDT基准价格
        self.price_volatility = 0.02  # 价格波动率
        self.last_simulated_price = self.base_price
        self.price_trend = 0.0  # 价格趋势
        
        self.logger.info(f"策略环境适配器初始化: {strategy_name}")
    
    def fetch_ticker(self, symbol):
        """获取价格信息"""
        if environment_manager.should_use_real_api():
            # 使用真实API
            try:
                return self.exchange.fetch_ticker(symbol)
            except Exception as e:
                self.logger.error(f"获取真实价格失败: {e}")
                # 降级到模拟数据
                return self._generate_simulated_ticker(symbol)
        else:
            # 使用模拟数据
            return self._generate_simulated_ticker(symbol)
    
    def fetch_balance(self):
        """获取账户余额"""
        if environment_manager.should_use_real_api():
            # 使用真实API
            try:
                return self.exchange.fetch_balance()
            except Exception as e:
                self.logger.error(f"获取真实余额失败: {e}")
                # 降级到模拟数据
                return self.simulated_balance.copy()
        else:
            # 使用模拟数据
            return self.simulated_balance.copy()
    
    def fetch_order_book(self, symbol, limit=5):
        """获取订单簿"""
        if environment_manager.should_use_real_api():
            # 使用真实API
            try:
                return self.exchange.fetch_order_book(symbol, limit)
            except Exception as e:
                self.logger.error(f"获取真实订单簿失败: {e}")
                # 降级到模拟数据
                return self._generate_simulated_orderbook(symbol, limit)
        else:
            # 使用模拟数据
            return self._generate_simulated_orderbook(symbol, limit)
    
    def fetch_trades(self, symbol, limit=1):
        """获取交易历史"""
        if environment_manager.should_use_real_api():
            # 使用真实API
            try:
                return self.exchange.fetch_trades(symbol, limit)
            except Exception as e:
                self.logger.error(f"获取真实交易历史失败: {e}")
                # 降级到模拟数据
                return self._generate_simulated_trades(symbol, limit)
        else:
            # 使用模拟数据
            return self._generate_simulated_trades(symbol, limit)
    
    def create_market_buy_order(self, symbol, amount):
        """创建市价买单"""
        if environment_manager.should_use_real_api():
            # 使用真实API
            try:
                return self.exchange.create_market_buy_order(symbol, amount)
            except Exception as e:
                self.logger.error(f"创建真实买单失败: {e}")
                # 降级到模拟交易
                return self._simulate_market_buy_order(symbol, amount)
        else:
            # 使用模拟交易
            return self._simulate_market_buy_order(symbol, amount)
    
    def create_market_sell_order(self, symbol, amount):
        """创建市价卖单"""
        if environment_manager.should_use_real_api():
            # 使用真实API
            try:
                return self.exchange.create_market_sell_order(symbol, amount)
            except Exception as e:
                self.logger.error(f"创建真实卖单失败: {e}")
                # 降级到模拟交易
                return self._simulate_market_sell_order(symbol, amount)
        else:
            # 使用模拟交易
            return self._simulate_market_sell_order(symbol, amount)
    
    def create_limit_buy_order(self, symbol, amount, price):
        """创建限价买单"""
        if environment_manager.should_use_real_api():
            # 使用真实API
            try:
                return self.exchange.create_limit_buy_order(symbol, amount, price)
            except Exception as e:
                self.logger.error(f"创建真实限价买单失败: {e}")
                # 降级到模拟交易
                return self._simulate_limit_buy_order(symbol, amount, price)
        else:
            # 使用模拟交易
            return self._simulate_limit_buy_order(symbol, amount, price)
    
    def create_limit_sell_order(self, symbol, amount, price):
        """创建限价卖单"""
        if environment_manager.should_use_real_api():
            # 使用真实API
            try:
                return self.exchange.create_limit_sell_order(symbol, amount, price)
            except Exception as e:
                self.logger.error(f"创建真实限价卖单失败: {e}")
                # 降级到模拟交易
                return self._simulate_limit_sell_order(symbol, amount, price)
        else:
            # 使用模拟交易
            return self._simulate_limit_sell_order(symbol, amount, price)
    
    def cancel_order(self, order_id, symbol):
        """取消订单"""
        if environment_manager.should_use_real_api():
            # 使用真实API
            try:
                return self.exchange.cancel_order(order_id, symbol)
            except Exception as e:
                self.logger.error(f"取消真实订单失败: {e}")
                # 降级到模拟操作
                return self._simulate_cancel_order(order_id, symbol)
        else:
            # 使用模拟操作
            return self._simulate_cancel_order(order_id, symbol)
    
    def fetch_order(self, order_id, symbol):
        """查询订单状态"""
        if environment_manager.should_use_real_api():
            # 使用真实API
            try:
                return self.exchange.fetch_order(order_id, symbol)
            except Exception as e:
                self.logger.error(f"查询真实订单失败: {e}")
                # 降级到模拟数据
                return self._simulate_fetch_order(order_id, symbol)
        else:
            # 使用模拟数据
            return self._simulate_fetch_order(order_id, symbol)
    
    def _generate_simulated_ticker(self, symbol):
        """生成模拟价格数据"""
        # 模拟价格波动
        change = random.uniform(-self.price_volatility, self.price_volatility)
        self.last_simulated_price *= (1 + change + self.price_trend)
        
        # 确保价格在合理范围内
        if symbol == 'CFX/USDT':
            self.last_simulated_price = max(0.1, min(1.0, self.last_simulated_price))
        
        return {
            'symbol': symbol,
            'last': self.last_simulated_price,
            'bid': self.last_simulated_price * 0.999,
            'ask': self.last_simulated_price * 1.001,
            'high': self.last_simulated_price * 1.02,
            'low': self.last_simulated_price * 0.98,
            'volume': random.uniform(100000, 1000000),
            'timestamp': int(time.time() * 1000)
        }
    
    def _generate_simulated_orderbook(self, symbol, limit):
        """生成模拟订单簿"""
        current_price = self.last_simulated_price
        
        bids = []
        asks = []
        
        for i in range(limit):
            bid_price = current_price * (1 - (i + 1) * 0.001)
            ask_price = current_price * (1 + (i + 1) * 0.001)
            
            bid_amount = random.uniform(100, 10000)
            ask_amount = random.uniform(100, 10000)
            
            bids.append([bid_price, bid_amount])
            asks.append([ask_price, ask_amount])
        
        return {
            'symbol': symbol,
            'bids': bids,
            'asks': asks,
            'timestamp': int(time.time() * 1000)
        }
    
    def _generate_simulated_trades(self, symbol, limit):
        """生成模拟交易历史"""
        trades = []
        
        for i in range(limit):
            trade = {
                'id': f"sim_trade_{int(time.time())}_{i}",
                'timestamp': int(time.time() * 1000) - i * 1000,
                'symbol': symbol,
                'side': random.choice(['buy', 'sell']),
                'amount': random.uniform(10, 1000),
                'price': self.last_simulated_price * random.uniform(0.999, 1.001),
                'cost': 0,
                'fee': {'cost': 0, 'currency': 'USDT'}
            }
            trade['cost'] = trade['amount'] * trade['price']
            trades.append(trade)
        
        return trades
    
    def _simulate_market_buy_order(self, symbol, amount):
        """模拟市价买单"""
        order_id = f"sim_buy_{self.order_id_counter}"
        self.order_id_counter += 1
        
        current_price = self.last_simulated_price
        cost = amount * current_price
        
        # 检查余额
        base_currency = symbol.split('/')[1]  # USDT
        if self.simulated_balance[base_currency]['free'] < cost:
            raise Exception("余额不足")
        
        # 更新余额
        self.simulated_balance[base_currency]['free'] -= cost
        self.simulated_balance[base_currency]['used'] += cost
        
        quote_currency = symbol.split('/')[0]  # CFX
        self.simulated_balance[quote_currency]['free'] += amount
        self.simulated_balance[quote_currency]['total'] += amount
        
        order = {
            'id': order_id,
            'symbol': symbol,
            'type': 'market',
            'side': 'buy',
            'amount': amount,
            'price': current_price,
            'cost': cost,
            'filled': amount,
            'remaining': 0,
            'status': 'closed',
            'timestamp': int(time.time() * 1000)
        }
        
        self.simulated_orders[order_id] = order
        self.logger.info(f"模拟买单执行: {amount} {symbol} @ {current_price}")
        
        return order
    
    def _simulate_market_sell_order(self, symbol, amount):
        """模拟市价卖单"""
        order_id = f"sim_sell_{self.order_id_counter}"
        self.order_id_counter += 1
        
        current_price = self.last_simulated_price
        cost = amount * current_price
        
        # 检查余额
        quote_currency = symbol.split('/')[0]  # CFX
        if self.simulated_balance[quote_currency]['free'] < amount:
            raise Exception("余额不足")
        
        # 更新余额
        self.simulated_balance[quote_currency]['free'] -= amount
        self.simulated_balance[quote_currency]['used'] += amount
        
        base_currency = symbol.split('/')[1]  # USDT
        self.simulated_balance[base_currency]['free'] += cost
        self.simulated_balance[base_currency]['total'] += cost
        
        order = {
            'id': order_id,
            'symbol': symbol,
            'type': 'market',
            'side': 'sell',
            'amount': amount,
            'price': current_price,
            'cost': cost,
            'filled': amount,
            'remaining': 0,
            'status': 'closed',
            'timestamp': int(time.time() * 1000)
        }
        
        self.simulated_orders[order_id] = order
        self.logger.info(f"模拟卖单执行: {amount} {symbol} @ {current_price}")
        
        return order
    
    def _simulate_limit_buy_order(self, symbol, amount, price):
        """模拟限价买单"""
        order_id = f"sim_limit_buy_{self.order_id_counter}"
        self.order_id_counter += 1
        
        order = {
            'id': order_id,
            'symbol': symbol,
            'type': 'limit',
            'side': 'buy',
            'amount': amount,
            'price': price,
            'cost': amount * price,
            'filled': 0,
            'remaining': amount,
            'status': 'open',
            'timestamp': int(time.time() * 1000)
        }
        
        self.simulated_orders[order_id] = order
        self.logger.info(f"模拟限价买单创建: {amount} {symbol} @ {price}")
        
        return order
    
    def _simulate_limit_sell_order(self, symbol, amount, price):
        """模拟限价卖单"""
        order_id = f"sim_limit_sell_{self.order_id_counter}"
        self.order_id_counter += 1
        
        order = {
            'id': order_id,
            'symbol': symbol,
            'type': 'limit',
            'side': 'sell',
            'amount': amount,
            'price': price,
            'cost': amount * price,
            'filled': 0,
            'remaining': amount,
            'status': 'open',
            'timestamp': int(time.time() * 1000)
        }
        
        self.simulated_orders[order_id] = order
        self.logger.info(f"模拟限价卖单创建: {amount} {symbol} @ {price}")
        
        return order
    
    def _simulate_cancel_order(self, order_id, symbol):
        """模拟取消订单"""
        if order_id in self.simulated_orders:
            order = self.simulated_orders[order_id]
            order['status'] = 'canceled'
            self.logger.info(f"模拟订单取消: {order_id}")
            return order
        else:
            raise Exception(f"订单不存在: {order_id}")
    
    def _simulate_fetch_order(self, order_id, symbol):
        """模拟查询订单"""
        if order_id in self.simulated_orders:
            return self.simulated_orders[order_id]
        else:
            raise Exception(f"订单不存在: {order_id}")
    
    def get_environment_info(self):
        """获取当前环境信息"""
        return {
            'mode': environment_manager.get_current_mode(),
            'use_real_api': environment_manager.should_use_real_api(),
            'use_sandbox': environment_manager.should_use_sandbox(),
            'strategy_name': self.strategy_name
        }
