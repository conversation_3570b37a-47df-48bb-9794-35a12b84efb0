#!/usr/bin/env python3
"""
检查PyInstaller构建结果
"""

import os
import sys

def check_build_result():
    """检查构建结果"""
    print("=" * 60)
    print("PyInstaller 构建结果检查")
    print("=" * 60)
    
    # 检查dist目录
    if os.path.exists('dist'):
        print("✓ dist目录存在")
        
        # 列出所有exe文件
        exe_files = []
        for file in os.listdir('dist'):
            if file.endswith('.exe'):
                exe_files.append(file)
                file_path = os.path.join('dist', file)
                file_size = os.path.getsize(file_path)
                print(f"✓ 找到可执行文件: {file}")
                print(f"  📁 路径: {file_path}")
                print(f"  📊 大小: {file_size:,} 字节 ({file_size/1024/1024:.1f} MB)")
        
        if exe_files:
            print(f"\n🎉 构建成功！生成了 {len(exe_files)} 个可执行文件")
            
            # 重命名为更好的名称
            for exe in exe_files:
                old_path = os.path.join('dist', exe)
                if exe == 'MyApp.exe':
                    new_path = os.path.join('dist', 'QuantTradingSystem.exe')
                    try:
                        if os.path.exists(new_path):
                            os.remove(new_path)
                        os.rename(old_path, new_path)
                        print(f"✓ 已重命名: {exe} -> QuantTradingSystem.exe")
                    except Exception as e:
                        print(f"重命名失败: {e}")
            
            print("\n🚀 使用方法:")
            print("1. 双击运行: dist\\QuantTradingSystem.exe")
            print("2. 或在命令行运行: .\\dist\\QuantTradingSystem.exe")
            print("3. 可以将exe文件复制到其他电脑使用")
            
            return True
        else:
            print("✗ dist目录中没有找到exe文件")
            return False
    else:
        print("✗ dist目录不存在")
        return False

def check_build_logs():
    """检查构建日志"""
    print("\n" + "=" * 60)
    print("构建日志检查")
    print("=" * 60)
    
    # 检查build目录
    if os.path.exists('build'):
        print("✓ build目录存在")
        
        # 查找警告文件
        for root, dirs, files in os.walk('build'):
            for file in files:
                if 'warn' in file.lower():
                    warn_file = os.path.join(root, file)
                    print(f"📋 警告文件: {warn_file}")
                    
                    try:
                        with open(warn_file, 'r', encoding='utf-8') as f:
                            warnings = f.read()
                            if warnings.strip():
                                print("⚠️ 构建警告:")
                                print(warnings[:500] + "..." if len(warnings) > 500 else warnings)
                            else:
                                print("✓ 没有构建警告")
                    except Exception as e:
                        print(f"读取警告文件失败: {e}")
    else:
        print("- build目录不存在")

def create_run_script():
    """创建运行脚本"""
    print("\n" + "=" * 60)
    print("创建便捷脚本")
    print("=" * 60)
    
    # 创建运行脚本
    run_script = '''@echo off
echo 启动量化交易系统...
echo ========================

if exist "dist\\QuantTradingSystem.exe" (
    echo 正在启动...
    start "" "dist\\QuantTradingSystem.exe"
    echo 系统已启动！
) else if exist "dist\\MyApp.exe" (
    echo 正在启动...
    start "" "dist\\MyApp.exe"
    echo 系统已启动！
) else (
    echo 错误：找不到可执行文件
    echo 请先运行构建脚本
    pause
)
'''
    
    try:
        with open('run_trading_system.bat', 'w', encoding='utf-8') as f:
            f.write(run_script)
        print("✓ 已创建运行脚本: run_trading_system.bat")
    except Exception as e:
        print(f"✗ 创建运行脚本失败: {e}")
    
    # 创建测试脚本
    test_script = '''@echo off
echo 测试量化交易系统...
echo ========================

if exist "dist\\QuantTradingSystem.exe" (
    echo 测试可执行文件...
    "dist\\QuantTradingSystem.exe" --help 2>nul
    if %ERRORLEVEL% EQU 0 (
        echo ✓ 可执行文件正常
    ) else (
        echo - 可执行文件可能需要GUI环境
    )
) else (
    echo ✗ 找不到可执行文件
)

echo.
echo 文件信息:
if exist "dist\\QuantTradingSystem.exe" dir "dist\\QuantTradingSystem.exe"
if exist "dist\\MyApp.exe" dir "dist\\MyApp.exe"

pause
'''
    
    try:
        with open('test_exe.bat', 'w', encoding='utf-8') as f:
            f.write(test_script)
        print("✓ 已创建测试脚本: test_exe.bat")
    except Exception as e:
        print(f"✗ 创建测试脚本失败: {e}")

def main():
    """主函数"""
    success = check_build_result()
    check_build_logs()
    create_run_script()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 PyInstaller 构建完成！")
        print("\n📋 总结:")
        print("✅ 可执行文件已生成")
        print("✅ 可以独立运行")
        print("✅ 无需Python环境")
        print("\n🚀 下一步:")
        print("1. 运行 run_trading_system.bat 启动系统")
        print("2. 或直接双击 dist\\QuantTradingSystem.exe")
        print("3. 测试所有功能是否正常")
    else:
        print("❌ 构建可能未完成或失败")
        print("\n🔧 建议:")
        print("1. 检查上面的错误信息")
        print("2. 重新运行构建命令")
        print("3. 查看构建日志")
    print("=" * 60)

if __name__ == "__main__":
    main()
