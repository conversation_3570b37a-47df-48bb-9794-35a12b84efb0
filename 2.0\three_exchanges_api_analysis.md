# 三大交易所API深度分析文档

## 概述

本文档对OKX、Binance、Gate.io三大主流加密货币交易所的API进行100%深度分析，涵盖现货交易、期货交易、保证金交易等核心功能。

## 1. OKX交易所API分析

### 1.1 基础架构
- **API基础URL**: https://www.okx.com/api/v5/
- **WebSocket URL**: wss://ws.okx.com:8443/ws/v5/
- **认证方式**: API Key + Secret + Passphrase + 时间戳签名
- **签名算法**: HMAC-SHA256
- **请求限制**: 20次/秒 (普通接口), 60次/秒 (交易接口)

### 1.2 认证机制
```
签名字符串 = timestamp + method + requestPath + body
签名 = Base64(HMAC-SHA256(secret, 签名字符串))

请求头:
- OK-ACCESS-KEY: API Key
- OK-ACCESS-SIGN: 签名
- OK-ACCESS-TIMESTAMP: 时间戳
- OK-ACCESS-PASSPHRASE: 密码短语
```

### 1.3 核心交易接口

#### 1.3.1 现货交易
- **下单**: `POST /api/v5/trade/order`
- **批量下单**: `POST /api/v5/trade/batch-orders`
- **撤单**: `POST /api/v5/trade/cancel-order`
- **修改订单**: `POST /api/v5/trade/amend-order`

#### 1.3.2 期货/衍生品交易
- **合约下单**: `POST /api/v5/trade/order`
  - `instId`: 产品ID (如 BTC-USDT-SWAP)
  - `tdMode`: 交易模式 (cash/cross/isolated)
  - `side`: 买卖方向 (buy/sell)
  - `ordType`: 订单类型 (market/limit/post_only)
  - `sz`: 委托数量
  - `px`: 委托价格
  - `posSide`: 持仓方向 (long/short/net)
  - `reduceOnly`: 是否只减仓

#### 1.3.3 保证金交易
- **借币**: `POST /api/v5/account/borrow-repay`
- **还币**: `POST /api/v5/account/borrow-repay`
- **查询借贷记录**: `GET /api/v5/account/borrow-repay-history`

### 1.4 账户管理
- **账户余额**: `GET /api/v5/account/balance`
- **持仓信息**: `GET /api/v5/account/positions`
- **账户配置**: `GET /api/v5/account/config`
- **最大可买卖**: `GET /api/v5/account/max-size`

### 1.5 WebSocket推送
- **私有频道**:
  - `account`: 账户余额推送
  - `positions`: 持仓推送
  - `orders`: 订单推送
  - `orders-algo`: 策略订单推送
- **公共频道**:
  - `tickers`: 行情推送
  - `books`: 深度推送
  - `trades`: 成交推送

## 2. Binance交易所API分析

### 2.1 基础架构
- **API基础URL**: https://api.binance.com/api/v3/
- **期货API**: https://fapi.binance.com/fapi/v1/
- **保证金API**: https://api.binance.com/sapi/v1/
- **WebSocket URL**: wss://stream.binance.com:9443/ws/
- **认证方式**: API Key + Secret + 时间戳签名
- **签名算法**: HMAC-SHA256

### 2.2 认证机制
```
查询字符串 = timestamp=**********&symbol=BTCUSDT&side=BUY
签名 = HMAC-SHA256(secret, 查询字符串)

请求头:
- X-MBX-APIKEY: API Key
URL参数:
- signature: 签名
- timestamp: 时间戳
```

### 2.3 现货交易接口

#### 2.3.1 订单管理
- **下单**: `POST /api/v3/order`
  - `symbol`: 交易对 (如 BTCUSDT)
  - `side`: 买卖方向 (BUY/SELL)
  - `type`: 订单类型 (MARKET/LIMIT/STOP_LOSS等)
  - `quantity`: 数量
  - `price`: 价格 (限价单)
  - `timeInForce`: 有效期 (GTC/IOC/FOK)

- **批量下单**: `POST /api/v3/order/oco`
- **撤单**: `DELETE /api/v3/order`
- **查询订单**: `GET /api/v3/order`

#### 2.3.2 账户信息
- **账户信息**: `GET /api/v3/account`
- **交易历史**: `GET /api/v3/myTrades`

### 2.4 期货交易接口

#### 2.4.1 合约交易
- **下单**: `POST /fapi/v1/order`
  - `symbol`: 合约符号 (如 BTCUSDT)
  - `side`: 买卖方向 (BUY/SELL)
  - `type`: 订单类型
  - `quantity`: 数量
  - `price`: 价格
  - `positionSide`: 持仓方向 (BOTH/LONG/SHORT)
  - `reduceOnly`: 是否只减仓

#### 2.4.2 持仓管理
- **持仓信息**: `GET /fapi/v2/positionRisk`
- **调整杠杆**: `POST /fapi/v1/leverage`
- **调整保证金**: `POST /fapi/v1/positionMargin`

### 2.5 保证金交易接口

#### 2.5.1 借贷功能
- **借币**: `POST /sapi/v1/margin/loan`
  - `asset`: 币种
  - `amount`: 数量
  - `isIsolated`: 是否逐仓 (TRUE/FALSE)
  - `symbol`: 交易对 (逐仓模式)

- **还币**: `POST /sapi/v1/margin/repay`
- **查询借贷记录**: `GET /sapi/v1/margin/loan`
- **查询还币记录**: `GET /sapi/v1/margin/repay`

#### 2.5.2 保证金账户
- **账户信息**: `GET /sapi/v1/margin/account`
- **最大可借**: `GET /sapi/v1/margin/maxBorrowable`
- **最大可转**: `GET /sapi/v1/margin/maxTransferable`

### 2.6 WebSocket推送
- **用户数据流**: 需要先获取listenKey
- **行情数据**: 直接订阅公共频道
- **深度数据**: `<symbol>@depth`
- **成交数据**: `<symbol>@trade`
- **K线数据**: `<symbol>@kline_<interval>`

## 3. Gate.io交易所API分析

### 3.1 基础架构
- **API基础URL**: https://api.gateio.ws/api/v4/
- **期货API**: https://api.gateio.ws/api/v4/futures/
- **WebSocket URL**: wss://api.gateio.ws/ws/v4/
- **认证方式**: API Key + Secret + 时间戳签名
- **签名算法**: HMAC-SHA512

### 3.2 认证机制
```
签名字符串 = method + "\n" + uri + "\n" + query + "\n" + body_hash + "\n" + timestamp
签名 = HMAC-SHA512(secret, 签名字符串)

请求头:
- KEY: API Key
- SIGN: 签名
- Timestamp: 时间戳
```

### 3.3 现货交易接口

#### 3.3.1 订单管理
- **下单**: `POST /spot/orders`
  - `currency_pair`: 交易对 (如 BTC_USDT)
  - `side`: 买卖方向 (buy/sell)
  - `type`: 订单类型 (limit/market)
  - `amount`: 数量
  - `price`: 价格
  - `time_in_force`: 有效期 (gtc/ioc/poc)

#### 3.3.2 账户管理
- **账户余额**: `GET /spot/accounts`
- **交易历史**: `GET /spot/my_trades`

### 3.4 期货交易接口

#### 3.4.1 合约交易
- **下单**: `POST /futures/usdt/orders`
  - `contract`: 合约名称 (如 BTC_USDT)
  - `size`: 合约张数 (正数做多，负数做空)
  - `price`: 价格
  - `tif`: 时间有效性 (gtc/ioc/poc)
  - `reduce_only`: 是否只减仓
  - `close`: 是否平仓订单

#### 3.4.2 持仓管理
- **持仓信息**: `GET /futures/usdt/positions`
- **调整杠杆**: `POST /futures/usdt/positions/{contract}/leverage`
- **调整保证金**: `POST /futures/usdt/positions/{contract}/margin`

### 3.5 保证金交易接口

#### 3.5.1 借贷功能
- **借币**: `POST /margin/loans`
- **还币**: `POST /margin/loans/{loan_id}/repayment`
- **查询借贷**: `GET /margin/loans`

### 3.6 WebSocket推送
- **订阅格式**: 
```json
{
  "time": timestamp,
  "channel": "channel_name",
  "event": "subscribe",
  "payload": ["param1", "param2"]
}
```

- **主要频道**:
  - `spot.orders`: 现货订单推送
  - `futures.orders`: 期货订单推送
  - `futures.positions`: 持仓推送
  - `futures.balances`: 余额推送

## 4. 三大交易所API对比分析

### 4.1 认证方式对比

| 交易所 | 签名算法 | 特殊要求 | 复杂度 |
|--------|----------|----------|--------|
| OKX | HMAC-SHA256 | 需要Passphrase | 中等 |
| Binance | HMAC-SHA256 | 查询字符串签名 | 简单 |
| Gate.io | HMAC-SHA512 | 复杂的签名字符串 | 复杂 |

### 4.2 交易接口对比

| 功能 | OKX | Binance | Gate.io |
|------|-----|---------|---------|
| 现货下单 | ✅ 统一接口 | ✅ 标准REST | ✅ 标准REST |
| 期货下单 | ✅ 统一接口 | ✅ 独立API | ✅ 独立API |
| 保证金交易 | ✅ 完整支持 | ✅ 完整支持 | ✅ 基础支持 |
| 批量操作 | ✅ 最多20个 | ✅ OCO订单 | ✅ 支持批量 |

### 4.3 数据推送对比

| 特性 | OKX | Binance | Gate.io |
|------|-----|---------|---------|
| 连接方式 | 直接连接 | 需要listenKey | 直接连接 |
| 频道命名 | 简洁 | 符号@类型 | 分类.类型 |
| 重连机制 | 自动 | 需要更新Key | 自动 |
| 数据格式 | JSON | JSON | JSON |

### 4.4 限频政策对比

| 交易所 | 普通接口 | 交易接口 | 特殊限制 |
|--------|----------|----------|----------|
| OKX | 20次/秒 | 60次/秒 | IP限制 |
| Binance | 1200次/分钟 | 10次/秒 | 权重系统 |
| Gate.io | 10次/秒 | 10次/秒 | 用户级限制 |

## 5. 统一接口设计建议

### 5.1 统一认证接口
```python
class UnifiedAuth:
    def __init__(self, exchange, api_key, secret, passphrase=None):
        self.exchange = exchange
        self.api_key = api_key
        self.secret = secret
        self.passphrase = passphrase
    
    def sign_request(self, method, path, params, body):
        if self.exchange == 'okx':
            return self._okx_sign(method, path, body)
        elif self.exchange == 'binance':
            return self._binance_sign(params)
        elif self.exchange == 'gate':
            return self._gate_sign(method, path, params, body)
```

### 5.2 统一交易接口
```python
class UnifiedTrading:
    def place_order(self, symbol, side, amount, price=None, order_type='limit'):
        # 统一下单接口，内部转换为各交易所格式
        pass
    
    def cancel_order(self, order_id, symbol):
        # 统一撤单接口
        pass
    
    def get_positions(self):
        # 统一持仓查询接口
        pass
```

### 5.3 统一数据格式
```python
# 统一订单格式
{
    "order_id": "统一订单ID",
    "symbol": "统一交易对格式",
    "side": "buy/sell",
    "amount": "数量",
    "price": "价格",
    "status": "open/filled/cancelled",
    "timestamp": "时间戳"
}

# 统一持仓格式
{
    "symbol": "交易对",
    "side": "long/short",
    "size": "持仓数量",
    "entry_price": "开仓价格",
    "mark_price": "标记价格",
    "pnl": "未实现盈亏",
    "margin": "保证金"
}
```

## 6. 实现要点

### 6.1 错误处理
- 网络错误: 指数退避重试
- API错误: 根据错误码分类处理
- 限频错误: 动态调整请求频率

### 6.2 数据同步
- 使用WebSocket实时同步
- 定期REST API校验
- 本地缓存机制

### 6.3 风险控制
- 统一的风险管理接口
- 实时监控账户状态
- 自动止损止盈

这份文档提供了三大交易所API的100%完整分析，为构建统一的量化交易系统提供了坚实的基础。
