"""
量化交易策略模块

本模块包含了量化交易系统的所有核心交易策略，每种策略都针对不同的市场情况和交易风格设计。
所有策略都经过严格的参数验证和错误处理，确保在各种市场环境下的稳定运行。

包含的策略类型：
1. GridTrading - 网格交易策略：适合震荡市场，通过高抛低吸获取稳定收益
2. MovingAverageStrategy - 移动平均线策略：适合趋势市场，跟随趋势方向交易
3. RSIStrategy - RSI策略：基于相对强弱指标，适合超买超卖信号交易
4. VolumeBreakoutStrategy - 成交量突破策略：基于成交量异常，捕捉价格突破机会

使用说明：
- 所有策略都需要传入交易所对象、交易对、相关参数
- 策略会自动进行参数验证，确保参数的合理性和安全性
- 支持模拟模式和实盘模式，建议先在模拟模式下充分测试
- 每个策略都有详细的日志记录，便于监控和调试

注意事项：
- 量化交易存在风险，请充分了解策略原理后使用
- 建议从小资金开始测试，逐步增加投资规模
- 定期监控策略表现，及时调整参数或停止策略
- 在市场剧烈波动时要特别谨慎

作者：量化交易系统开发团队
版本：v2.0 (增强版，包含参数验证和中文错误处理)
"""
import ccxt
import time
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from logger import get_logger, log_trade, log_error, log_strategy_start, log_strategy_stop
from parameter_validator import validator, ValidationError
from error_handler import error_handler, ErrorCode

class GridTrading:
    """
    网格交易策略类

    网格交易是一种适合震荡市场的量化交易策略。它在当前价格上下设置多个买入和卖出订单，
    形成一个"网格"，当价格在网格范围内波动时，通过高抛低吸来获取收益。

    策略原理：
    1. 以当前价格为基准，按照设定的间距在上下方向设置网格
    2. 在价格下方设置买入订单，在价格上方设置卖出订单
    3. 当价格波动触发订单成交时，在相应位置重新设置反向订单
    4. 通过不断的买低卖高来获取价差收益

    适用场景：
    - 震荡市场：价格在一定范围内波动，没有明显趋势
    - 流动性好的交易对：确保订单能够及时成交
    - 波动率适中：太小无利润，太大风险高

    风险提示：
    - 单边趋势市场可能导致大量亏损
    - 需要充足的资金支持网格运行
    - 网格间距设置不当可能影响收益

    参数说明：
    - exchange: 交易所对象，用于执行交易操作
    - symbol: 交易对，如 'CFX/USDT'
    - base_price: 基准价格，网格的中心价格
    - grid_spacing: 网格间距（百分比），如 2.0 表示 2%
    - grid_count: 网格数量，建议 10-30 个
    - order_amount: 每个网格的交易数量

    使用示例：
    ```python
    # 创建网格交易策略
    strategy = GridTrading(
        exchange=exchange,
        symbol='CFX/USDT',
        base_price=0.213456,
        grid_spacing=2.0,      # 2% 间距
        grid_count=10,         # 10 个网格
        order_amount=100       # 每次交易 100 个代币
    )

    # 启动策略
    strategy.start()

    # 停止策略
    strategy.stop()
    ```
    """

    def __init__(self, exchange, symbol, base_price, grid_spacing, grid_count, order_amount):
        """
        初始化网格交易策略

        Args:
            exchange: 交易所对象，必须实现标准的交易接口
            symbol (str): 交易对符号，格式如 'CFX/USDT'
            base_price (float): 基准价格，作为网格的中心价格
            grid_spacing (float): 网格间距百分比，如 2.0 表示 2%
            grid_count (int): 网格总数量，建议 10-30 个
            order_amount (float): 每个网格的交易数量

        Raises:
            ValidationError: 当参数不符合要求时抛出
            Exception: 当策略初始化失败时抛出

        注意事项：
            - 基准价格建议设置为当前市场价格
            - 网格间距不宜过小（<0.5%）或过大（>10%）
            - 网格数量过多会占用大量资金，过少可能错过机会
            - 交易数量应根据账户资金合理设置
        """
        # 初始化日志记录器
        self.logger = get_logger("grid_trading")

        try:
            # 第一步：严格的参数验证，确保所有参数都在合理范围内
            self._validate_parameters(symbol, base_price, grid_spacing, grid_count, order_amount)

            # 第二步：初始化策略属性
            self.exchange = exchange                    # 交易所接口对象
            self.symbol = symbol                        # 交易对，如 'CFX/USDT'
            self.base_price = base_price               # 网格中心价格，通常设为当前市价
            self.grid_spacing = grid_spacing           # 网格间距百分比，如 2.0 表示 2%
            self.grid_count = grid_count               # 网格总数量，决定策略覆盖的价格范围
            self.order_amount = order_amount           # 每个网格的交易数量

            # 第三步：初始化运行状态变量
            self.orders = {}                           # 存储当前活跃的订单信息 {价格: 订单详情}
            self.running = False                       # 策略运行状态标志
            self.last_price = None                     # 缓存最近一次获取的价格，避免频繁API调用
            self.price_check_counter = 0               # 价格检查计数器，用于性能监控

            # 记录成功初始化的日志
            self.logger.info(f"网格交易策略初始化成功: {symbol}, 基准价格: {base_price}, 网格间距: {grid_spacing}%, 网格数量: {grid_count}")

        except ValidationError as e:
            # 参数验证失败的处理
            error_details = error_handler.handle_error(e, context={
                "strategy": "GridTrading",
                "symbol": symbol,
                "base_price": base_price,
                "grid_spacing": grid_spacing,
                "grid_count": grid_count,
                "order_amount": order_amount
            })
            self.logger.error(f"网格交易策略参数验证失败: {error_details['message']}")
            raise Exception(error_handler.format_user_message(error_details))
        except Exception as e:
            # 其他初始化错误的处理
            error_details = error_handler.handle_error(e, ErrorCode.STRATEGY_INIT_FAILED)
            self.logger.error(f"网格交易策略初始化失败: {error_details['message']}")
            raise Exception(error_handler.format_user_message(error_details))

    def _validate_parameters(self, symbol, base_price, grid_spacing, grid_count, order_amount):
        """
        验证网格交易策略的所有参数

        这个方法会对传入的参数进行严格验证，确保参数在合理范围内，
        避免因参数设置不当导致的策略失败或资金损失。

        Args:
            symbol (str): 交易对符号
            base_price (float): 基准价格
            grid_spacing (float): 网格间距百分比
            grid_count (int): 网格数量
            order_amount (float): 每次交易数量

        Raises:
            ValidationError: 当任何参数不符合要求时抛出

        验证规则：
            - 交易对格式必须正确（如 CFX/USDT）
            - 基准价格必须为正数且在合理范围内
            - 网格间距建议在 0.1% - 20% 之间
            - 网格数量建议在 2 - 100 之间
            - 交易数量必须为正数且不超过限制
        """
        validator.validate_symbol(symbol)
        # 注意：这里将百分比转换为小数，如 2.0% -> 0.02
        validator.validate_grid_parameters(base_price, grid_spacing/100, grid_count, order_amount)
    
    def create_grid_orders(self):
        """
        创建网格订单

        这是网格交易策略的核心方法，会在当前价格上下创建买入和卖出订单，
        形成一个价格网格。当价格波动时，这些订单会被触发，实现高抛低吸。

        工作原理：
        1. 获取当前市场价格作为参考
        2. 以基准价格为中心，按网格间距计算各个网格价格
        3. 在低于当前价格的位置设置买入订单（等待价格下跌时买入）
        4. 在高于当前价格的位置设置卖出订单（等待价格上涨时卖出）

        Returns:
            None

        注意事项：
            - 确保账户有足够的资金支持所有订单
            - 网格订单一旦设置，会持续等待市场价格触发
            - 如果市场出现单边行情，可能导致订单全部在一侧成交
        """
        # 获取当前市场价格作为网格布局的参考
        current_price = self.get_current_price()

        # 计算网格范围：从 -网格数量/2 到 +网格数量/2
        # 例如：10个网格就是 -5 到 +5，跳过 0（当前价格位置）
        for i in range(-self.grid_count//2, self.grid_count//2 + 1):
            if i == 0:
                continue  # 跳过中心位置，不在当前价格设置订单

            # 计算每个网格的价格
            # 公式：网格价格 = 当前价格 × (1 + 网格位置 × 间距百分比 / 100)
            grid_price = current_price * (1 + i * self.grid_spacing / 100)

            if i < 0:  # 负数位置：价格低于当前价格，设置买入订单
                try:
                    order = self.exchange.create_limit_buy_order(
                        self.symbol, self.order_amount, grid_price
                    )
                    self.orders[grid_price] = {'type': 'buy', 'order': order}
                    self.logger.info(f"买单已放置:{grid_price:.2f}")
                except Exception as e:
                    self.logger.error(f"买单失败:{e}")
            else:  # 上方网格，放卖单
                try:
                    order = self.exchange.create_limit_sell_order(
                        self.symbol, self.order_amount, grid_price
                    )
                    self.orders[grid_price] = {'type': 'sell', 'order': order}
                    self.logger.info(f"卖单已放置:{grid_price:.2f}")
                except Exception as e:
                    self.logger.error(f"卖单失败: {e}")
    
    def get_current_price(self):
        """获取当前价格"""
        try:
            # 方法1: 使用ticker获取最新价格
            ticker = self.exchange.fetch_ticker(self.symbol)
            price = float(ticker['last'])

            if price > 0:
                self.last_price = price  # 缓存成功获取的价格
                return price

            # 方法2: 如果ticker价格异常，使用orderbook
            orderbook = self.exchange.fetch_order_book(self.symbol, limit=5)
            if orderbook['bids'] and orderbook['asks']:
                bid_price = float(orderbook['bids'][0][0])
                ask_price = float(orderbook['asks'][0][0])
                price = (bid_price + ask_price) / 2
                self.last_price = price  # 缓存成功获取的价格
                return price

            # 方法3: 使用最近交易记录
            trades = self.exchange.fetch_trades(self.symbol, limit=1)
            if trades:
                price = float(trades[0]['price'])
                self.last_price = price  # 缓存成功获取的价格
                return price

            # 如果所有方法都失败，返回默认值并记录错误
            self.logger.error("无法获取有效价格，使用缓存价格")
            return self.last_price if self.last_price else 0.0

        except Exception as e:
            self.logger.error(f"获取价格失败: {e}")
            # 返回上次成功获取的价格，如果没有则返回0
            return self.last_price if self.last_price else 0.0
    
    def check_and_rebalance(self):
        """检查订单状态并重新平衡"""
        for price, order_info in list(self.orders.items()):
            try:
                order_status = self.exchange.fetch_order(
                    order_info['order']['id'], self.symbol
                )
                
                if order_status['status'] == 'closed':
                    self.logger.info(f"订单已成交:{price:.2f},类型:{order_info['type']}")
                    # 在相反位置放置反向订单
                    if order_info['type'] == 'buy':
                        # 买单成交了，在更高价格放卖单
                        new_price = price * (1 + self.grid_spacing / 100)
                        new_order = self.exchange.create_limit_sell_order(
                            self.symbol, self.order_amount, new_price
                        )
                        self.orders[new_price] = {'type': 'sell', 'order': new_order}
                    else:
                        # 卖单成交了，在更低价格放买单
                        new_price = price * (1 - self.grid_spacing / 100)
                        new_order = self.exchange.create_limit_buy_order(
                            self.symbol, self.order_amount, new_price
                        )
                        self.orders[new_price] = {'type': 'buy', 'order': new_order}
                    
                    # 删除已成交的订单
                    del self.orders[price]
            except Exception as e:
                self.logger.error(f"检查订单状态失败:{e}")
    
    def run(self):
        """运行网格策略"""
        self.logger.info("网格交易策略已启动")
        self.running = True
        self.create_grid_orders()

        while self.running:
            try:
                # 获取并显示当前价格
                current_price = self.get_current_price()
                self.logger.info(f"当前价格: {current_price:.6f}")

                # 每12次价格检查（约1分钟）检查一次订单状态
                self.price_check_counter += 1
                if self.price_check_counter >= 12:
                    self.check_and_rebalance()
                    self.price_check_counter = 0

                # 每5秒检查一次价格
                time.sleep(5)
            except KeyboardInterrupt:
                self.logger.info("网格交易停止")
                break
            except Exception as e:
                self.logger.error(f"运行错误:{e}")
                time.sleep(10)
    
    def stop(self):
        """停止策略"""
        self.running = False
        # 取消所有挂单
        try:
            for price, order_info in self.orders.items():
                self.exchange.cancel_order(order_info['order']['id'], self.symbol)
            self.orders.clear()
            self.logger.info("网格交易已停止，所有订单已取消")
        except Exception as e:
            self.logger.error(f"停止策略时出错:{e}")


class MovingAverageStrategy:
    """
    移动平均线交易策略类

    移动平均线策略是一种经典的趋势跟踪策略，通过比较短期和长期移动平均线的关系
    来判断市场趋势，并据此进行买入和卖出操作。

    策略原理：
    1. 计算短期移动平均线（如5日均线）和长期移动平均线（如20日均线）
    2. 当短期均线从下方穿越长期均线时，产生买入信号（金叉）
    3. 当短期均线从上方穿越长期均线时，产生卖出信号（死叉）
    4. 通过跟随趋势方向来获取收益

    适用场景：
    - 趋势明显的市场：有明确的上涨或下跌趋势
    - 流动性好的交易对：确保能够及时进出场
    - 中长期交易：适合持仓时间较长的交易风格

    风险提示：
    - 在震荡市场中可能产生频繁的假信号
    - 移动平均线具有滞后性，可能错过最佳进出场时机
    - 需要合理设置止损，避免趋势反转时的大幅亏损

    参数说明：
    - exchange: 交易所对象
    - symbol: 交易对，如 'CFX/USDT'
    - short_period: 短期均线周期，建议 5-20
    - long_period: 长期均线周期，建议 20-100
    - amount: 每次交易数量

    使用示例：
    ```python
    # 创建移动平均线策略
    strategy = MovingAverageStrategy(
        exchange=exchange,
        symbol='CFX/USDT',
        short_period=5,        # 5日短期均线
        long_period=20,        # 20日长期均线
        amount=100             # 每次交易100个代币
    )

    # 启动策略
    strategy.start()
    ```
    """
    def __init__(self, exchange, symbol, short_period=10, long_period=30, amount=100):
        self.logger = get_logger("moving_average")

        try:
            # 参数验证
            self._validate_parameters(symbol, short_period, long_period, amount)

            self.exchange = exchange
            self.symbol = symbol
            self.short_period = short_period
            self.long_period = long_period
            self.amount = amount
            self.position = 0  # 1表示持有多头，-1表示持有空头，0表示无仓
            self.last_action_time = 0
            self.running = False

            # 兼容旧参数名
            self.short_window = short_period
            self.long_window = long_period

            self.logger.info(f"移动平均线策略初始化成功: {symbol}, 短期: {short_period}, 长期: {long_period}, 数量: {amount}")

        except ValidationError as e:
            error_details = error_handler.handle_error(e, context={
                "strategy": "MovingAverageStrategy",
                "symbol": symbol,
                "short_period": short_period,
                "long_period": long_period,
                "amount": amount
            })
            self.logger.error(f"移动平均线策略参数验证失败: {error_details['message']}")
            raise Exception(error_handler.format_user_message(error_details))
        except Exception as e:
            error_details = error_handler.handle_error(e, ErrorCode.STRATEGY_INIT_FAILED)
            self.logger.error(f"移动平均线策略初始化失败: {error_details['message']}")
            raise Exception(error_handler.format_user_message(error_details))

    def _validate_parameters(self, symbol, short_period, long_period, amount):
        """验证移动平均线参数"""
        validator.validate_symbol(symbol)
        validator.validate_ma_parameters(short_period, long_period, amount)
    
    def get_historical_data(self, timeframe='1h', limit=100):
        """获取历史价格数据"""
        try:
            ohlcv = self.exchange.fetch_ohlcv(
                self.symbol, timeframe, limit=limit
            )
            df = pd.DataFrame(
                ohlcv,
                columns=['timestamp', 'open', 'high', 'low', 'close', 'volume']
            )
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            return df
        except Exception as e:
            self.logger.error(f"获取历史数据失败:{e}")
            return None
    
    def calculate_moving_averages(self, df):
        """计算移动平均线"""
        df['ma_short'] = df['close'].rolling(window=self.short_window).mean()
        df['ma_long'] = df['close'].rolling(window=self.long_window).mean()
        return df
    
    def generate_signals(self, df):
        """生成交易信号"""
        df['signal'] = 0
        df['position'] = 0
        
        # 金叉信号(短期均线上穿长期均线)
        golden_cross = (df['ma_short'] > df['ma_long']) & \
                      (df['ma_short'].shift(1) <= df['ma_long'].shift(1))
        
        # 死叉信号(短期均线下穿长期均线)
        death_cross = (df['ma_short'] < df['ma_long']) & \
                     (df['ma_short'].shift(1) >= df['ma_long'].shift(1))
        
        df.loc[golden_cross, 'signal'] = 1  # 买入信号
        df.loc[death_cross, 'signal'] = -1  # 卖出信号

        return df

    def execute_trade(self, signal, current_price):
        """执行交易"""
        current_time = time.time()
        # 防止频繁交易，至少间隔1小时
        if current_time - self.last_action_time < 3600:
            return

        try:
            balance = self.exchange.fetch_balance()
            if signal == 1 and self.position <= 0:  # 买入信号
                # 用70%的USDT买入
                usdt_balance = balance['USDT']['free']
                buy_amount = (usdt_balance * 0.7) / current_price
                if buy_amount > 0.001:  # 最小交易数量
                    order = self.exchange.create_market_buy_order(
                        self.symbol, buy_amount
                    )
                    self.position = 1
                    self.last_action_time = current_time
                    log_trade("moving_average", "买入", self.symbol, buy_amount, current_price, order.get('id'))
                    self.logger.info(f"买入执行:{buy_amount:.6f} @ {current_price:.2f}")

            elif signal == -1 and self.position >= 0:  # 卖出信号
                # 卖出所有持仓
                base_currency = self.symbol.split('/')[0]
                sell_amount = balance[base_currency]['free']
                if sell_amount > 0.001:
                    order = self.exchange.create_market_sell_order(
                        self.symbol, sell_amount
                    )
                    self.position = -1
                    self.last_action_time = current_time
                    log_trade("moving_average", "卖出", self.symbol, sell_amount, current_price, order.get('id'))
                    self.logger.info(f"卖出执行:{sell_amount:.6f} @ {current_price:.2f}")
        except Exception as e:
            log_error("moving_average", "交易执行失败", e)
            self.logger.error(f"交易执行失败:{e}")

    def run_backtest(self, days=30):
        """回测策略"""
        df = self.get_historical_data(timeframe='1h', limit=days*24)
        if df is None:
            return

        df = self.calculate_moving_averages(df)
        df = self.generate_signals(df)

        # 计算策略收益
        df['returns'] = df['close'].pct_change()
        df['strategy_returns'] = df['signal'].shift(1) * df['returns']

        # 计算累计收益
        df['cumulative_returns'] = (1 + df['returns']).cumprod()
        df['cumulative_strategy_returns'] = (1 + df['strategy_returns']).cumprod()

        # 打印回测结果
        total_return = df['cumulative_strategy_returns'].iloc[-1] - 1
        sharpe_ratio = df['strategy_returns'].mean() / df['strategy_returns'].std() * np.sqrt(24*365)

        self.logger.info(f"回测期间总收益:{total_return:.2%}")
        self.logger.info(f"夏普比率:{sharpe_ratio:.2f}")

        return df

    def run_live(self):
        """实时交易"""
        self.logger.info("开始移动平均线实时交易...")
        self.running = True

        while self.running:
            try:
                df = self.get_historical_data(timeframe='1h', limit=50)
                if df is None:
                    time.sleep(300)
                    continue

                df = self.calculate_moving_averages(df)
                df = self.generate_signals(df)

                # 获取最新信号
                latest_signal = df['signal'].iloc[-1]
                current_price = df['close'].iloc[-1]

                # 显示当前价格和均线状态
                short_ma = df['short_ma'].iloc[-1]
                long_ma = df['long_ma'].iloc[-1]
                trend = "上升" if short_ma > long_ma else "下降"
                self.logger.info(f"当前价格: {current_price:.6f}, 短期均线: {short_ma:.6f}, 长期均线: {long_ma:.6f}, 趋势: {trend}")

                if latest_signal != 0:
                    self.execute_trade(latest_signal, current_price)

                # 每5分钟检查一次
                time.sleep(300)
            except KeyboardInterrupt:
                self.logger.info("策略停止")
                break
            except Exception as e:
                log_error("moving_average", "运行错误", e)
                self.logger.error(f"运行错误: {e}")
                time.sleep(60)

    def get_current_price(self):
        """获取当前价格"""
        try:
            # 方法1: 使用ticker获取最新价格
            ticker = self.exchange.fetch_ticker(self.symbol)
            price = float(ticker['last'])

            if price > 0:
                self.last_price = getattr(self, 'last_price', price)
                self.last_price = price
                return price

            # 方法2: 如果ticker价格异常，使用orderbook
            orderbook = self.exchange.fetch_order_book(self.symbol, limit=5)
            if orderbook['bids'] and orderbook['asks']:
                bid_price = float(orderbook['bids'][0][0])
                ask_price = float(orderbook['asks'][0][0])
                price = (bid_price + ask_price) / 2
                self.last_price = price
                return price

            # 方法3: 使用最近交易记录
            trades = self.exchange.fetch_trades(self.symbol, limit=1)
            if trades:
                price = float(trades[0]['price'])
                self.last_price = price
                return price

            # 如果所有方法都失败，返回缓存价格
            return getattr(self, 'last_price', 0.0)

        except Exception as e:
            self.logger.error(f"获取价格失败: {e}")
            return getattr(self, 'last_price', 0.0)

    def stop(self):
        """停止策略"""
        self.running = False
        log_strategy_stop("moving_average")


class RSIStrategy:
    def __init__(self, exchange, symbol, period=14, oversold=30, overbought=70, amount=100):
        self.logger = get_logger("rsi_strategy")

        try:
            # 参数验证
            self._validate_parameters(symbol, period, oversold, overbought, amount)

            self.exchange = exchange
            self.symbol = symbol
            self.period = period
            self.oversold = oversold
            self.overbought = overbought
            self.amount = amount
            self.position = 0
            self.last_action_time = 0
            self.running = False

            # 兼容旧参数名
            self.rsi_period = period
            self.oversold_threshold = oversold
            self.overbought_threshold = overbought

            self.logger.info(f"RSI策略初始化成功: {symbol}, 周期: {period}, 超卖: {oversold}, 超买: {overbought}, 数量: {amount}")

        except ValidationError as e:
            error_details = error_handler.handle_error(e, context={
                "strategy": "RSIStrategy",
                "symbol": symbol,
                "period": period,
                "oversold": oversold,
                "overbought": overbought,
                "amount": amount
            })
            self.logger.error(f"RSI策略参数验证失败: {error_details['message']}")
            raise Exception(error_handler.format_user_message(error_details))
        except Exception as e:
            error_details = error_handler.handle_error(e, ErrorCode.STRATEGY_INIT_FAILED)
            self.logger.error(f"RSI策略初始化失败: {error_details['message']}")
            raise Exception(error_handler.format_user_message(error_details))

    def _validate_parameters(self, symbol, period, oversold, overbought, amount):
        """验证RSI参数"""
        validator.validate_symbol(symbol)
        validator.validate_rsi_parameters(period, oversold, overbought, amount)

    def calculate_rsi(self, prices, period=14):
        """计算RSI指标"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi

    def get_market_data(self, timeframe='1h', limit=100):
        """获取市场数据"""
        try:
            # 兼容Gate.io适配器和CCXT接口
            if hasattr(self.exchange, 'fetch_ohlcv'):
                ohlcv = self.exchange.fetch_ohlcv(self.symbol, timeframe, limit=limit)
            else:
                # 如果是Gate.io适配器
                ohlcv = self.exchange.fetch_ohlcv(self.symbol, timeframe, limit)

            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            # 计算RSI
            df['rsi'] = self.calculate_rsi(df['close'], self.rsi_period)
            return df
        except Exception as e:
            self.logger.error(f"获取市场数据失败:{e}")
            return None

    def generate_signals(self, df):
        """生成交易信号"""
        df['signal'] = 0
        # RSI超卖信号(RSI < 30)
        oversold_condition = df['rsi'] < self.oversold_threshold
        df.loc[oversold_condition, 'signal'] = 1
        # RSI超买信号(RSI > 70)
        overbought_condition = df['rsi'] > self.overbought_threshold
        df.loc[overbought_condition, 'signal'] = -1
        return df

    def calculate_position_size(self, current_price, risk_per_trade=0.02):
        """计算仓位大小"""
        try:
            balance = self.exchange.fetch_balance()
            account_balance = balance['USDT']['free']
            # 风险控制:每次交易不超过账户余额2%
            position_value = account_balance * risk_per_trade
            position_size = position_value / current_price
            return max(position_size, 0.001)  # 最小数量
        except Exception as e:
            self.logger.error(f"计算仓位大小失败: {e}")
            return 0.001

    def run_live(self):
        """实时运行RSI策略"""
        self.logger.info("开始RSI反转策略...")
        self.running = True

        while self.running:
            try:
                df = self.get_market_data(timeframe='1h', limit=50)
                if df is None:
                    time.sleep(300)
                    continue

                df = self.generate_signals(df)
                # 获取最新数据
                latest_signal = df['signal'].iloc[-1]
                current_price = df['close'].iloc[-1]
                current_rsi = df['rsi'].iloc[-1]

                self.logger.info(f"当前价格: {current_price:.6f}, RSI: {current_rsi:.2f}")

                if latest_signal != 0:
                    self.execute_trade(latest_signal, current_price, current_rsi)

                time.sleep(600)  # 10分钟检查一次
            except KeyboardInterrupt:
                self.logger.info("RSI策略停止")
                break
            except Exception as e:
                log_error("rsi_strategy", "运行错误", e)
                self.logger.error(f"运行错误: {e}")
                time.sleep(60)

    def get_current_price(self):
        """获取当前价格"""
        try:
            # 方法1: 使用ticker获取最新价格
            ticker = self.exchange.fetch_ticker(self.symbol)
            price = float(ticker['last'])

            if price > 0:
                self.last_price = getattr(self, 'last_price', price)
                self.last_price = price
                return price

            # 方法2: 如果ticker价格异常，使用orderbook
            orderbook = self.exchange.fetch_order_book(self.symbol, limit=5)
            if orderbook['bids'] and orderbook['asks']:
                bid_price = float(orderbook['bids'][0][0])
                ask_price = float(orderbook['asks'][0][0])
                price = (bid_price + ask_price) / 2
                self.last_price = price
                return price

            # 方法3: 使用最近交易记录
            trades = self.exchange.fetch_trades(self.symbol, limit=1)
            if trades:
                price = float(trades[0]['price'])
                self.last_price = price
                return price

            # 如果所有方法都失败，返回缓存价格
            return getattr(self, 'last_price', 0.0)

        except Exception as e:
            self.logger.error(f"获取价格失败: {e}")
            return getattr(self, 'last_price', 0.0)

    def stop(self):
        """停止策略"""
        self.running = False
        log_strategy_stop("rsi_strategy")

    def execute_trade(self, signal, current_price, current_rsi):
        """执行交易"""
        current_time = time.time()
        # 防止频繁交易
        if current_time - self.last_action_time < 1800:  # 30分钟间隔
            return

        try:
            if signal == 1 and self.position <= 0:  # 买入信号
                position_size = self.calculate_position_size(current_price)
                order = self.exchange.create_market_buy_order(self.symbol, position_size)
                self.position = 1
                self.last_action_time = current_time
                log_trade("rsi_strategy", "买入", self.symbol, position_size, current_price, order.get('id'))
                self.logger.info(f"RSI超卖买入:{position_size:.6f} @ {current_price:.2f},RSI:{current_rsi:.2f}")

            elif signal == -1 and self.position >= 0:  # 卖出信号
                # 卖出所有持仓
                balance = self.exchange.fetch_balance()
                base_currency = self.symbol.split('/')[0]
                sell_amount = balance[base_currency]['free']
                if sell_amount > 0.001:
                    order = self.exchange.create_market_sell_order(self.symbol, sell_amount)
                    self.position = -1
                    self.last_action_time = current_time
                    log_trade("rsi_strategy", "卖出", self.symbol, sell_amount, current_price, order.get('id'))
                    self.logger.info(f"RSI超买卖出:{sell_amount:.6f} @ {current_price:.2f},RSI:{current_rsi:.2f}")
        except Exception as e:
            log_error("rsi_strategy", "交易执行失败", e)
            self.logger.error(f"交易执行失败:{e}")

    def run_backtest(self, days=60):
        """回测RSI策略"""
        df = self.get_market_data(timeframe='4h', limit=days*6)
        if df is None:
            return

        df = self.generate_signals(df)
        # 模拟交易
        capital = 10000  # 初始资金
        position = 0
        trades = []

        for i in range(len(df)):
            if pd.isna(df['rsi'].iloc[i]):
                continue

            current_price = df['close'].iloc[i]
            current_signal = df['signal'].iloc[i]
            current_rsi = df['rsi'].iloc[i]

            if current_signal == 1 and position <= 0:  # 买入
                position = capital / current_price
                capital = 0
                trades.append({
                    'type': 'buy',
                    'price': current_price,
                    'rsi': current_rsi,
                    'time': df['timestamp'].iloc[i]
                })
            elif current_signal == -1 and position > 0:  # 卖出
                capital = position * current_price
                position = 0
                trades.append({
                    'type': 'sell',
                    'price': current_price,
                    'rsi': current_rsi,
                    'time': df['timestamp'].iloc[i]
                })

        # 计算最终收益
        final_value = capital + position * df['close'].iloc[-1]
        total_return = (final_value - 10000) / 10000

        self.logger.info(f"RSI策略回测结果:")
        self.logger.info(f"初始资金:$10,000")
        self.logger.info(f"最终价值:${final_value:.2f}")
        self.logger.info(f"总收益率:{total_return:.2%}")
        self.logger.info(f"交易次数:{len(trades)}")

        return trades

    def run_live(self):
        """实时运行RSI策略"""
        self.logger.info("开始RSI反转策略...")
        self.running = True

        while self.running:
            try:
                df = self.get_market_data(timeframe='1h', limit=50)
                if df is None:
                    time.sleep(300)
                    continue

                df = self.generate_signals(df)
                # 获取最新数据
                latest_signal = df['signal'].iloc[-1]
                current_price = df['close'].iloc[-1]
                current_rsi = df['rsi'].iloc[-1]

                self.logger.info(f"当前价格: {current_price:.6f}, RSI: {current_rsi:.2f}")

                if latest_signal != 0:
                    self.execute_trade(latest_signal, current_price, current_rsi)

                time.sleep(600)  # 10分钟检查一次
            except KeyboardInterrupt:
                self.logger.info("RSI策略停止")
                break
            except Exception as e:
                log_error("rsi_strategy", "运行错误", e)
                self.logger.error(f"运行错误: {e}")
                time.sleep(60)

    def stop(self):
        """停止策略"""
        self.running = False
        log_strategy_stop("rsi_strategy")


class VolumeBreakoutStrategy:
    def __init__(self, exchange, symbol, lookback_period=20, volume_multiplier=2, breakout_threshold=0.02, amount=100):
        self.logger = get_logger("volume_breakout")

        try:
            # 参数验证
            self._validate_parameters(symbol, lookback_period, volume_multiplier, breakout_threshold, amount)

            self.exchange = exchange
            self.symbol = symbol
            self.lookback_period = lookback_period
            self.volume_multiplier = volume_multiplier
            self.breakout_threshold = breakout_threshold
            self.amount = amount
            self.position = 0
            self.entry_price = 0
            self.stop_loss_pct = 0.03  # 3%止损
            self.take_profit_pct = 0.06  # 6%止盈
            self.running = False

            self.logger.info(f"成交量突破策略初始化成功: {symbol}, 回看周期: {lookback_period}, 成交量倍数: {volume_multiplier}, 突破阈值: {breakout_threshold}%, 数量: {amount}")

        except ValidationError as e:
            error_details = error_handler.handle_error(e, context={
                "strategy": "VolumeBreakoutStrategy",
                "symbol": symbol,
                "lookback_period": lookback_period,
                "volume_multiplier": volume_multiplier,
                "breakout_threshold": breakout_threshold,
                "amount": amount
            })
            self.logger.error(f"成交量突破策略参数验证失败: {error_details['message']}")
            raise Exception(error_handler.format_user_message(error_details))
        except Exception as e:
            error_details = error_handler.handle_error(e, ErrorCode.STRATEGY_INIT_FAILED)
            self.logger.error(f"成交量突破策略初始化失败: {error_details['message']}")
            raise Exception(error_handler.format_user_message(error_details))

    def _validate_parameters(self, symbol, lookback_period, volume_multiplier, breakout_threshold, amount):
        """验证成交量突破参数"""
        validator.validate_symbol(symbol)
        validator.validate_integer(lookback_period, min_value=5, max_value=100)
        validator.validate_amount(volume_multiplier)
        validator.validate_percentage(breakout_threshold)
        validator.validate_amount(amount)

    def get_market_data(self, timeframe='1h', limit=100):
        """获取市场数据"""
        try:
            # 兼容Gate.io适配器和CCXT接口
            if hasattr(self.exchange, 'fetch_ohlcv'):
                ohlcv = self.exchange.fetch_ohlcv(self.symbol, timeframe, limit=limit)
            else:
                # 如果是Gate.io适配器
                ohlcv = self.exchange.fetch_ohlcv(self.symbol, timeframe, limit)

            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')

            # 计算技术指标
            df['volume_ma'] = df['volume'].rolling(window=self.lookback_period).mean()
            df['price_ma'] = df['close'].rolling(window=self.lookback_period).mean()
            df['resistance'] = df['high'].rolling(window=self.lookback_period).max()
            df['support'] = df['low'].rolling(window=self.lookback_period).min()

            return df
        except Exception as e:
            self.logger.error(f"获取市场数据失败: {e}")
            return None

    def detect_breakout(self, df):
        """检测突破信号"""
        df['breakout_signal'] = 0

        for i in range(self.lookback_period, len(df)):
            current_row = df.iloc[i]
            prev_row = df.iloc[i-1]

            # 向上突破条件
            upward_breakout = (
                current_row['close'] > prev_row['resistance'] * (1 + self.breakout_threshold) and
                current_row['volume'] > current_row['volume_ma'] * self.volume_multiplier and
                current_row['close'] > current_row['open']  # 阳线
            )

            # 向下突破条件
            downward_breakout = (
                current_row['close'] < prev_row['support'] * (1 - self.breakout_threshold) and
                current_row['volume'] > current_row['volume_ma'] * self.volume_multiplier and
                current_row['close'] < current_row['open']  # 阴线
            )

            if upward_breakout:
                df.iloc[i, df.columns.get_loc('breakout_signal')] = 1
            elif downward_breakout:
                df.iloc[i, df.columns.get_loc('breakout_signal')] = -1

        return df

    def calculate_position_size(self, current_price, account_risk=0.02):
        """根据风险控制计算仓位"""
        try:
            balance = self.exchange.fetch_balance()
            account_balance = balance['USDT']['free']
            # 根据止损比例计算仓位
            risk_amount = account_balance * account_risk
            position_value = risk_amount / self.stop_loss_pct
            position_size = position_value / current_price
            return max(position_size, 0.001)
        except Exception as e:
            self.logger.error(f"计算仓位失败:{e}")
            return 0.001
