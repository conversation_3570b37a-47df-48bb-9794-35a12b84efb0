"""
交易策略模块
包含所有从4.txt文件中复制的交易策略
"""
import ccxt
import time
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from logger import get_logger, log_trade, log_error, log_strategy_start, log_strategy_stop

class GridTrading:
    def __init__(self, exchange, symbol, base_price, grid_spacing, grid_count, order_amount):
        self.exchange = exchange
        self.symbol = symbol
        self.base_price = base_price  # 基准价格
        self.grid_spacing = grid_spacing  # 网格间距(百分比)
        self.grid_count = grid_count  # 网格数量
        self.order_amount = order_amount  # 每次交易数量
        self.orders = {}  # 存储订单信息
        self.running = False
        self.logger = get_logger("grid_trading")
    
    def create_grid_orders(self):
        """创建网格订单"""
        current_price = self.get_current_price()
        # 创建上下网格价格
        for i in range(-self.grid_count//2, self.grid_count//2 + 1):
            if i == 0:
                continue
            grid_price = current_price * (1 + i * self.grid_spacing / 100)
            if i < 0:  # 下方网格，放买单
                try:
                    order = self.exchange.create_limit_buy_order(
                        self.symbol, self.order_amount, grid_price
                    )
                    self.orders[grid_price] = {'type': 'buy', 'order': order}
                    self.logger.info(f"买单已放置:{grid_price:.2f}")
                except Exception as e:
                    self.logger.error(f"买单失败:{e}")
            else:  # 上方网格，放卖单
                try:
                    order = self.exchange.create_limit_sell_order(
                        self.symbol, self.order_amount, grid_price
                    )
                    self.orders[grid_price] = {'type': 'sell', 'order': order}
                    self.logger.info(f"卖单已放置:{grid_price:.2f}")
                except Exception as e:
                    self.logger.error(f"卖单失败: {e}")
    
    def get_current_price(self):
        """获取当前价格"""
        ticker = self.exchange.fetch_ticker(self.symbol)
        return ticker['last']
    
    def check_and_rebalance(self):
        """检查订单状态并重新平衡"""
        for price, order_info in list(self.orders.items()):
            try:
                order_status = self.exchange.fetch_order(
                    order_info['order']['id'], self.symbol
                )
                
                if order_status['status'] == 'closed':
                    self.logger.info(f"订单已成交:{price:.2f},类型:{order_info['type']}")
                    # 在相反位置放置反向订单
                    if order_info['type'] == 'buy':
                        # 买单成交了，在更高价格放卖单
                        new_price = price * (1 + self.grid_spacing / 100)
                        new_order = self.exchange.create_limit_sell_order(
                            self.symbol, self.order_amount, new_price
                        )
                        self.orders[new_price] = {'type': 'sell', 'order': new_order}
                    else:
                        # 卖单成交了，在更低价格放买单
                        new_price = price * (1 - self.grid_spacing / 100)
                        new_order = self.exchange.create_limit_buy_order(
                            self.symbol, self.order_amount, new_price
                        )
                        self.orders[new_price] = {'type': 'buy', 'order': new_order}
                    
                    # 删除已成交的订单
                    del self.orders[price]
            except Exception as e:
                self.logger.error(f"检查订单状态失败:{e}")
    
    def run(self):
        """运行网格策略"""
        self.logger.info("开始网格交易...")
        self.running = True
        self.create_grid_orders()
        
        while self.running:
            try:
                self.check_and_rebalance()
                time.sleep(60)  # 每分钟检查一次
            except KeyboardInterrupt:
                self.logger.info("网格交易停止")
                break
            except Exception as e:
                self.logger.error(f"运行错误:{e}")
                time.sleep(10)
    
    def stop(self):
        """停止策略"""
        self.running = False
        # 取消所有挂单
        try:
            for price, order_info in self.orders.items():
                self.exchange.cancel_order(order_info['order']['id'], self.symbol)
            self.orders.clear()
            self.logger.info("网格交易已停止，所有订单已取消")
        except Exception as e:
            self.logger.error(f"停止策略时出错:{e}")


class MovingAverageStrategy:
    def __init__(self, exchange, symbol, short_window=10, long_window=30):
        self.exchange = exchange
        self.symbol = symbol
        self.short_window = short_window
        self.long_window = long_window
        self.position = 0  # 1表示持有多头，-1表示持有空头，0表示无仓
        self.last_action_time = 0
        self.running = False
        self.logger = get_logger("moving_average")
    
    def get_historical_data(self, timeframe='1h', limit=100):
        """获取历史价格数据"""
        try:
            ohlcv = self.exchange.fetch_ohlcv(
                self.symbol, timeframe, limit=limit
            )
            df = pd.DataFrame(
                ohlcv,
                columns=['timestamp', 'open', 'high', 'low', 'close', 'volume']
            )
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            return df
        except Exception as e:
            self.logger.error(f"获取历史数据失败:{e}")
            return None
    
    def calculate_moving_averages(self, df):
        """计算移动平均线"""
        df['ma_short'] = df['close'].rolling(window=self.short_window).mean()
        df['ma_long'] = df['close'].rolling(window=self.long_window).mean()
        return df
    
    def generate_signals(self, df):
        """生成交易信号"""
        df['signal'] = 0
        df['position'] = 0
        
        # 金叉信号(短期均线上穿长期均线)
        golden_cross = (df['ma_short'] > df['ma_long']) & \
                      (df['ma_short'].shift(1) <= df['ma_long'].shift(1))
        
        # 死叉信号(短期均线下穿长期均线)
        death_cross = (df['ma_short'] < df['ma_long']) & \
                     (df['ma_short'].shift(1) >= df['ma_long'].shift(1))
        
        df.loc[golden_cross, 'signal'] = 1  # 买入信号
        df.loc[death_cross, 'signal'] = -1  # 卖出信号

        return df

    def execute_trade(self, signal, current_price):
        """执行交易"""
        current_time = time.time()
        # 防止频繁交易，至少间隔1小时
        if current_time - self.last_action_time < 3600:
            return

        try:
            balance = self.exchange.fetch_balance()
            if signal == 1 and self.position <= 0:  # 买入信号
                # 用70%的USDT买入
                usdt_balance = balance['USDT']['free']
                buy_amount = (usdt_balance * 0.7) / current_price
                if buy_amount > 0.001:  # 最小交易数量
                    order = self.exchange.create_market_buy_order(
                        self.symbol, buy_amount
                    )
                    self.position = 1
                    self.last_action_time = current_time
                    log_trade("moving_average", "买入", self.symbol, buy_amount, current_price, order.get('id'))
                    self.logger.info(f"买入执行:{buy_amount:.6f} @ {current_price:.2f}")

            elif signal == -1 and self.position >= 0:  # 卖出信号
                # 卖出所有持仓
                base_currency = self.symbol.split('/')[0]
                sell_amount = balance[base_currency]['free']
                if sell_amount > 0.001:
                    order = self.exchange.create_market_sell_order(
                        self.symbol, sell_amount
                    )
                    self.position = -1
                    self.last_action_time = current_time
                    log_trade("moving_average", "卖出", self.symbol, sell_amount, current_price, order.get('id'))
                    self.logger.info(f"卖出执行:{sell_amount:.6f} @ {current_price:.2f}")
        except Exception as e:
            log_error("moving_average", "交易执行失败", e)
            self.logger.error(f"交易执行失败:{e}")

    def run_backtest(self, days=30):
        """回测策略"""
        df = self.get_historical_data(timeframe='1h', limit=days*24)
        if df is None:
            return

        df = self.calculate_moving_averages(df)
        df = self.generate_signals(df)

        # 计算策略收益
        df['returns'] = df['close'].pct_change()
        df['strategy_returns'] = df['signal'].shift(1) * df['returns']

        # 计算累计收益
        df['cumulative_returns'] = (1 + df['returns']).cumprod()
        df['cumulative_strategy_returns'] = (1 + df['strategy_returns']).cumprod()

        # 打印回测结果
        total_return = df['cumulative_strategy_returns'].iloc[-1] - 1
        sharpe_ratio = df['strategy_returns'].mean() / df['strategy_returns'].std() * np.sqrt(24*365)

        self.logger.info(f"回测期间总收益:{total_return:.2%}")
        self.logger.info(f"夏普比率:{sharpe_ratio:.2f}")

        return df

    def run_live(self):
        """实时交易"""
        self.logger.info("开始移动平均线实时交易...")
        self.running = True

        while self.running:
            try:
                df = self.get_historical_data(timeframe='1h', limit=50)
                if df is None:
                    time.sleep(300)
                    continue

                df = self.calculate_moving_averages(df)
                df = self.generate_signals(df)

                # 获取最新信号
                latest_signal = df['signal'].iloc[-1]
                current_price = df['close'].iloc[-1]

                if latest_signal != 0:
                    self.execute_trade(latest_signal, current_price)

                # 每5分钟检查一次
                time.sleep(300)
            except KeyboardInterrupt:
                self.logger.info("策略停止")
                break
            except Exception as e:
                log_error("moving_average", "运行错误", e)
                self.logger.error(f"运行错误: {e}")
                time.sleep(60)

    def stop(self):
        """停止策略"""
        self.running = False
        log_strategy_stop("moving_average")


class RSIStrategy:
    def __init__(self, exchange, symbol, rsi_period=14, oversold_threshold=30, overbought_threshold=70):
        self.exchange = exchange
        self.symbol = symbol
        self.rsi_period = rsi_period
        self.oversold_threshold = oversold_threshold
        self.overbought_threshold = overbought_threshold
        self.position = 0
        self.last_action_time = 0
        self.running = False
        self.logger = get_logger("rsi_strategy")

    def calculate_rsi(self, prices, period=14):
        """计算RSI指标"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi

    def get_market_data(self, timeframe='1h', limit=100):
        """获取市场数据"""
        try:
            # 兼容Gate.io适配器和CCXT接口
            if hasattr(self.exchange, 'fetch_ohlcv'):
                ohlcv = self.exchange.fetch_ohlcv(self.symbol, timeframe, limit=limit)
            else:
                # 如果是Gate.io适配器
                ohlcv = self.exchange.fetch_ohlcv(self.symbol, timeframe, limit)

            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            # 计算RSI
            df['rsi'] = self.calculate_rsi(df['close'], self.rsi_period)
            return df
        except Exception as e:
            self.logger.error(f"获取市场数据失败:{e}")
            return None

    def generate_signals(self, df):
        """生成交易信号"""
        df['signal'] = 0
        # RSI超卖信号(RSI < 30)
        oversold_condition = df['rsi'] < self.oversold_threshold
        df.loc[oversold_condition, 'signal'] = 1
        # RSI超买信号(RSI > 70)
        overbought_condition = df['rsi'] > self.overbought_threshold
        df.loc[overbought_condition, 'signal'] = -1
        return df

    def calculate_position_size(self, current_price, risk_per_trade=0.02):
        """计算仓位大小"""
        try:
            balance = self.exchange.fetch_balance()
            account_balance = balance['USDT']['free']
            # 风险控制:每次交易不超过账户余额2%
            position_value = account_balance * risk_per_trade
            position_size = position_value / current_price
            return max(position_size, 0.001)  # 最小数量
        except Exception as e:
            self.logger.error(f"计算仓位大小失败: {e}")
            return 0.001

    def run_live(self):
        """实时运行RSI策略"""
        self.logger.info("开始RSI反转策略...")
        self.running = True

        while self.running:
            try:
                df = self.get_market_data(timeframe='1h', limit=50)
                if df is None:
                    time.sleep(300)
                    continue

                df = self.generate_signals(df)
                # 获取最新数据
                latest_signal = df['signal'].iloc[-1]
                current_price = df['close'].iloc[-1]
                current_rsi = df['rsi'].iloc[-1]

                self.logger.info(f"当前价格:{current_price:.2f},RSI:{current_rsi:.2f}")

                if latest_signal != 0:
                    self.execute_trade(latest_signal, current_price, current_rsi)

                time.sleep(600)  # 10分钟检查一次
            except KeyboardInterrupt:
                self.logger.info("RSI策略停止")
                break
            except Exception as e:
                log_error("rsi_strategy", "运行错误", e)
                self.logger.error(f"运行错误: {e}")
                time.sleep(60)

    def stop(self):
        """停止策略"""
        self.running = False
        log_strategy_stop("rsi_strategy")

    def execute_trade(self, signal, current_price, current_rsi):
        """执行交易"""
        current_time = time.time()
        # 防止频繁交易
        if current_time - self.last_action_time < 1800:  # 30分钟间隔
            return

        try:
            if signal == 1 and self.position <= 0:  # 买入信号
                position_size = self.calculate_position_size(current_price)
                order = self.exchange.create_market_buy_order(self.symbol, position_size)
                self.position = 1
                self.last_action_time = current_time
                log_trade("rsi_strategy", "买入", self.symbol, position_size, current_price, order.get('id'))
                self.logger.info(f"RSI超卖买入:{position_size:.6f} @ {current_price:.2f},RSI:{current_rsi:.2f}")

            elif signal == -1 and self.position >= 0:  # 卖出信号
                # 卖出所有持仓
                balance = self.exchange.fetch_balance()
                base_currency = self.symbol.split('/')[0]
                sell_amount = balance[base_currency]['free']
                if sell_amount > 0.001:
                    order = self.exchange.create_market_sell_order(self.symbol, sell_amount)
                    self.position = -1
                    self.last_action_time = current_time
                    log_trade("rsi_strategy", "卖出", self.symbol, sell_amount, current_price, order.get('id'))
                    self.logger.info(f"RSI超买卖出:{sell_amount:.6f} @ {current_price:.2f},RSI:{current_rsi:.2f}")
        except Exception as e:
            log_error("rsi_strategy", "交易执行失败", e)
            self.logger.error(f"交易执行失败:{e}")

    def run_backtest(self, days=60):
        """回测RSI策略"""
        df = self.get_market_data(timeframe='4h', limit=days*6)
        if df is None:
            return

        df = self.generate_signals(df)
        # 模拟交易
        capital = 10000  # 初始资金
        position = 0
        trades = []

        for i in range(len(df)):
            if pd.isna(df['rsi'].iloc[i]):
                continue

            current_price = df['close'].iloc[i]
            current_signal = df['signal'].iloc[i]
            current_rsi = df['rsi'].iloc[i]

            if current_signal == 1 and position <= 0:  # 买入
                position = capital / current_price
                capital = 0
                trades.append({
                    'type': 'buy',
                    'price': current_price,
                    'rsi': current_rsi,
                    'time': df['timestamp'].iloc[i]
                })
            elif current_signal == -1 and position > 0:  # 卖出
                capital = position * current_price
                position = 0
                trades.append({
                    'type': 'sell',
                    'price': current_price,
                    'rsi': current_rsi,
                    'time': df['timestamp'].iloc[i]
                })

        # 计算最终收益
        final_value = capital + position * df['close'].iloc[-1]
        total_return = (final_value - 10000) / 10000

        self.logger.info(f"RSI策略回测结果:")
        self.logger.info(f"初始资金:$10,000")
        self.logger.info(f"最终价值:${final_value:.2f}")
        self.logger.info(f"总收益率:{total_return:.2%}")
        self.logger.info(f"交易次数:{len(trades)}")

        return trades

    def run_live(self):
        """实时运行RSI策略"""
        self.logger.info("开始RSI反转策略...")
        self.running = True

        while self.running:
            try:
                df = self.get_market_data(timeframe='1h', limit=50)
                if df is None:
                    time.sleep(300)
                    continue

                df = self.generate_signals(df)
                # 获取最新数据
                latest_signal = df['signal'].iloc[-1]
                current_price = df['close'].iloc[-1]
                current_rsi = df['rsi'].iloc[-1]

                self.logger.info(f"当前价格:{current_price:.2f},RSI:{current_rsi:.2f}")

                if latest_signal != 0:
                    self.execute_trade(latest_signal, current_price, current_rsi)

                time.sleep(600)  # 10分钟检查一次
            except KeyboardInterrupt:
                self.logger.info("RSI策略停止")
                break
            except Exception as e:
                log_error("rsi_strategy", "运行错误", e)
                self.logger.error(f"运行错误: {e}")
                time.sleep(60)

    def stop(self):
        """停止策略"""
        self.running = False
        log_strategy_stop("rsi_strategy")


class VolumeBreakoutStrategy:
    def __init__(self, exchange, symbol, lookback_period=20, volume_multiplier=2, breakout_threshold=0.02):
        self.exchange = exchange
        self.symbol = symbol
        self.lookback_period = lookback_period
        self.volume_multiplier = volume_multiplier
        self.breakout_threshold = breakout_threshold
        self.position = 0
        self.entry_price = 0
        self.stop_loss_pct = 0.03  # 3%止损
        self.take_profit_pct = 0.06  # 6%止盈
        self.running = False
        self.logger = get_logger("volume_breakout")

    def get_market_data(self, timeframe='1h', limit=100):
        """获取市场数据"""
        try:
            # 兼容Gate.io适配器和CCXT接口
            if hasattr(self.exchange, 'fetch_ohlcv'):
                ohlcv = self.exchange.fetch_ohlcv(self.symbol, timeframe, limit=limit)
            else:
                # 如果是Gate.io适配器
                ohlcv = self.exchange.fetch_ohlcv(self.symbol, timeframe, limit)

            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')

            # 计算技术指标
            df['volume_ma'] = df['volume'].rolling(window=self.lookback_period).mean()
            df['price_ma'] = df['close'].rolling(window=self.lookback_period).mean()
            df['resistance'] = df['high'].rolling(window=self.lookback_period).max()
            df['support'] = df['low'].rolling(window=self.lookback_period).min()

            return df
        except Exception as e:
            self.logger.error(f"获取市场数据失败: {e}")
            return None

    def detect_breakout(self, df):
        """检测突破信号"""
        df['breakout_signal'] = 0

        for i in range(self.lookback_period, len(df)):
            current_row = df.iloc[i]
            prev_row = df.iloc[i-1]

            # 向上突破条件
            upward_breakout = (
                current_row['close'] > prev_row['resistance'] * (1 + self.breakout_threshold) and
                current_row['volume'] > current_row['volume_ma'] * self.volume_multiplier and
                current_row['close'] > current_row['open']  # 阳线
            )

            # 向下突破条件
            downward_breakout = (
                current_row['close'] < prev_row['support'] * (1 - self.breakout_threshold) and
                current_row['volume'] > current_row['volume_ma'] * self.volume_multiplier and
                current_row['close'] < current_row['open']  # 阴线
            )

            if upward_breakout:
                df.iloc[i, df.columns.get_loc('breakout_signal')] = 1
            elif downward_breakout:
                df.iloc[i, df.columns.get_loc('breakout_signal')] = -1

        return df

    def calculate_position_size(self, current_price, account_risk=0.02):
        """根据风险控制计算仓位"""
        try:
            balance = self.exchange.fetch_balance()
            account_balance = balance['USDT']['free']
            # 根据止损比例计算仓位
            risk_amount = account_balance * account_risk
            position_value = risk_amount / self.stop_loss_pct
            position_size = position_value / current_price
            return max(position_size, 0.001)
        except Exception as e:
            self.logger.error(f"计算仓位失败:{e}")
            return 0.001
