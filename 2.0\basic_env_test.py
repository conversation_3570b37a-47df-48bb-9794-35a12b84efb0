#!/usr/bin/env python3
"""
基础环境切换功能测试
"""

print("开始基础环境切换功能测试...")

try:
    # 测试环境管理器导入
    print("1. 测试环境管理器导入...")
    from environment_manager import environment_manager
    print("   ✓ 环境管理器导入成功")
    
    # 测试基本功能
    print("2. 测试基本功能...")
    current_mode = environment_manager.get_current_mode()
    print(f"   当前模式: {current_mode}")
    
    is_simulation = environment_manager.is_simulation_mode()
    print(f"   是否模拟模式: {is_simulation}")
    
    use_sandbox = environment_manager.should_use_sandbox()
    print(f"   应该使用沙盒: {use_sandbox}")
    
    use_real_api = environment_manager.should_use_real_api()
    print(f"   应该使用真实API: {use_real_api}")
    
    # 测试API配置生成
    print("3. 测试API配置生成...")
    config = environment_manager.get_api_config('okx', 'test_key', 'test_secret', 'test_pass')
    print(f"   API配置: {config}")
    
    # 测试状态信息
    print("4. 测试状态信息...")
    status = environment_manager.get_status_info()
    print(f"   状态信息: {status}")
    
    print("\n✓ 环境管理器基本功能测试通过")
    
except Exception as e:
    print(f"✗ 测试失败: {e}")
    import traceback
    traceback.print_exc()

try:
    # 测试策略适配器导入
    print("\n5. 测试策略适配器导入...")
    from strategy_environment_adapter import StrategyEnvironmentAdapter
    print("   ✓ 策略适配器导入成功")
    
    # 创建模拟交易所
    class MockExchange:
        def fetch_ticker(self, symbol):
            return {'last': 0.213456, 'symbol': symbol}
    
    mock_exchange = MockExchange()
    
    # 创建适配器
    adapter = StrategyEnvironmentAdapter('test', mock_exchange, 'CFX/USDT')
    print("   ✓ 策略适配器创建成功")
    
    # 测试API调用
    ticker = adapter.fetch_ticker('CFX/USDT')
    print(f"   价格: {ticker['last']:.6f}")
    
    print("✓ 策略适配器基本功能测试通过")
    
except Exception as e:
    print(f"✗ 策略适配器测试失败: {e}")
    import traceback
    traceback.print_exc()

try:
    # 测试GUI组件导入
    print("\n6. 测试GUI组件导入...")
    from environment_control_panel import EnvironmentControlPanel, EnvironmentStatusBar
    print("   ✓ GUI组件导入成功")
    
    print("✓ GUI组件导入测试通过")
    
except Exception as e:
    print(f"✗ GUI组件测试失败: {e}")
    import traceback
    traceback.print_exc()

print("\n" + "="*50)
print("基础测试完成")
print("="*50)

# 创建功能总结
summary = """
环境切换功能实现总结

✅ 已实现的功能:
1. 环境管理器 (environment_manager.py)
   - 三种环境模式: 模拟/测试网/实盘
   - 模式切换逻辑
   - API配置生成
   - 状态信息管理

2. 环境控制面板 (environment_control_panel.py)
   - 实盘模式复选框
   - 测试环境复选框
   - 快速切换按钮
   - 状态显示

3. 策略环境适配器 (strategy_environment_adapter.py)
   - 自动API切换
   - 模拟数据生成
   - 环境感知能力

4. 交易所管理器集成
   - 环境切换支持
   - 自动重连机制

5. 主界面集成
   - 环境控制面板
   - 状态栏显示
   - 模式变化处理

🔒 安全措施:
- 实盘模式风险警告
- 双重确认机制
- 明显的模式标识
- 详细的日志记录

📋 使用方法:
1. 启动程序默认为模拟模式
2. 勾选"实盘模式"启用实盘交易
3. 勾选"使用测试环境"使用测试网
4. 观察状态栏的模式指示器

⚠️ 重要提示:
- 实盘模式将使用真实资金
- 请充分测试策略后再启用实盘模式
- 建议从小资金开始测试
"""

print(summary)

print("\n🎉 环境切换功能实现完成！")
print("现在可以启动主程序测试完整功能。")
