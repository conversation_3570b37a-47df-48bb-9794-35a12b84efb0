#!/usr/bin/env python3
"""
量化交易系统文件复制工具
将核心系统文件复制到目标目录 C:/Users/<USER>/Desktop/yl/2.0
"""

import os
import shutil
import sys
from datetime import datetime
from pathlib import Path

class SystemFileCopier:
    """系统文件复制器"""
    
    def __init__(self):
        self.source_dir = Path(".")
        self.target_dir = Path("C:/Users/<USER>/Desktop/yl/2.0")
        
        # 定义需要复制的量化交易系统文件
        self.system_files = {
            # 核心策略文件
            "strategies.py": "策略核心文件",
            
            # 核心管理模块
            "exchange_manager.py": "交易所管理器",
            "environment_manager.py": "环境管理器",
            
            # 验证和错误处理模块
            "parameter_validator.py": "参数验证器",
            "error_handler.py": "错误处理中心",
            "user_friendly_messages.py": "用户友好消息",
            
            # 测试框架
            "test_framework.py": "测试框架核心",
            
            # 配置文件
            "logger.py": "日志系统",
            "risk_manager.py": "风险管理器",
            "config.py": "系统配置",
            
            # 适配器文件
            "gate_adapter.py": "Gate交易所适配器",
            "strategy_environment_adapter.py": "策略环境适配器",
            
            # GUI相关文件
            "main_gui.py": "主界面",
            "main.py": "主程序入口",
            "strategy_tabs.py": "策略标签页",
            "system_log_tab.py": "系统日志标签页",
            "environment_control_panel.py": "环境控制面板",
            
            # 扩展策略文件
            "strategies_extended.py": "扩展策略",
            "resilient_strategy_base.py": "弹性策略基类",
            
            # 其他系统文件
            "unified_trading_system.py": "统一交易系统",
            "three_exchanges_config.py": "三交易所配置",
            "three_exchanges_example.py": "三交易所示例",
            
            # 配置和依赖文件
            "requirements.txt": "依赖包列表",
        }
        
        # 测试文件模式
        self.test_file_patterns = [
            "test_*.py",
            "*_test.py"
        ]
        
        # 文档文件
        self.doc_files = [
            "*.md",
            "*.txt"
        ]
        
        # 不需要复制的文件和目录
        self.exclude_patterns = [
            "__pycache__",
            "*.pyc",
            "*.pyo",
            "build",
            "dist",
            "*.egg-info",
            ".git",
            ".vscode",
            "logs",
            "data",
            "这些量化策略再蠢也能赚点零花钱*",
            "*.zip",
            "*.exe",
            "*.spec",
            "*.bat",
            "*.html",
            "*.rtf",
            "*.docx",
            "4.txt",
            "44.rtf",
            "444.docx",
            "rz.txt"
        ]
    
    def should_exclude(self, file_path: Path) -> bool:
        """判断文件是否应该被排除"""
        file_str = str(file_path)
        name = file_path.name
        
        for pattern in self.exclude_patterns:
            if pattern.startswith("*") and pattern.endswith("*"):
                # 包含模式
                if pattern[1:-1] in name:
                    return True
            elif pattern.startswith("*"):
                # 后缀模式
                if name.endswith(pattern[1:]):
                    return True
            elif pattern.endswith("*"):
                # 前缀模式
                if name.startswith(pattern[:-1]):
                    return True
            else:
                # 精确匹配
                if name == pattern or file_path.name == pattern:
                    return True
        
        return False
    
    def analyze_source_files(self):
        """分析源目录中的文件"""
        print("🔍 分析源目录文件...")
        print(f"源目录: {self.source_dir.absolute()}")
        
        all_files = []
        system_files_found = []
        test_files_found = []
        doc_files_found = []
        other_files = []
        
        # 遍历源目录
        for file_path in self.source_dir.iterdir():
            if file_path.is_file():
                all_files.append(file_path)
                
                # 检查是否应该排除
                if self.should_exclude(file_path):
                    continue
                
                # 分类文件
                if file_path.name in self.system_files:
                    system_files_found.append(file_path)
                elif file_path.name.startswith("test_") or file_path.name.endswith("_test.py"):
                    test_files_found.append(file_path)
                elif file_path.suffix in [".md", ".txt"]:
                    doc_files_found.append(file_path)
                elif file_path.suffix == ".py":
                    other_files.append(file_path)
        
        print(f"\n📊 文件分析结果:")
        print(f"总文件数: {len(all_files)}")
        print(f"系统核心文件: {len(system_files_found)}")
        print(f"测试文件: {len(test_files_found)}")
        print(f"文档文件: {len(doc_files_found)}")
        print(f"其他Python文件: {len(other_files)}")
        
        return {
            "system_files": system_files_found,
            "test_files": test_files_found,
            "doc_files": doc_files_found,
            "other_files": other_files
        }
    
    def create_target_directory(self):
        """创建目标目录"""
        print(f"\n📁 创建目标目录: {self.target_dir}")
        
        try:
            self.target_dir.mkdir(parents=True, exist_ok=True)
            print("✅ 目标目录创建成功")
            return True
        except Exception as e:
            print(f"❌ 目标目录创建失败: {e}")
            return False
    
    def copy_files(self, files_dict):
        """复制文件到目标目录"""
        print(f"\n📋 开始复制文件...")
        
        copied_files = []
        failed_files = []
        
        # 复制系统核心文件
        print("\n🔧 复制系统核心文件:")
        for file_path in files_dict["system_files"]:
            try:
                target_path = self.target_dir / file_path.name
                shutil.copy2(file_path, target_path)
                copied_files.append(file_path.name)
                description = self.system_files.get(file_path.name, "系统文件")
                print(f"  ✅ {file_path.name} - {description}")
            except Exception as e:
                failed_files.append((file_path.name, str(e)))
                print(f"  ❌ {file_path.name} - 复制失败: {e}")
        
        # 复制测试文件
        print("\n🧪 复制测试文件:")
        for file_path in files_dict["test_files"]:
            try:
                target_path = self.target_dir / file_path.name
                shutil.copy2(file_path, target_path)
                copied_files.append(file_path.name)
                print(f"  ✅ {file_path.name}")
            except Exception as e:
                failed_files.append((file_path.name, str(e)))
                print(f"  ❌ {file_path.name} - 复制失败: {e}")
        
        # 复制文档文件
        print("\n📚 复制文档文件:")
        for file_path in files_dict["doc_files"]:
            try:
                target_path = self.target_dir / file_path.name
                shutil.copy2(file_path, target_path)
                copied_files.append(file_path.name)
                print(f"  ✅ {file_path.name}")
            except Exception as e:
                failed_files.append((file_path.name, str(e)))
                print(f"  ❌ {file_path.name} - 复制失败: {e}")
        
        # 复制其他重要的Python文件
        print("\n🐍 复制其他Python文件:")
        for file_path in files_dict["other_files"]:
            try:
                target_path = self.target_dir / file_path.name
                shutil.copy2(file_path, target_path)
                copied_files.append(file_path.name)
                print(f"  ✅ {file_path.name}")
            except Exception as e:
                failed_files.append((file_path.name, str(e)))
                print(f"  ❌ {file_path.name} - 复制失败: {e}")
        
        return copied_files, failed_files
    
    def verify_copy(self, copied_files):
        """验证复制结果"""
        print(f"\n🔍 验证复制结果...")
        
        verification_results = []
        
        for filename in copied_files:
            source_path = self.source_dir / filename
            target_path = self.target_dir / filename
            
            if not target_path.exists():
                verification_results.append((filename, "目标文件不存在"))
                continue
            
            # 检查文件大小
            source_size = source_path.stat().st_size
            target_size = target_path.stat().st_size
            
            if source_size != target_size:
                verification_results.append((filename, f"文件大小不匹配: 源{source_size} vs 目标{target_size}"))
                continue
            
            verification_results.append((filename, "验证通过"))
        
        # 显示验证结果
        passed = sum(1 for _, result in verification_results if result == "验证通过")
        total = len(verification_results)
        
        print(f"验证结果: {passed}/{total} 文件通过验证")
        
        # 显示失败的文件
        failed_verifications = [(f, r) for f, r in verification_results if r != "验证通过"]
        if failed_verifications:
            print("\n❌ 验证失败的文件:")
            for filename, reason in failed_verifications:
                print(f"  {filename}: {reason}")
        
        return len(failed_verifications) == 0
    
    def generate_copy_report(self, copied_files, failed_files):
        """生成复制报告"""
        report_content = f"""# 量化交易系统文件复制报告

## 复制概览
- **复制时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- **源目录**: {self.source_dir.absolute()}
- **目标目录**: {self.target_dir.absolute()}
- **成功复制**: {len(copied_files)} 个文件
- **复制失败**: {len(failed_files)} 个文件

## 成功复制的文件

### 系统核心文件
"""
        
        # 添加系统文件列表
        for filename in copied_files:
            if filename in self.system_files:
                description = self.system_files[filename]
                report_content += f"- `{filename}` - {description}\n"
        
        report_content += "\n### 测试文件\n"
        for filename in copied_files:
            if filename.startswith("test_") or filename.endswith("_test.py"):
                report_content += f"- `{filename}`\n"
        
        report_content += "\n### 文档文件\n"
        for filename in copied_files:
            if filename.endswith(".md") or filename.endswith(".txt"):
                report_content += f"- `{filename}`\n"
        
        if failed_files:
            report_content += "\n## 复制失败的文件\n"
            for filename, error in failed_files:
                report_content += f"- `{filename}`: {error}\n"
        
        report_content += f"\n## 总结\n复制操作{'成功' if not failed_files else '部分成功'}完成。"
        
        # 保存报告
        report_path = self.target_dir / f"copy_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        try:
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(report_content)
            print(f"\n📋 复制报告已生成: {report_path}")
        except Exception as e:
            print(f"\n❌ 报告生成失败: {e}")
    
    def run_copy_operation(self):
        """执行完整的复制操作"""
        print("🚀 量化交易系统文件复制工具")
        print("=" * 60)
        
        # 1. 分析源文件
        files_dict = self.analyze_source_files()
        
        # 2. 创建目标目录
        if not self.create_target_directory():
            return False
        
        # 3. 复制文件
        copied_files, failed_files = self.copy_files(files_dict)
        
        # 4. 验证复制结果
        verification_passed = self.verify_copy(copied_files)
        
        # 5. 生成报告
        self.generate_copy_report(copied_files, failed_files)
        
        # 6. 显示总结
        print("\n" + "=" * 60)
        print("📊 复制操作总结")
        print("=" * 60)
        print(f"成功复制: {len(copied_files)} 个文件")
        print(f"复制失败: {len(failed_files)} 个文件")
        print(f"验证结果: {'✅ 通过' if verification_passed else '❌ 部分失败'}")
        
        if len(copied_files) > 0:
            print(f"\n🎉 量化交易系统核心文件已成功复制到:")
            print(f"   {self.target_dir.absolute()}")
        
        return len(failed_files) == 0 and verification_passed

def main():
    """主函数"""
    copier = SystemFileCopier()
    success = copier.run_copy_operation()
    
    if success:
        print("\n✅ 所有文件复制成功！")
        return 0
    else:
        print("\n⚠️ 复制过程中出现问题，请检查报告。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
