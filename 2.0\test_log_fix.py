#!/usr/bin/env python3
"""
测试日志修复
验证日志系统是否正常工作，不再出现"main thread is not in main loop"错误
"""

import sys
import os
import time
import threading

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_logger_import():
    """测试日志模块导入"""
    print("🔍 测试日志模块导入...")
    try:
        from logger import get_logger, trading_logger
        print("✅ 日志模块导入成功")
        return True
    except Exception as e:
        print(f"❌ 日志模块导入失败: {e}")
        return False

def test_basic_logging():
    """测试基础日志功能"""
    print("\n🔍 测试基础日志功能...")
    try:
        from logger import get_logger
        
        logger = get_logger("test")
        logger.info("这是一条测试信息")
        logger.warning("这是一条测试警告")
        logger.error("这是一条测试错误")
        
        print("✅ 基础日志功能正常")
        return True
    except Exception as e:
        print(f"❌ 基础日志功能失败: {e}")
        return False

def test_gui_log_handler():
    """测试GUI日志处理器"""
    print("\n🔍 测试GUI日志处理器...")
    try:
        import queue
        from system_log_tab import QueueLogHandler
        
        # 创建测试队列
        test_queue = queue.Queue()
        
        # 创建处理器
        handler = QueueLogHandler(test_queue)
        
        # 创建测试记录
        import logging
        record = logging.LogRecord(
            name="test",
            level=logging.INFO,
            pathname="test.py",
            lineno=1,
            msg="测试消息",
            args=(),
            exc_info=None
        )
        
        # 发送记录
        handler.emit(record)
        
        # 检查队列
        if not test_queue.empty():
            received_record = test_queue.get()
            print(f"✅ GUI日志处理器正常，收到消息: {received_record.msg}")
            return True
        else:
            print("❌ GUI日志处理器未收到消息")
            return False
            
    except Exception as e:
        print(f"❌ GUI日志处理器测试失败: {e}")
        return False

def test_threaded_logging():
    """测试多线程日志记录"""
    print("\n🔍 测试多线程日志记录...")
    try:
        from logger import get_logger
        
        logger = get_logger("thread_test")
        
        def log_from_thread(thread_id):
            """从线程中记录日志"""
            try:
                for i in range(3):
                    logger.info(f"线程 {thread_id} 消息 {i}")
                    time.sleep(0.1)
                print(f"✅ 线程 {thread_id} 日志记录完成")
            except Exception as e:
                print(f"❌ 线程 {thread_id} 日志记录失败: {e}")
        
        # 创建多个线程
        threads = []
        for i in range(3):
            thread = threading.Thread(target=log_from_thread, args=(i,))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        print("✅ 多线程日志记录测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 多线程日志记录测试失败: {e}")
        return False

def test_gui_initialization():
    """测试GUI初始化（不显示窗口）"""
    print("\n🔍 测试GUI初始化...")
    try:
        import tkinter as tk
        
        # 创建隐藏的根窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        # 测试SystemLogTab初始化
        from system_log_tab import SystemLogTab
        
        # 创建一个简单的父容器
        parent = tk.Frame(root)
        
        # 模拟主应用对象
        class MockMainApp:
            def __init__(self):
                self.logger = None
        
        mock_app = MockMainApp()
        
        # 创建SystemLogTab
        log_tab = SystemLogTab(parent, mock_app)
        
        # 等待延迟初始化完成
        root.update()
        time.sleep(0.5)
        root.update()
        
        print("✅ GUI初始化测试完成，无错误")
        
        # 清理
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ GUI初始化测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始日志系统修复验证测试")
    print("=" * 50)
    
    tests = [
        ("日志模块导入", test_logger_import),
        ("基础日志功能", test_basic_logging),
        ("GUI日志处理器", test_gui_log_handler),
        ("多线程日志记录", test_threaded_logging),
        ("GUI初始化", test_gui_initialization),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"⚠️ 测试 '{test_name}' 未通过")
        except Exception as e:
            print(f"❌ 测试 '{test_name}' 出现异常: {e}")
    
    print("\n" + "=" * 50)
    print("📊 测试结果总结")
    print("=" * 50)
    print(f"总测试数: {total}")
    print(f"通过测试: {passed}")
    print(f"失败测试: {total - passed}")
    print(f"通过率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有测试通过！日志系统修复成功！")
        print("现在可以安全运行量化交易系统了。")
    else:
        print(f"\n⚠️ 有 {total - passed} 个测试失败，需要进一步检查。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
