#!/usr/bin/env python3
"""
安全性测试
测试实盘模式的安全机制、风险控制、数据保护
"""

import sys
import os
import time
import threading
from datetime import datetime
from unittest.mock import patch, MagicMock

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from test_framework import test_framework, TestResult
from environment_manager import environment_manager
from risk_manager import RiskManager
from strategies import GridTrading

class SecurityTester:
    """安全性测试器"""
    
    def __init__(self):
        self.framework = test_framework
        self.mock_exchange = self.framework.create_mock_exchange()
        self.risk_manager = RiskManager()
        
    def test_environment_security(self):
        """测试环境安全机制"""
        
        def test_live_mode_confirmation():
            """测试实盘模式确认机制"""
            # 测试默认状态
            self.framework.assert_true(environment_manager.is_simulation_mode(), 
                                     "默认应为模拟模式")
            self.framework.assert_false(environment_manager.risk_acknowledged, 
                                      "默认风险确认状态应为False")
            
            # 测试风险警告函数存在
            self.framework.assert_true(hasattr(environment_manager, '_show_live_mode_warning'), 
                                     "应该有实盘模式风险警告函数")
            self.framework.assert_true(hasattr(environment_manager, '_show_testnet_warning'), 
                                     "应该有测试网模式警告函数")
        
        def test_mode_switching_security():
            """测试模式切换安全性"""
            # 保存原始警告函数
            original_live_warning = environment_manager._show_live_mode_warning
            original_testnet_warning = environment_manager._show_testnet_warning
            
            try:
                # 测试拒绝实盘模式切换
                environment_manager._show_live_mode_warning = lambda: False
                success = environment_manager.switch_to_live()
                self.framework.assert_false(success, "拒绝确认应该阻止切换到实盘模式")
                self.framework.assert_true(environment_manager.is_simulation_mode(), 
                                         "拒绝后应保持模拟模式")
                
                # 测试接受实盘模式切换
                environment_manager._show_live_mode_warning = lambda: True
                success = environment_manager.switch_to_live()
                self.framework.assert_true(success, "接受确认应该允许切换到实盘模式")
                self.framework.assert_true(environment_manager.risk_acknowledged, 
                                         "风险确认状态应为True")
                
            finally:
                # 恢复原始函数并切换回模拟模式
                environment_manager._show_live_mode_warning = original_live_warning
                environment_manager._show_testnet_warning = original_testnet_warning
                environment_manager.switch_to_simulation()
        
        def test_api_configuration_security():
            """测试API配置安全性"""
            # 测试模拟模式配置
            environment_manager.switch_to_simulation()
            config = environment_manager.get_api_config('okx', 'test_key', 'test_secret', 'test_pass')
            
            self.framework.assert_true(config['sandbox'], "模拟模式应强制使用沙盒")
            self.framework.assert_true(config['simulation'], "模拟模式应标记为模拟")
            
            # 测试实盘模式配置
            original_warning = environment_manager._show_live_mode_warning
            environment_manager._show_live_mode_warning = lambda: True
            
            try:
                environment_manager.switch_to_live()
                config = environment_manager.get_api_config('okx', 'test_key', 'test_secret', 'test_pass')
                
                self.framework.assert_false(config['sandbox'], "实盘模式不应使用沙盒")
                self.framework.assert_false(config['simulation'], "实盘模式不应标记为模拟")
                
            finally:
                environment_manager._show_live_mode_warning = original_warning
                environment_manager.switch_to_simulation()
        
        # 运行环境安全测试
        self.framework.run_test_case(
            test_live_mode_confirmation,
            "实盘模式确认机制测试",
            "验证实盘模式的确认和警告机制",
            "安全性测试",
            "HIGH"
        )
        
        self.framework.run_test_case(
            test_mode_switching_security,
            "模式切换安全性测试",
            "验证模式切换的安全控制机制",
            "安全性测试",
            "HIGH"
        )
        
        self.framework.run_test_case(
            test_api_configuration_security,
            "API配置安全性测试",
            "验证不同模式下API配置的安全性",
            "安全性测试",
            "HIGH"
        )
    
    def test_risk_management_security(self):
        """测试风险管理安全机制"""
        
        def test_balance_validation():
            """测试余额验证安全"""
            # 测试余额检查
            balance = {'USDT': {'free': 100.0, 'used': 0.0, 'total': 100.0}}
            
            # 测试正常余额
            sufficient = self.risk_manager.check_balance_sufficient(balance, 'USDT', 50.0)
            self.framework.assert_true(sufficient, "充足余额应该通过检查")
            
            # 测试余额不足
            insufficient = self.risk_manager.check_balance_sufficient(balance, 'USDT', 150.0)
            self.framework.assert_false(insufficient, "余额不足应该被检测")
            
            # 测试负余额
            negative_balance = {'USDT': {'free': -10.0, 'used': 0.0, 'total': -10.0}}
            negative_check = self.risk_manager.check_balance_sufficient(negative_balance, 'USDT', 1.0)
            self.framework.assert_false(negative_check, "负余额应该被拒绝")
        
        def test_position_size_limits():
            """测试仓位大小限制"""
            # 测试仓位大小验证
            total_balance = 1000.0
            
            # 测试正常仓位
            normal_position = 100.0
            is_valid = self.risk_manager.validate_position_size(normal_position, total_balance)
            self.framework.assert_true(is_valid, "正常仓位应该通过验证")
            
            # 测试过大仓位
            large_position = 900.0
            is_valid = self.risk_manager.validate_position_size(large_position, total_balance)
            self.framework.assert_false(is_valid, "过大仓位应该被拒绝")
            
            # 测试负仓位
            negative_position = -100.0
            is_valid = self.risk_manager.validate_position_size(negative_position, total_balance)
            self.framework.assert_false(is_valid, "负仓位应该被拒绝")
        
        def test_price_validation():
            """测试价格验证安全"""
            current_price = 0.213456
            
            # 测试正常价格范围
            normal_price = 0.21
            is_valid = self.risk_manager.validate_price_range(normal_price, current_price)
            self.framework.assert_true(is_valid, "正常价格应该通过验证")
            
            # 测试异常价格
            abnormal_price = 0.10  # 偏离过大
            is_valid = self.risk_manager.validate_price_range(abnormal_price, current_price)
            self.framework.assert_false(is_valid, "异常价格应该被拒绝")
            
            # 测试负价格
            negative_price = -0.1
            is_valid = self.risk_manager.validate_price_range(negative_price, current_price)
            self.framework.assert_false(is_valid, "负价格应该被拒绝")
        
        def test_stop_loss_mechanism():
            """测试止损机制"""
            entry_price = 0.213456
            current_price = 0.19  # 下跌约11%
            stop_loss_percentage = 0.1  # 10%止损
            
            should_stop = self.risk_manager.check_stop_loss(entry_price, current_price, stop_loss_percentage)
            self.framework.assert_true(should_stop, "应该触发止损")
            
            # 测试未触发止损
            current_price = 0.21  # 下跌约1.6%
            should_stop = self.risk_manager.check_stop_loss(entry_price, current_price, stop_loss_percentage)
            self.framework.assert_false(should_stop, "不应该触发止损")
        
        # 运行风险管理安全测试
        self.framework.run_test_case(
            test_balance_validation,
            "余额验证安全测试",
            "验证余额检查的安全机制",
            "安全性测试",
            "HIGH"
        )
        
        self.framework.run_test_case(
            test_position_size_limits,
            "仓位大小限制测试",
            "验证仓位大小的安全限制",
            "安全性测试",
            "HIGH"
        )
        
        self.framework.run_test_case(
            test_price_validation,
            "价格验证安全测试",
            "验证价格范围的安全检查",
            "安全性测试",
            "HIGH"
        )
        
        self.framework.run_test_case(
            test_stop_loss_mechanism,
            "止损机制测试",
            "验证止损机制的正确性",
            "安全性测试",
            "HIGH"
        )
    
    def test_data_protection(self):
        """测试数据保护机制"""
        
        def test_sensitive_data_handling():
            """测试敏感数据处理"""
            # 测试API密钥不应在日志中明文显示
            api_key = "test_api_key_12345"
            secret = "test_secret_67890"
            
            # 模拟日志记录
            log_message = f"API Key: {api_key[:4]}***"
            
            self.framework.assert_true("***" in log_message, "API密钥应该被部分隐藏")
            self.framework.assert_false(api_key in log_message, "完整API密钥不应出现在日志中")
        
        def test_configuration_validation():
            """测试配置验证"""
            # 测试无效配置检测
            invalid_configs = [
                {'apiKey': '', 'secret': 'test'},  # 空API密钥
                {'apiKey': 'test', 'secret': ''},  # 空密钥
                {'apiKey': None, 'secret': 'test'},  # None值
            ]
            
            for config in invalid_configs:
                is_valid = self.risk_manager.validate_api_config(config)
                self.framework.assert_false(is_valid, f"无效配置应该被拒绝: {config}")
        
        def test_input_sanitization():
            """测试输入清理"""
            # 测试交易对名称清理
            valid_symbols = ['CFX/USDT', 'BTC/USDT', 'ETH/USDT']
            invalid_symbols = ['CFX/USDT; DROP TABLE', '<script>alert(1)</script>', '../../etc/passwd']
            
            for symbol in valid_symbols:
                is_valid = self.risk_manager.validate_symbol(symbol)
                self.framework.assert_true(is_valid, f"有效交易对应该通过: {symbol}")
            
            for symbol in invalid_symbols:
                is_valid = self.risk_manager.validate_symbol(symbol)
                self.framework.assert_false(is_valid, f"无效交易对应该被拒绝: {symbol}")
        
        # 运行数据保护测试
        self.framework.run_test_case(
            test_sensitive_data_handling,
            "敏感数据处理测试",
            "验证敏感数据的安全处理",
            "安全性测试",
            "HIGH"
        )
        
        self.framework.run_test_case(
            test_configuration_validation,
            "配置验证测试",
            "验证配置参数的安全验证",
            "安全性测试",
            "MEDIUM"
        )
        
        self.framework.run_test_case(
            test_input_sanitization,
            "输入清理测试",
            "验证用户输入的安全清理",
            "安全性测试",
            "MEDIUM"
        )
    
    def test_strategy_security(self):
        """测试策略安全机制"""
        
        def test_strategy_parameter_validation():
            """测试策略参数验证"""
            # 测试网格交易参数验证
            try:
                # 正常参数
                strategy = GridTrading(
                    exchange=self.mock_exchange,
                    symbol='CFX/USDT',
                    base_price=0.213456,
                    grid_spacing=2.0,
                    grid_count=10,
                    order_amount=100
                )
                self.framework.assert_not_none(strategy, "正常参数应该创建成功")
            except Exception as e:
                self.framework.logger.warning(f"正常参数创建失败: {e}")
            
            # 测试异常参数
            invalid_params = [
                {'base_price': -0.1},  # 负价格
                {'grid_spacing': 0},   # 零间距
                {'grid_count': -5},    # 负网格数
                {'order_amount': 0},   # 零数量
            ]
            
            for params in invalid_params:
                try:
                    strategy = GridTrading(
                        exchange=self.mock_exchange,
                        symbol='CFX/USDT',
                        base_price=params.get('base_price', 0.213456),
                        grid_spacing=params.get('grid_spacing', 2.0),
                        grid_count=params.get('grid_count', 10),
                        order_amount=params.get('order_amount', 100)
                    )
                    # 如果创建成功，检查参数是否被正确处理
                    self.framework.logger.info(f"参数 {params} 创建成功，可能有内部验证")
                except Exception as e:
                    # 预期会抛出异常
                    self.framework.logger.info(f"参数 {params} 正确被拒绝: {e}")
        
        def test_strategy_state_protection():
            """测试策略状态保护"""
            strategy = GridTrading(
                exchange=self.mock_exchange,
                symbol='CFX/USDT',
                base_price=0.213456,
                grid_spacing=2.0,
                grid_count=10,
                order_amount=100
            )
            
            # 测试初始状态
            self.framework.assert_false(strategy.running, "策略初始状态应为未运行")
            
            # 测试状态切换保护
            strategy.running = True
            self.framework.assert_true(strategy.running, "应该能设置运行状态")
            
            strategy.stop()
            self.framework.assert_false(strategy.running, "停止后应为未运行状态")
        
        # 运行策略安全测试
        self.framework.run_test_case(
            test_strategy_parameter_validation,
            "策略参数验证测试",
            "验证策略参数的安全验证",
            "安全性测试",
            "HIGH"
        )
        
        self.framework.run_test_case(
            test_strategy_state_protection,
            "策略状态保护测试",
            "验证策略状态的安全保护",
            "安全性测试",
            "MEDIUM"
        )
    
    def run_all_security_tests(self):
        """运行所有安全性测试"""
        print("开始安全性测试...")
        
        # 测试所有安全功能
        self.test_environment_security()
        self.test_risk_management_security()
        self.test_data_protection()
        self.test_strategy_security()
        
        print("安全性测试完成")

def main():
    """主测试函数"""
    print("=" * 80)
    print("量化交易系统 - 安全性全面测试")
    print("=" * 80)
    
    tester = SecurityTester()
    tester.run_all_security_tests()
    
    # 生成测试报告
    summary = test_framework.reporter.generate_summary()
    print(f"\n安全性测试摘要:")
    print(f"总测试用例: {summary['total']}")
    print(f"通过: {summary['passed']}")
    print(f"失败: {summary['failed']}")
    print(f"错误: {summary['errors']}")
    print(f"通过率: {summary['pass_rate']:.1f}%")

if __name__ == "__main__":
    main()
