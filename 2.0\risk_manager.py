"""
风险管理模块
提供全面的风险控制和监控功能
"""
import time
import threading
from datetime import datetime, timedelta
from logger import get_logger, log_risk_alert
import pandas as pd
import numpy as np

class RiskManager:
    def __init__(self, exchange, initial_capital=10000):
        self.exchange = exchange
        self.initial_capital = initial_capital
        self.max_daily_loss = 0.02  # 最大日亏损2%
        self.max_position_size = 0.1  # 最大仓位不超过10%
        self.max_drawdown = 0.15  # 最大回撤15%
        self.daily_pnl = {}
        self.peak_balance = initial_capital
        self.logger = get_logger("risk_manager")
        self.monitoring = False
        self.monitor_thread = None
    
    def check_daily_loss_limit(self):
        """检查单日亏损限制"""
        today = datetime.now().strftime('%Y-%m-%d')
        current_balance = self.get_current_balance()
        
        if today not in self.daily_pnl:
            self.daily_pnl[today] = current_balance
        
        daily_pnl = (current_balance - self.daily_pnl[today]) / self.daily_pnl[today]
        
        if daily_pnl < -self.max_daily_loss:
            log_risk_alert("risk_manager", "日亏损限制", f"当日亏损:{daily_pnl:.2%}")
            self.logger.warning(f"触发日亏损止损!当日亏损:{daily_pnl:.2%}")
            return False
        
        return True
    
    def check_position_size(self, symbol, position_value):
        """检查仓位大小"""
        current_balance = self.get_current_balance()
        position_ratio = position_value / current_balance
        
        if position_ratio > self.max_position_size:
            log_risk_alert("risk_manager", "仓位超限", f"{symbol} 仓位比例:{position_ratio:.2%}")
            self.logger.warning(f"仓位超限!{symbol} 仓位比例:{position_ratio:.2%}")
            return False
        
        return True
    
    def check_max_drawdown(self):
        """检查最大回撤"""
        current_balance = self.get_current_balance()
        
        if current_balance > self.peak_balance:
            self.peak_balance = current_balance
        
        drawdown = (self.peak_balance - current_balance) / self.peak_balance
        
        if drawdown > self.max_drawdown:
            log_risk_alert("risk_manager", "最大回撤", f"当前回撤:{drawdown:.2%}")
            self.logger.warning(f"触发最大回撤止损!当前回撤:{drawdown:.2%}")
            return False
        
        return True
    
    def get_current_balance(self):
        """获取当前账户余额"""
        try:
            balance = self.exchange.fetch_balance()
            total_balance = balance['total']['USDT']
            
            # 计算其他币种的价值
            for currency, amount in balance['total'].items():
                if currency != 'USDT' and amount > 0:
                    try:
                        ticker = self.exchange.fetch_ticker(f"{currency}/USDT")
                        total_balance += amount * ticker['last']
                    except:
                        pass  # 忽略无法获取价格的币种
            
            return total_balance
        except Exception as e:
            self.logger.error(f"获取账户余额失败:{e}")
            return self.initial_capital
    
    def calculate_kelly_criterion(self, win_rate, avg_win, avg_loss):
        """计算凯利公式最优仓位"""
        if avg_loss == 0:
            return 0
        
        win_loss_ratio = avg_win / avg_loss
        kelly_pct = (win_rate * win_loss_ratio - (1 - win_rate)) / win_loss_ratio
        
        # 保守起见，使用1/4凯利
        return max(0, min(kelly_pct * 0.25, 0.1))
    
    def should_stop_trading(self):
        """综合风险检查"""
        checks = [
            self.check_daily_loss_limit(),
            self.check_max_drawdown()
        ]
        
        return not all(checks)
    
    def get_risk_metrics(self):
        """获取风险指标"""
        current_balance = self.get_current_balance()
        today = datetime.now().strftime('%Y-%m-%d')
        
        # 计算当日盈亏
        if today in self.daily_pnl:
            daily_pnl = (current_balance - self.daily_pnl[today]) / self.daily_pnl[today]
        else:
            daily_pnl = 0
        
        # 计算总收益
        total_return = (current_balance - self.initial_capital) / self.initial_capital
        
        # 计算当前回撤
        drawdown = (self.peak_balance - current_balance) / self.peak_balance if self.peak_balance > 0 else 0
        
        return {
            'current_balance': current_balance,
            'daily_pnl': daily_pnl,
            'total_return': total_return,
            'drawdown': drawdown,
            'peak_balance': self.peak_balance
        }
    
    def start_monitoring(self):
        """开始风险监控"""
        if self.monitoring:
            return
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
        self.logger.info("风险监控已启动")
    
    def stop_monitoring(self):
        """停止风险监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        self.logger.info("风险监控已停止")
    
    def _monitor_loop(self):
        """监控循环"""
        while self.monitoring:
            try:
                # 每分钟检查一次风险指标
                metrics = self.get_risk_metrics()
                
                # 记录风险指标
                if metrics['daily_pnl'] < -0.01:  # 日亏损超过1%时警告
                    log_risk_alert("risk_manager", "日亏损警告", f"当日亏损:{metrics['daily_pnl']:.2%}")
                
                if metrics['drawdown'] > 0.1:  # 回撤超过10%时警告
                    log_risk_alert("risk_manager", "回撤警告", f"当前回撤:{metrics['drawdown']:.2%}")
                
                time.sleep(60)  # 每分钟检查一次
            except Exception as e:
                self.logger.error(f"风险监控错误:{e}")
                time.sleep(60)


class SafeTradingBot:
    """安全交易机器人"""
    def __init__(self, exchange, strategy, risk_manager):
        self.exchange = exchange
        self.strategy = strategy
        self.risk_manager = risk_manager
        self.is_trading_paused = False
        self.logger = get_logger("safe_trading_bot")
    
    def execute_trade_with_risk_check(self, signal, symbol, amount):
        """带风险检查的交易执行"""
        # 检查是否应该停止交易
        if self.risk_manager.should_stop_trading():
            self.is_trading_paused = True
            self.logger.warning("风险控制:暂停交易")
            return False
        
        # 检查仓位大小
        current_price = self.get_current_price(symbol)
        position_value = amount * current_price
        
        if not self.risk_manager.check_position_size(symbol, position_value):
            # 调整仓位大小
            max_position_value = self.risk_manager.get_current_balance() * self.risk_manager.max_position_size
            amount = max_position_value / current_price
            self.logger.info(f"调整仓位大小:{amount:.6f}")
        
        # 执行交易
        try:
            if signal == 1:  # 买入
                order = self.exchange.create_market_buy_order(symbol, amount)
                self.logger.info(f"买入执行:{amount:.6f} @{current_price:.2f}")
            elif signal == -1:  # 卖出
                order = self.exchange.create_market_sell_order(symbol, amount)
                self.logger.info(f"卖出执行:{amount:.6f} @ {current_price:.2f}")
            return True
        except Exception as e:
            self.logger.error(f"交易执行失败: {e}")
            return False
    
    def get_current_price(self, symbol):
        """获取当前价格"""
        ticker = self.exchange.fetch_ticker(symbol)
        return ticker['last']
    
    def run_with_risk_control(self):
        """带风险控制的策略运行"""
        self.logger.info("启动风险控制交易...")
        self.risk_manager.start_monitoring()
        
        while True:
            try:
                if self.is_trading_paused:
                    # 每小时检查一次是否可以恢复交易
                    if not self.risk_manager.should_stop_trading():
                        self.is_trading_paused = False
                        self.logger.info("风险状况改善,恢复交易")
                    else:
                        self.logger.info("风险控制中,暂停交易")
                        time.sleep(3600)  # 等待1小时
                        continue
                
                # 获取风险指标并显示
                metrics = self.risk_manager.get_risk_metrics()
                total_return = metrics['total_return']
                current_balance = metrics['current_balance']
                
                self.logger.info(f"当前余额: ${current_balance:.2f},总收益率:{total_return:.2%}")
                
                time.sleep(300)  # 5分钟检查一次
            except KeyboardInterrupt:
                self.logger.info("交易停止")
                break
            except Exception as e:
                self.logger.error(f"运行错误: {e}")
                time.sleep(60)
        
        self.risk_manager.stop_monitoring()
    
    def stop(self):
        """停止交易机器人"""
        self.is_trading_paused = True
        self.risk_manager.stop_monitoring()
        self.logger.info("安全交易机器人已停止")
