('C:\\Users\\<USER>\\Desktop\\yl\\build\\MyApp\\MyApp.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'C:\\Users\\<USER>\\Desktop\\yl\\build\\MyApp\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'C:\\Users\\<USER>\\Desktop\\yl\\build\\MyApp\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'C:\\Users\\<USER>\\Desktop\\yl\\build\\MyApp\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'C:\\Users\\<USER>\\Desktop\\yl\\build\\MyApp\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'C:\\Users\\<USER>\\Desktop\\yl\\build\\MyApp\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'C:\\Users\\<USER>\\Desktop\\yl\\build\\MyApp\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'c:\\program '
   'files\\python\\lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Program '
   'Files\\python\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Program '
   'Files\\python\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Program '
   'Files\\python\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_cryptography_openssl',
   'C:\\Program '
   'Files\\python\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_cryptography_openssl.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'C:\\Program '
   'Files\\python\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'C:\\Program '
   'Files\\python\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_mplconfig',
   'C:\\Program '
   'Files\\python\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_mplconfig.py',
   'PYSOURCE'),
  ('pyi_rth_pywintypes',
   'C:\\Program '
   'Files\\python\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_pywintypes.py',
   'PYSOURCE'),
  ('pyi_rth_pythoncom',
   'C:\\Program '
   'Files\\python\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_pythoncom.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'C:\\Program '
   'Files\\python\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('main', 'C:\\Users\\<USER>\\Desktop\\yl\\main.py', 'PYSOURCE'),
  ('yl\\dist\\MyApp.exe',
   'C:\\Users\\<USER>\\Desktop\\yl\\dist\\MyApp.exe',
   'BINARY'),
  ('python38.dll', 'c:\\program files\\python\\python38.dll', 'BINARY'),
  ('numpy\\.libs\\libopenblas64__v0.3.21-gcc_10_3_0.dll',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\.libs\\libopenblas64__v0.3.21-gcc_10_3_0.dll',
   'BINARY'),
  ('matplotlib.libs\\msvcp140-456d948669199b545d061b84c160bebc.dll',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib.libs\\msvcp140-456d948669199b545d061b84c160bebc.dll',
   'BINARY'),
  ('pywin32_system32\\pywintypes38.dll',
   'c:\\program '
   'files\\python\\Lib\\site-packages\\pywin32_system32\\pywintypes38.dll',
   'BINARY'),
  ('pywin32_system32\\pythoncom38.dll',
   'c:\\program '
   'files\\python\\Lib\\site-packages\\pywin32_system32\\pythoncom38.dll',
   'BINARY'),
  ('_ctypes.pyd', 'c:\\program files\\python\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_hashlib.pyd',
   'c:\\program files\\python\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'c:\\program files\\python\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd', 'c:\\program files\\python\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd', 'c:\\program files\\python\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'c:\\program files\\python\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'c:\\program files\\python\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_overlapped.pyd',
   'c:\\program files\\python\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_ssl.pyd', 'c:\\program files\\python\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_asyncio.pyd',
   'c:\\program files\\python\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('_queue.pyd', 'c:\\program files\\python\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'c:\\program files\\python\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd', 'c:\\program files\\python\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_decimal.pyd',
   'c:\\program files\\python\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_tests.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\core\\_multiarray_tests.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\psutil\\_psutil_windows.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_umath.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\core\\_multiarray_umath.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('win32\\win32pdh.pyd',
   'c:\\program files\\python\\lib\\site-packages\\win32\\win32pdh.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\lapack_lite.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\linalg\\lapack_lite.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\random\\mtrand.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\random\\_sfc64.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\random\\_philox.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\random\\_pcg64.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\random\\_mt19937.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\random\\bit_generator.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\random\\_generator.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\random\\_bounded_integers.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\random\\_common.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_internal.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\fft\\_pocketfft_internal.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('win32\\win32wnet.pyd',
   'c:\\program files\\python\\lib\\site-packages\\win32\\win32wnet.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_rust.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\_cffi_backend.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\charset_normalizer\\md__mypyc.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\charset_normalizer\\md.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('bcrypt\\_bcrypt.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\bcrypt\\_bcrypt.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\writers.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\_libs\\writers.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('markupsafe\\_speedups.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\markupsafe\\_speedups.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_path.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\_path.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_c_internal_utils.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\_c_internal_utils.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\PIL\\_imagingtk.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\PIL\\_webp.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\PIL\\_imaging.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'c:\\program files\\python\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('matplotlib\\backends\\_tkagg.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\backends\\_tkagg.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\backends\\_backend_agg.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\backends\\_backend_agg.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\ft2font.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\ft2font.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('contourpy\\_contourpy.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\contourpy\\_contourpy.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_tri.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\_tri.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_qhull.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\_qhull.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('kiwisolver\\_cext.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\kiwisolver\\_cext.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_image.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\_image.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cprocessors.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\cprocessors.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cresultproxy.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\cresultproxy.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cutils.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\cutils.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd',
   'c:\\program files\\python\\DLLs\\_sqlite3.pyd',
   'EXTENSION'),
  ('win32\\_win32sysloader.pyd',
   'c:\\program files\\python\\lib\\site-packages\\win32\\_win32sysloader.pyd',
   'EXTENSION'),
  ('win32\\win32api.pyd',
   'c:\\program files\\python\\lib\\site-packages\\win32\\win32api.pyd',
   'EXTENSION'),
  ('Pythonwin\\win32ui.pyd',
   'c:\\program files\\python\\lib\\site-packages\\Pythonwin\\win32ui.pyd',
   'EXTENSION'),
  ('win32\\win32trace.pyd',
   'c:\\program files\\python\\lib\\site-packages\\win32\\win32trace.pyd',
   'EXTENSION'),
  ('MySQLdb\\_mysql.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\MySQLdb\\_mysql.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\etree.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\lxml\\etree.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\_elementpath.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\lxml\\_elementpath.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\sax.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\lxml\\sax.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\objectify.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\lxml\\objectify.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\html\\diff.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\lxml\\html\\diff.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\html\\clean.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\lxml\\html\\clean.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\builder.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\lxml\\builder.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\indexers.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\_libs\\window\\indexers.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\aggregations.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\_libs\\window\\aggregations.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\vectorized.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\_libs\\tslibs\\vectorized.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\tzconversion.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\_libs\\tslibs\\tzconversion.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timezones.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\_libs\\tslibs\\timezones.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timestamps.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\_libs\\tslibs\\timestamps.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timedeltas.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\_libs\\tslibs\\timedeltas.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\strptime.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\_libs\\tslibs\\strptime.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\period.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\_libs\\tslibs\\period.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\parsing.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\_libs\\tslibs\\parsing.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\offsets.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\_libs\\tslibs\\offsets.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\np_datetime.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\_libs\\tslibs\\np_datetime.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\nattype.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\_libs\\tslibs\\nattype.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\fields.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\_libs\\tslibs\\fields.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\dtypes.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\_libs\\tslibs\\dtypes.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\conversion.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\_libs\\tslibs\\conversion.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\ccalendar.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\_libs\\tslibs\\ccalendar.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\base.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\_libs\\tslibs\\base.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\testing.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\_libs\\testing.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\sparse.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\_libs\\sparse.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\reshape.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\_libs\\reshape.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\reduction.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\_libs\\reduction.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\properties.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\_libs\\properties.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\parsers.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\_libs\\parsers.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops_dispatch.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\_libs\\ops_dispatch.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\_libs\\ops.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\missing.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\_libs\\missing.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\json.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\_libs\\json.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\join.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\_libs\\join.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\interval.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\_libs\\interval.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\internals.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\_libs\\internals.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\indexing.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\_libs\\indexing.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\index.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\_libs\\index.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashing.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\_libs\\hashing.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\groupby.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\_libs\\groupby.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\arrays.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\_libs\\arrays.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\algos.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\_libs\\algos.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\io\\sas\\_sas.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\io\\sas\\_sas.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\io\\sas\\_byteswap.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\io\\sas\\_byteswap.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslib.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\_libs\\tslib.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\lib.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\_libs\\lib.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashtable.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\_libs\\hashtable.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('_tkinter.pyd',
   'c:\\program files\\python\\DLLs\\_tkinter.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Program Files (x86)\\Python打包助手\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Program Files (x86)\\Python打包助手\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Program Files (x86)\\Python打包助手\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Program Files (x86)\\Python打包助手\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Program Files (x86)\\Python打包助手\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Program Files (x86)\\Python打包助手\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll', 'c:\\program files\\python\\VCRUNTIME140.dll', 'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Program Files (x86)\\Python打包助手\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Program Files (x86)\\Python打包助手\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Program Files (x86)\\Python打包助手\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Program Files (x86)\\Python打包助手\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Program Files (x86)\\Python打包助手\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Program Files (x86)\\Python打包助手\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Program Files (x86)\\Python打包助手\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('libffi-7.dll', 'c:\\program files\\python\\DLLs\\libffi-7.dll', 'BINARY'),
  ('libcrypto-1_1.dll',
   'c:\\program files\\python\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('libssl-1_1.dll',
   'c:\\program files\\python\\DLLs\\libssl-1_1.dll',
   'BINARY'),
  ('win32\\pywintypes38.dll',
   'c:\\program files\\python\\lib\\site-packages\\win32\\pywintypes38.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Program Files (x86)\\Python打包助手\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('python3.dll', 'c:\\program files\\python\\python3.dll', 'BINARY'),
  ('MSVCP140.dll',
   'C:\\Program Files (x86)\\Python打包助手\\PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'c:\\program files\\python\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('sqlite3.dll', 'c:\\program files\\python\\DLLs\\sqlite3.dll', 'BINARY'),
  ('Pythonwin\\mfc140u.dll',
   'c:\\program files\\python\\lib\\site-packages\\Pythonwin\\mfc140u.dll',
   'BINARY'),
  ('pandas\\_libs\\window\\MSVCP140.dll',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\_libs\\window\\MSVCP140.dll',
   'BINARY'),
  ('pandas\\_libs\\window\\VCRUNTIME140_1.dll',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\_libs\\window\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('tk86t.dll', 'c:\\program files\\python\\DLLs\\tk86t.dll', 'BINARY'),
  ('tcl86t.dll', 'c:\\program files\\python\\DLLs\\tcl86t.dll', 'BINARY'),
  ('ucrtbase.dll',
   'C:\\Program Files (x86)\\Python打包助手\\ucrtbase.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Program Files (x86)\\Python打包助手\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Program Files (x86)\\Python打包助手\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Program Files '
   '(x86)\\Python打包助手\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Program Files (x86)\\Python打包助手\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Program Files '
   '(x86)\\Python打包助手\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Program Files (x86)\\Python打包助手\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Program Files (x86)\\Python打包助手\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Program Files (x86)\\Python打包助手\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Program Files '
   '(x86)\\Python打包助手\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Program Files (x86)\\Python打包助手\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Program Files '
   '(x86)\\Python打包助手\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Program Files (x86)\\Python打包助手\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Program Files (x86)\\Python打包助手\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Program Files (x86)\\Python打包助手\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Program Files (x86)\\Python打包助手\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Program Files '
   '(x86)\\Python打包助手\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Program Files (x86)\\Python打包助手\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Program Files (x86)\\Python打包助手\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Program Files '
   '(x86)\\Python打包助手\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Program Files (x86)\\Python打包助手\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Program Files '
   '(x86)\\Python打包助手\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Program Files (x86)\\Python打包助手\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Program Files (x86)\\Python打包助手\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Program Files (x86)\\Python打包助手\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('yl\\4.txt', 'C:\\Users\\<USER>\\Desktop\\yl\\4.txt', 'DATA'),
  ('yl\\44.rtf', 'C:\\Users\\<USER>\\Desktop\\yl\\44.rtf', 'DATA'),
  ('yl\\444.docx', 'C:\\Users\\<USER>\\Desktop\\yl\\444.docx', 'DATA'),
  ('yl\\GATE_API_GUIDE.md',
   'C:\\Users\\<USER>\\Desktop\\yl\\GATE_API_GUIDE.md',
   'DATA'),
  ('yl\\GATE_INTEGRATION_SUMMARY.md',
   'C:\\Users\\<USER>\\Desktop\\yl\\GATE_INTEGRATION_SUMMARY.md',
   'DATA'),
  ('yl\\MyApp.spec',
   'C:\\Users\\<USER>\\Desktop\\yl\\MyApp.spec',
   'DATA'),
  ('yl\\PROJECT_SUMMARY.md',
   'C:\\Users\\<USER>\\Desktop\\yl\\PROJECT_SUMMARY.md',
   'DATA'),
  ('yl\\PYINSTALLER_FIX.md',
   'C:\\Users\\<USER>\\Desktop\\yl\\PYINSTALLER_FIX.md',
   'DATA'),
  ('yl\\QuantTradingSystem.spec',
   'C:\\Users\\<USER>\\Desktop\\yl\\QuantTradingSystem.spec',
   'DATA'),
  ('yl\\README.md', 'C:\\Users\\<USER>\\Desktop\\yl\\README.md', 'DATA'),
  ('yl\\build_exe.bat',
   'C:\\Users\\<USER>\\Desktop\\yl\\build_exe.bat',
   'DATA'),
  ('yl\\build_solution.md',
   'C:\\Users\\<USER>\\Desktop\\yl\\build_solution.md',
   'DATA'),
  ('yl\\config.py', 'C:\\Users\\<USER>\\Desktop\\yl\\config.py', 'DATA'),
  ('yl\\exchange_manager.py',
   'C:\\Users\\<USER>\\Desktop\\yl\\exchange_manager.py',
   'DATA'),
  ('yl\\final_verification.py',
   'C:\\Users\\<USER>\\Desktop\\yl\\final_verification.py',
   'DATA'),
  ('yl\\fix_and_build.bat',
   'C:\\Users\\<USER>\\Desktop\\yl\\fix_and_build.bat',
   'DATA'),
  ('yl\\fix_icon_issue.py',
   'C:\\Users\\<USER>\\Desktop\\yl\\fix_icon_issue.py',
   'DATA'),
  ('yl\\fix_pyinstaller.py',
   'C:\\Users\\<USER>\\Desktop\\yl\\fix_pyinstaller.py',
   'DATA'),
  ('yl\\gate_adapter.py',
   'C:\\Users\\<USER>\\Desktop\\yl\\gate_adapter.py',
   'DATA'),
  ('yl\\install.py',
   'C:\\Users\\<USER>\\Desktop\\yl\\install.py',
   'DATA'),
  ('yl\\logger.py', 'C:\\Users\\<USER>\\Desktop\\yl\\logger.py', 'DATA'),
  ('yl\\logs\\GridTradingTab_20250805.log',
   'C:\\Users\\<USER>\\Desktop\\yl\\logs\\GridTradingTab_20250805.log',
   'DATA'),
  ('yl\\logs\\MovingAverageTab_20250805.log',
   'C:\\Users\\<USER>\\Desktop\\yl\\logs\\MovingAverageTab_20250805.log',
   'DATA'),
  ('yl\\logs\\RSIStrategyTab_20250805.log',
   'C:\\Users\\<USER>\\Desktop\\yl\\logs\\RSIStrategyTab_20250805.log',
   'DATA'),
  ('yl\\logs\\RiskManagementTab_20250805.log',
   'C:\\Users\\<USER>\\Desktop\\yl\\logs\\RiskManagementTab_20250805.log',
   'DATA'),
  ('yl\\logs\\SmartGridTab_20250805.log',
   'C:\\Users\\<USER>\\Desktop\\yl\\logs\\SmartGridTab_20250805.log',
   'DATA'),
  ('yl\\logs\\VolumeBreakoutTab_20250805.log',
   'C:\\Users\\<USER>\\Desktop\\yl\\logs\\VolumeBreakoutTab_20250805.log',
   'DATA'),
  ('yl\\logs\\exchange_manager_20250805.log',
   'C:\\Users\\<USER>\\Desktop\\yl\\logs\\exchange_manager_20250805.log',
   'DATA'),
  ('yl\\logs\\gate_adapter_20250805.log',
   'C:\\Users\\<USER>\\Desktop\\yl\\logs\\gate_adapter_20250805.log',
   'DATA'),
  ('yl\\logs\\main_gui_20250805.log',
   'C:\\Users\\<USER>\\Desktop\\yl\\logs\\main_gui_20250805.log',
   'DATA'),
  ('yl\\logs\\moving_average_20250805.log',
   'C:\\Users\\<USER>\\Desktop\\yl\\logs\\moving_average_20250805.log',
   'DATA'),
  ('yl\\logs\\risk_manager_20250805.log',
   'C:\\Users\\<USER>\\Desktop\\yl\\logs\\risk_manager_20250805.log',
   'DATA'),
  ('yl\\logs\\rsi_strategy_20250805.log',
   'C:\\Users\\<USER>\\Desktop\\yl\\logs\\rsi_strategy_20250805.log',
   'DATA'),
  ('yl\\logs\\test_20250805.log',
   'C:\\Users\\<USER>\\Desktop\\yl\\logs\\test_20250805.log',
   'DATA'),
  ('yl\\logs\\test_strategy_20250805.log',
   'C:\\Users\\<USER>\\Desktop\\yl\\logs\\test_strategy_20250805.log',
   'DATA'),
  ('yl\\logs\\trading_20250805.log',
   'C:\\Users\\<USER>\\Desktop\\yl\\logs\\trading_20250805.log',
   'DATA'),
  ('yl\\main.py', 'C:\\Users\\<USER>\\Desktop\\yl\\main.py', 'DATA'),
  ('yl\\main_gui.py',
   'C:\\Users\\<USER>\\Desktop\\yl\\main_gui.py',
   'DATA'),
  ('yl\\quick_build.bat',
   'C:\\Users\\<USER>\\Desktop\\yl\\quick_build.bat',
   'DATA'),
  ('yl\\requirements.txt',
   'C:\\Users\\<USER>\\Desktop\\yl\\requirements.txt',
   'DATA'),
  ('yl\\risk_manager.py',
   'C:\\Users\\<USER>\\Desktop\\yl\\risk_manager.py',
   'DATA'),
  ('yl\\rz.txt', 'C:\\Users\\<USER>\\Desktop\\yl\\rz.txt', 'DATA'),
  ('yl\\strategies.py',
   'C:\\Users\\<USER>\\Desktop\\yl\\strategies.py',
   'DATA'),
  ('yl\\strategies_extended.py',
   'C:\\Users\\<USER>\\Desktop\\yl\\strategies_extended.py',
   'DATA'),
  ('yl\\strategy_tabs.py',
   'C:\\Users\\<USER>\\Desktop\\yl\\strategy_tabs.py',
   'DATA'),
  ('yl\\test_gate_integration.py',
   'C:\\Users\\<USER>\\Desktop\\yl\\test_gate_integration.py',
   'DATA'),
  ('yl\\test_system.py',
   'C:\\Users\\<USER>\\Desktop\\yl\\test_system.py',
   'DATA'),
  ('yl\\three_exchanges_api_analysis.md',
   'C:\\Users\\<USER>\\Desktop\\yl\\three_exchanges_api_analysis.md',
   'DATA'),
  ('yl\\three_exchanges_config.py',
   'C:\\Users\\<USER>\\Desktop\\yl\\three_exchanges_config.py',
   'DATA'),
  ('yl\\three_exchanges_example.py',
   'C:\\Users\\<USER>\\Desktop\\yl\\three_exchanges_example.py',
   'DATA'),
  ('yl\\unified_trading_system.py',
   'C:\\Users\\<USER>\\Desktop\\yl\\unified_trading_system.py',
   'DATA'),
  ('yl\\这些量化策略再蠢也能赚点零花钱（附代码）.html',
   'C:\\Users\\<USER>\\Desktop\\yl\\这些量化策略再蠢也能赚点零花钱（附代码）.html',
   'DATA'),
  ('yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\0',
   'C:\\Users\\<USER>\\Desktop\\yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\0',
   'DATA'),
  ('yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\0(1)',
   'C:\\Users\\<USER>\\Desktop\\yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\0(1)',
   'DATA'),
  ('yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\300',
   'C:\\Users\\<USER>\\Desktop\\yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\300',
   'DATA'),
  ('yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640',
   'C:\\Users\\<USER>\\Desktop\\yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640',
   'DATA'),
  ('yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(1)',
   'C:\\Users\\<USER>\\Desktop\\yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(1)',
   'DATA'),
  ('yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(10)',
   'C:\\Users\\<USER>\\Desktop\\yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(10)',
   'DATA'),
  ('yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(11)',
   'C:\\Users\\<USER>\\Desktop\\yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(11)',
   'DATA'),
  ('yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(12)',
   'C:\\Users\\<USER>\\Desktop\\yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(12)',
   'DATA'),
  ('yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(13)',
   'C:\\Users\\<USER>\\Desktop\\yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(13)',
   'DATA'),
  ('yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(14)',
   'C:\\Users\\<USER>\\Desktop\\yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(14)',
   'DATA'),
  ('yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(15)',
   'C:\\Users\\<USER>\\Desktop\\yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(15)',
   'DATA'),
  ('yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(16)',
   'C:\\Users\\<USER>\\Desktop\\yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(16)',
   'DATA'),
  ('yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(17)',
   'C:\\Users\\<USER>\\Desktop\\yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(17)',
   'DATA'),
  ('yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(18)',
   'C:\\Users\\<USER>\\Desktop\\yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(18)',
   'DATA'),
  ('yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(19)',
   'C:\\Users\\<USER>\\Desktop\\yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(19)',
   'DATA'),
  ('yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(2)',
   'C:\\Users\\<USER>\\Desktop\\yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(2)',
   'DATA'),
  ('yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(20)',
   'C:\\Users\\<USER>\\Desktop\\yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(20)',
   'DATA'),
  ('yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(21)',
   'C:\\Users\\<USER>\\Desktop\\yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(21)',
   'DATA'),
  ('yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(22)',
   'C:\\Users\\<USER>\\Desktop\\yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(22)',
   'DATA'),
  ('yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(23)',
   'C:\\Users\\<USER>\\Desktop\\yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(23)',
   'DATA'),
  ('yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(24)',
   'C:\\Users\\<USER>\\Desktop\\yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(24)',
   'DATA'),
  ('yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(25)',
   'C:\\Users\\<USER>\\Desktop\\yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(25)',
   'DATA'),
  ('yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(26)',
   'C:\\Users\\<USER>\\Desktop\\yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(26)',
   'DATA'),
  ('yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(27)',
   'C:\\Users\\<USER>\\Desktop\\yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(27)',
   'DATA'),
  ('yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(28)',
   'C:\\Users\\<USER>\\Desktop\\yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(28)',
   'DATA'),
  ('yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(29)',
   'C:\\Users\\<USER>\\Desktop\\yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(29)',
   'DATA'),
  ('yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(3)',
   'C:\\Users\\<USER>\\Desktop\\yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(3)',
   'DATA'),
  ('yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(30)',
   'C:\\Users\\<USER>\\Desktop\\yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(30)',
   'DATA'),
  ('yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(31)',
   'C:\\Users\\<USER>\\Desktop\\yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(31)',
   'DATA'),
  ('yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(32)',
   'C:\\Users\\<USER>\\Desktop\\yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(32)',
   'DATA'),
  ('yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(33)',
   'C:\\Users\\<USER>\\Desktop\\yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(33)',
   'DATA'),
  ('yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(34)',
   'C:\\Users\\<USER>\\Desktop\\yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(34)',
   'DATA'),
  ('yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(35)',
   'C:\\Users\\<USER>\\Desktop\\yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(35)',
   'DATA'),
  ('yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(36)',
   'C:\\Users\\<USER>\\Desktop\\yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(36)',
   'DATA'),
  ('yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(37)',
   'C:\\Users\\<USER>\\Desktop\\yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(37)',
   'DATA'),
  ('yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(4)',
   'C:\\Users\\<USER>\\Desktop\\yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(4)',
   'DATA'),
  ('yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(5)',
   'C:\\Users\\<USER>\\Desktop\\yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(5)',
   'DATA'),
  ('yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(6)',
   'C:\\Users\\<USER>\\Desktop\\yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(6)',
   'DATA'),
  ('yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(7)',
   'C:\\Users\\<USER>\\Desktop\\yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(7)',
   'DATA'),
  ('yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(8)',
   'C:\\Users\\<USER>\\Desktop\\yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(8)',
   'DATA'),
  ('yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(9)',
   'C:\\Users\\<USER>\\Desktop\\yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\640(9)',
   'DATA'),
  ('yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\appmsg.mdss9pkh42ad8d3a.js.下载',
   'C:\\Users\\<USER>\\Desktop\\yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\appmsg.mdss9pkh42ad8d3a.js.下载',
   'DATA'),
  ('yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\channel_product_utils.mdss9pkh199e90e4.css',
   'C:\\Users\\<USER>\\Desktop\\yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\channel_product_utils.mdss9pkh199e90e4.css',
   'DATA'),
  ('yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\cover_next.mdss9pkh356e3217.css',
   'C:\\Users\\<USER>\\Desktop\\yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\cover_next.mdss9pkh356e3217.css',
   'DATA'),
  ('yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\frame.html',
   'C:\\Users\\<USER>\\Desktop\\yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\frame.html',
   'DATA'),
  ('yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\index.mdss9pkh648611c0.css',
   'C:\\Users\\<USER>\\Desktop\\yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\index.mdss9pkh648611c0.css',
   'DATA'),
  ('yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\interaction.mdss9pkh75c44785.css',
   'C:\\Users\\<USER>\\Desktop\\yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\interaction.mdss9pkh75c44785.css',
   'DATA'),
  ('yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\lottery.mdss9pkh8fa27823.css',
   'C:\\Users\\<USER>\\Desktop\\yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\lottery.mdss9pkh8fa27823.css',
   'DATA'),
  ('yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\opqv3ix6k9E4e64ZzO7uIqE3ZblwIojfmt7u70m59yS1ylFK-hTu6Ra8V_LaWQJ1P4OlUJPdXLfVBtrm3TwRrw',
   'C:\\Users\\<USER>\\Desktop\\yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\opqv3ix6k9E4e64ZzO7uIqE3ZblwIojfmt7u70m59yS1ylFK-hTu6Ra8V_LaWQJ1P4OlUJPdXLfVBtrm3TwRrw',
   'DATA'),
  ('yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\polyfills-legacy.mdss9pkhef21609a.js.下载',
   'C:\\Users\\<USER>\\Desktop\\yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\polyfills-legacy.mdss9pkhef21609a.js.下载',
   'DATA'),
  ('yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\preview_reload.mdss9pkhb52f7963.css',
   'C:\\Users\\<USER>\\Desktop\\yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\preview_reload.mdss9pkhb52f7963.css',
   'DATA'),
  ('yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\qqmail_tpl_vite_entry.mdss9pkhbeeb7d36.css',
   'C:\\Users\\<USER>\\Desktop\\yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\qqmail_tpl_vite_entry.mdss9pkhbeeb7d36.css',
   'DATA'),
  ('yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\tencent_portfolio_light.mdss9pkh9e6f8ec8.css',
   'C:\\Users\\<USER>\\Desktop\\yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\tencent_portfolio_light.mdss9pkh9e6f8ec8.css',
   'DATA'),
  ('yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\topic.mdss9pkh54335d72.css',
   'C:\\Users\\<USER>\\Desktop\\yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\topic.mdss9pkh54335d72.css',
   'DATA'),
  ('yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\undefined',
   'C:\\Users\\<USER>\\Desktop\\yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\undefined',
   'DATA'),
  ('yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\weui.min.css',
   'C:\\Users\\<USER>\\Desktop\\yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\weui.min.css',
   'DATA'),
  ('yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\weui.min.js.下载',
   'C:\\Users\\<USER>\\Desktop\\yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\weui.min.js.下载',
   'DATA'),
  ('yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\wxopensdk.js.下载',
   'C:\\Users\\<USER>\\Desktop\\yl\\这些量化策略再蠢也能赚点零花钱（附代码）_files\\wxopensdk.js.下载',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\REQUESTED',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography-45.0.5.dist-info\\REQUESTED',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\RECORD',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography-45.0.5.dist-info\\RECORD',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\WHEEL',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography-45.0.5.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\licenses\\LICENSE.BSD',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography-45.0.5.dist-info\\licenses\\LICENSE.BSD',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\licenses\\LICENSE.APACHE',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography-45.0.5.dist-info\\licenses\\LICENSE.APACHE',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\INSTALLER',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography-45.0.5.dist-info\\INSTALLER',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\licenses\\LICENSE',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography-45.0.5.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\METADATA',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography-45.0.5.dist-info\\METADATA',
   'DATA'),
  ('certifi\\cacert.pem',
   'c:\\program files\\python\\lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'c:\\program '
   'files\\python\\lib\\site-packages\\dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anguilla',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Anguilla',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Thomas',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Thomas',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Eastern',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\US\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Malabo',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Malabo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port-au-Prince',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Port-au-Prince',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Istanbul',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Algiers',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Algiers',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Rome',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Rome',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pitcairn',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pitcairn',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vatican',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vatican',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Moncton',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Moncton',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Knox',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Knox',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Blantyre',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Blantyre',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Mountain',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\US\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maputo',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maputo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bratislava',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bratislava',
   'DATA'),
  ('pytz\\zoneinfo\\US\\East-Indiana',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\US\\East-Indiana',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Lisbon',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Lisbon',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vladivostok',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vladivostok',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\South_Pole',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\South_Pole',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Beirut',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Beirut',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Saigon',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Saigon',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kabul',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kabul',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Rothera',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Rothera',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kiritimati',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kiritimati',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Colombo',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Colombo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Eastern',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Auckland',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Auckland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anchorage',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Anchorage',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tehran',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tehran',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qyzylorda',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qyzylorda',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Makassar',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Makassar',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Canary',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Canary',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kinshasa',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kinshasa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Lucia',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Lucia',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Ponape',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Ponape',
   'DATA'),
  ('pytz\\zoneinfo\\MST7MDT',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\MST7MDT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jujuy',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nuuk',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nuuk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kralendijk',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Kralendijk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Vincent',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Vincent',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Godthab',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Godthab',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Banjul',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Banjul',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Athens',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Athens',
   'DATA'),
  ('pytz\\zoneinfo\\posixrules',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\posixrules',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macao',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macao',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Juneau',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Juneau',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Simferopol',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Simferopol',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Famagusta',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Famagusta',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\London',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\London',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Reunion',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Reunion',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santarem',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santarem',
   'DATA'),
  ('pytz\\zoneinfo\\Greenwich',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grand_Turk',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Grand_Turk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Astrakhan',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Astrakhan',
   'DATA'),
  ('pytz\\zoneinfo\\NZ-CHAT',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\NZ-CHAT',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Windhoek',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Windhoek',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Creston',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Creston',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\DeNoronha',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\DeNoronha',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atka',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Atka',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Pangnirtung',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Pangnirtung',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Palau',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Palau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lower_Princes',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Lower_Princes',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Nauru',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Nauru',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Brunei',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Brunei',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmara',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmara',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belize',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Belize',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Nelson',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Nelson',
   'DATA'),
  ('pytz\\zoneinfo\\ROK',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\ROK',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Niue',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Niue',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bogota',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Bogota',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Shiprock',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Shiprock',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-4',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-4',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Moscow',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Moscow',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tallinn',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tallinn',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\ACT',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\ACT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chungking',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chungking',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Skopje',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Skopje',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\LHI',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\LHI',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashkhabad',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashkhabad',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\West',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\West',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kamchatka',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kamchatka',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Queensland',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Queensland',
   'DATA'),
  ('pytz\\zoneinfo\\Universal',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Wayne',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Wayne',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Mariehamn',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Mariehamn',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Halifax',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Halifax',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Kitts',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Kitts',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Chagos',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Chagos',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Accra',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Accra',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Yancowinna',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Yancowinna',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bangui',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bangui',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuching',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuching',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Sao_Tome',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Sao_Tome',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UTC',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Kerguelen',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Kerguelen',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Samoa',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Havana',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Havana',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Miquelon',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Miquelon',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chongqing',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chongqing',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Anadyr',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Anadyr',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port_of_Spain',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Port_of_Spain',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Barthelemy',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Barthelemy',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Casey',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Casey',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+3',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+3',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lusaka',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lusaka',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Prague',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Prague',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kampala',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kampala',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimbu',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimbu',
   'DATA'),
  ('pytz\\zoneinfo\\zone1970.tab',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\zone1970.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+7',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+7',
   'DATA'),
  ('pytz\\zoneinfo\\GMT+0',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmera',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmera',
   'DATA'),
  ('pytz\\zoneinfo\\CST6CDT',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\CST6CDT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tomsk',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tomsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chihuahua',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Chihuahua',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Acre',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Acre',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kirov',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kirov',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santo_Domingo',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santo_Domingo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bishkek',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bishkek',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cancun',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cancun',
   'DATA'),
  ('pytz\\zoneinfo\\Navajo',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Navajo',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Zulu',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Alaska',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\US\\Alaska',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Oslo',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Oslo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Winnipeg',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Winnipeg',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Greenwich',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Sakhalin',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Sakhalin',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Berlin',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Berlin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Whitehorse',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Whitehorse',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Podgorica',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Podgorica',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Madrid',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Madrid',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belfast',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belfast',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-2',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-2',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Helsinki',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Helsinki',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\East',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\East',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rio_Branco',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rio_Branco',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tunis',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tunis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Antigua',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Antigua',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\South',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\South',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\El_Aaiun',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\El_Aaiun',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nome',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nome',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Harbin',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Harbin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Edmonton',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Edmonton',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zagreb',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zagreb',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chicago',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Chicago',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Velho',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Velho',
   'DATA'),
  ('pytz\\zoneinfo\\GB',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\GB',
   'DATA'),
  ('pytz\\zoneinfo\\America\\New_York',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\New_York',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thunder_Bay',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Thunder_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ust-Nera',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ust-Nera',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Virgin',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Virgin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Goose_Bay',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Goose_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\NSW',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\NSW',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Central',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\US\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jayapura',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jayapura',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lubumbashi',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lubumbashi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rankin_Inlet',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rankin_Inlet',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tortola',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Tortola',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bamako',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bamako',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Stanley',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Stanley',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pyongyang',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pyongyang',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Recife',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Recife',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-11',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-11',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ceuta',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ceuta',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+9',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+9',
   'DATA'),
  ('pytz\\zoneinfo\\MET',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\MET',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Adelaide',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Adelaide',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Casablanca',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Casablanca',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Victoria',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Victoria',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Yap',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Yap',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santiago',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santiago',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nassau',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nassau',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kolkata',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kolkata',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yakutat',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Yakutat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Araguaina',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Araguaina',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Conakry',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Conakry',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-5',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-5',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-8',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-8',
   'DATA'),
  ('pytz\\zoneinfo\\iso3166.tab',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\iso3166.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qatar',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qatar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Eirunepe',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Eirunepe',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ljubljana',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ljubljana',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lima',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Lima',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+11',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+11',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Sydney',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Sydney',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pontianak',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pontianak',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Costa_Rica',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Costa_Rica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Detroit',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Detroit',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tripoli',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tripoli',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bissau',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bissau',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Manila',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Manila',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tegucigalpa',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Tegucigalpa',
   'DATA'),
  ('pytz\\zoneinfo\\CET',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\CET',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Riyadh',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Riyadh',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zaporozhye',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zaporozhye',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kwajalein',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mexico_City',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Mexico_City',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\UTC',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Amsterdam',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Amsterdam',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Budapest',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Budapest',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Brisbane',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Brisbane',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayman',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cayman',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tarawa',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tarawa',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wake',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wake',
   'DATA'),
  ('pytz\\zoneinfo\\tzdata.zi',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\tzdata.zi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Salta',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Salta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cuiaba',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cuiaba',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'DATA'),
  ('pytz\\zoneinfo\\Hongkong',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Hongkong',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Aleutian',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\US\\Aleutian',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Honolulu',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Honolulu',
   'DATA'),
  ('pytz\\zoneinfo\\Cuba',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Cuba',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Darwin',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Darwin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montreal',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Montreal',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faroe',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faroe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grenada',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Grenada',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dubai',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dubai',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson_Creek',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson_Creek',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Chisinau',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Chisinau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Merida',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Merida',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Newfoundland',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Newfoundland',
   'DATA'),
  ('pytz\\zoneinfo\\Iran',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Iran',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Monaco',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Monaco',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hovd',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hovd',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Andorra',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Andorra',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Samoa',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\US\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sitka',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Sitka',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bucharest',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bucharest',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayenne',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cayenne',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Freetown',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Freetown',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Choibalsan',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Choibalsan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Antananarivo',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Antananarivo',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\West',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\West',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Johannesburg',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Johannesburg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montserrat',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Montserrat',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guam',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guam',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rosario',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rosario',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Paris',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Paris',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Gambier',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Gambier',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Riga',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Riga',
   'DATA'),
  ('pytz\\zoneinfo\\Singapore',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Caracas',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Caracas',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Harare',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Harare',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Oral',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Oral',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Monrovia',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Monrovia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mazatlan',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Mazatlan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Atyrau',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Atyrau',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yakutsk',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yakutsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mendoza',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Volgograd',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Volgograd',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Saratov',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Saratov',
   'DATA'),
  ('pytz\\zoneinfo\\Egypt',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Egypt',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Indiana-Starke',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\US\\Indiana-Starke',
   'DATA'),
  ('pytz\\zoneinfo\\America\\La_Paz',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\La_Paz',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guatemala',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guatemala',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Vancouver',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Vancouver',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Monterrey',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Monterrey',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Magadan',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Magadan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baghdad',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baghdad',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Maceio',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Maceio',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guayaquil',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guayaquil',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+10',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+10',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Minsk',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Minsk',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Truk',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Truk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Samara',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Samara',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\North',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\North',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Eucla',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Eucla',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sofia',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sofia',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+0',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+4',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+4',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Michigan',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\US\\Michigan',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mayotte',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mayotte',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bangkok',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bangkok',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Louisville',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT0',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jamaica',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Amman',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Amman',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UCT',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\Iceland',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Iceland',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuwait',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuwait',
   'DATA'),
  ('pytz\\zoneinfo\\zone.tab',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\zone.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\San_Marino',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\San_Marino',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Phoenix',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Phoenix',
   'DATA'),
  ('pytz\\zoneinfo\\ROC',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\ROC',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+6',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+6',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Knox_IN',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Knox_IN',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+12',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+12',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Campo_Grande',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Campo_Grande',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Singapore',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vilnius',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vilnius',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Vostok',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Vostok',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtau',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtau',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dakar',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dakar',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jakarta',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jakarta',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Yukon',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Yukon',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-13',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-13',
   'DATA'),
  ('pytz\\zoneinfo\\GB-Eire',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\GB-Eire',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kiev',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kiev',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tirane',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tirane',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Luanda',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Luanda',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Almaty',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Almaty',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ouagadougou',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ouagadougou',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mahe',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mahe',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Comoro',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Comoro',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bujumbura',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bujumbura',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Punta_Arenas',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Punta_Arenas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ensenada',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Ensenada',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Midway',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Midway',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia_Banderas',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia_Banderas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rainy_River',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rainy_River',
   'DATA'),
  ('pytz\\zoneinfo\\Jamaica',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cordoba',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Resolute',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Resolute',
   'DATA'),
  ('pytz\\zoneinfo\\EST',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\EST',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-0',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Toronto',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Toronto',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nipigon',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nipigon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Noronha',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Noronha',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tbilisi',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tbilisi',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indianapolis',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Omsk',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Omsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Seoul',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Seoul',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Johns',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Johns',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Inuvik',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Inuvik',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Martinique',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Martinique',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Shanghai',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Shanghai',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dominica',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Dominica',
   'DATA'),
  ('pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Juba',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Juba',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lagos',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lagos',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaSur',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaSur',
   'DATA'),
  ('pytz\\zoneinfo\\Kwajalein',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Manaus',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Manaus',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Busingen',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Busingen',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaNorte',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaNorte',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Abidjan',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Abidjan',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\McMurdo',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\McMurdo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atikokan',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Atikokan',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Enderbury',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Enderbury',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Hobart',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Hobart',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Johnston',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Johnston',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-14',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-14',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Jersey',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Jersey',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-9',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-9',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Marigot',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Marigot',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dili',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dili',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Istanbul',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\St_Helena',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\St_Helena',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tashkent',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tashkent',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yangon',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yangon',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Stockholm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Stockholm',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thule',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Thule',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boise',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Boise',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'DATA'),
  ('pytz\\zoneinfo\\PRC',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\PRC',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Pacific',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\US\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\EasterIsland',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Chile\\EasterIsland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Regina',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Regina',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pohnpei',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pohnpei',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Nicosia',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Universal',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belem',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Belem',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Arizona',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\US\\Arizona',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aden',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aden',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Barnaul',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Barnaul',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novosibirsk',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novosibirsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kashgar',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kashgar',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Bermuda',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Bermuda',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Karachi',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Karachi',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Malta',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Malta',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-7',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-7',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Macquarie',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Macquarie',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Palmer',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Palmer',
   'DATA'),
  ('pytz\\zoneinfo\\W-SU',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\W-SU',
   'DATA'),
  ('pytz\\zoneinfo\\Israel',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Israel',
   'DATA'),
  ('pytz\\zoneinfo\\Libya',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Libya',
   'DATA'),
  ('pytz\\zoneinfo\\America\\El_Salvador',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\El_Salvador',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Swift_Current',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Swift_Current',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-10',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-10',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Buenos_Aires',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tijuana',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Tijuana',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dacca',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dacca',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Urumqi',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Urumqi',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Calcutta',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Calcutta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Managua',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Managua',
   'DATA'),
  ('pytz\\zoneinfo\\GMT-0',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Maldives',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Maldives',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Pacific',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Currie',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Currie',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Easter',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Easter',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santa_Isabel',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santa_Isabel',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lord_Howe',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lord_Howe',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chatham',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chatham',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Madeira',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Madeira',
   'DATA'),
  ('pytz\\zoneinfo\\Eire',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Eire',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Taipei',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Taipei',
   'DATA'),
  ('pytz\\zoneinfo\\EET',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\EET',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimphu',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimphu',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Canberra',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Canberra',
   'DATA'),
  ('pytz\\zoneinfo\\Factory',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Factory',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Scoresbysund',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Scoresbysund',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Syowa',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Syowa',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Luxembourg',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Luxembourg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Damascus',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Damascus',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+1',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+1',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Niamey',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Niamey',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qostanay',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qostanay',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Mountain',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chuuk',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chuuk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Brussels',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Brussels',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maseru',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maseru',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zurich',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zurich',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Dublin',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Dublin',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+5',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+5',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Uzhgorod',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Uzhgorod',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nouakchott',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nouakchott',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Troll',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Troll',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Efate',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Efate',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Davis',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Davis',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Mawson',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Mawson',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Barbados',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Barbados',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baku',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baku',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Cairo',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Cairo',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-1',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-1',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mogadishu',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mogadishu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vientiane',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vientiane',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sarajevo',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sarajevo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Denver',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Denver',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tokyo',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tokyo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Paramaribo',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Paramaribo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtobe',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtobe',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Libreville',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Libreville',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Melbourne',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Melbourne',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('pytz\\zoneinfo\\HST',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\HST',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashgabat',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashgabat',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vienna',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vienna',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Danmarkshavn',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Danmarkshavn',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Samarkand',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Samarkand',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-3',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-3',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Douala',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Douala',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Broken_Hill',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Broken_Hill',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Porto-Novo',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Porto-Novo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dushanbe',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dushanbe',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jerusalem',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jerusalem',
   'DATA'),
  ('pytz\\zoneinfo\\Portugal',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Portugal',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Hermosillo',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Hermosillo',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+2',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+2',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guyana',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guyana',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Metlakatla',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Metlakatla',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Hawaii',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\US\\Hawaii',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Warsaw',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Warsaw',
   'DATA'),
  ('pytz\\zoneinfo\\leapseconds',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\leapseconds',
   'DATA'),
  ('pytz\\zoneinfo\\Poland',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Poland',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Apia',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Apia',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Majuro',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Majuro',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Los_Angeles',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Los_Angeles',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tahiti',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tahiti',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Galapagos',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Galapagos',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Copenhagen',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Copenhagen',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Perth',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Perth',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guadeloupe',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guadeloupe',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Saskatchewan',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Saskatchewan',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+8',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+8',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chita',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chita',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kaliningrad',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kaliningrad',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Gibraltar',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Gibraltar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Adak',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Adak',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Irkutsk',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Irkutsk',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-6',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-6',
   'DATA'),
  ('pytz\\zoneinfo\\WET',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\WET',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mbabane',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mbabane',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lindeman',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lindeman',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\Acre',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\Acre',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Brazzaville',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Brazzaville',
   'DATA'),
  ('pytz\\zoneinfo\\Japan',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Japan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dhaka',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dhaka',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kosrae',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kosrae',
   'DATA'),
  ('pytz\\zoneinfo\\GMT0',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Djibouti',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Djibouti',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Christmas',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Christmas',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belgrade',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belgrade',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macau',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macau',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Marquesas',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Marquesas',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Rangoon',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Rangoon',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Gaza',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Gaza',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tiraspol',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tiraspol',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boa_Vista',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Boa_Vista',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Rarotonga',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Rarotonga',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tongatapu',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tongatapu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sao_Paulo',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Sao_Paulo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yerevan',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yerevan',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Khartoum',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Khartoum',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Matamoros',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Matamoros',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Puerto_Rico',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Puerto_Rico',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Tasmania',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Tasmania',
   'DATA'),
  ('pytz\\zoneinfo\\Turkey',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Turkey',
   'DATA'),
  ('pytz\\zoneinfo\\GMT',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Khandyga',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Khandyga',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hong_Kong',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hong_Kong',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kathmandu',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kathmandu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Aruba',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Aruba',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Asuncion',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Asuncion',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Atlantic',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Atlantic',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Panama',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Panama',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vaduz',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vaduz',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yellowknife',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Yellowknife',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Glace_Bay',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Glace_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fiji',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fiji',
   'DATA'),
  ('pytz\\zoneinfo\\UCT',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Norfolk',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Norfolk',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-12',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-12',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Azores',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Azores',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fakaofo',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fakaofo',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Gaborone',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Gaborone',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Curacao',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Curacao',
   'DATA'),
  ('pytz\\zoneinfo\\MST',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\MST',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Katmandu',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Katmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nairobi',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nairobi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montevideo',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Montevideo',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Bougainville',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Bougainville',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fortaleza',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Fortaleza',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\Continental',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Chile\\Continental',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Central',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Guernsey',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Guernsey',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Nicosia',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mauritius',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mauritius',
   'DATA'),
  ('pytz\\zoneinfo\\EST5EDT',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\EST5EDT',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Timbuktu',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Timbuktu',
   'DATA'),
  ('pytz\\zoneinfo\\PST8PDT',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\PST8PDT',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kigali',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kigali',
   'DATA'),
  ('pytz\\zoneinfo\\Zulu',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Funafuti',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Funafuti',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Saipan',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Saipan',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faeroe',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faeroe',
   'DATA'),
  ('pytz\\zoneinfo\\NZ',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\zoneinfo\\NZ',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wallis',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wallis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cambridge_Bay',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cambridge_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coral_Harbour',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Coral_Harbour',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Catamarca',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lome',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lome',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bahrain',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bahrain',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Blanc-Sablon',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Blanc-Sablon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Menominee',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Menominee',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Noumea',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Noumea',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Iqaluit',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Iqaluit',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Muscat',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Muscat',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\General',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Mexico\\General',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Cocos',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Cocos',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ndjamena',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ndjamena',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hebron',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hebron',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ojinaga',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pytz\\zoneinfo\\America\\Ojinaga',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_table.tpl',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\io\\formats\\templates\\latex_table.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\string.tpl',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\io\\formats\\templates\\string.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex.tpl',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\io\\formats\\templates\\latex.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html.tpl',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\io\\formats\\templates\\html.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_table.tpl',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\io\\formats\\templates\\html_table.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_style.tpl',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\io\\formats\\templates\\html_style.tpl',
   'DATA'),
  ('jaraco\\text\\Lorem ipsum.txt',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('importlib_metadata-8.5.0.dist-info\\LICENSE',
   'c:\\program '
   'files\\python\\lib\\site-packages\\importlib_metadata-8.5.0.dist-info\\LICENSE',
   'DATA'),
  ('importlib_metadata-8.5.0.dist-info\\METADATA',
   'c:\\program '
   'files\\python\\lib\\site-packages\\importlib_metadata-8.5.0.dist-info\\METADATA',
   'DATA'),
  ('importlib_metadata-8.5.0.dist-info\\WHEEL',
   'c:\\program '
   'files\\python\\lib\\site-packages\\importlib_metadata-8.5.0.dist-info\\WHEEL',
   'DATA'),
  ('importlib_metadata-8.5.0.dist-info\\INSTALLER',
   'c:\\program '
   'files\\python\\lib\\site-packages\\importlib_metadata-8.5.0.dist-info\\INSTALLER',
   'DATA'),
  ('importlib_metadata-8.5.0.dist-info\\RECORD',
   'c:\\program '
   'files\\python\\lib\\site-packages\\importlib_metadata-8.5.0.dist-info\\RECORD',
   'DATA'),
  ('importlib_metadata-8.5.0.dist-info\\top_level.txt',
   'c:\\program '
   'files\\python\\lib\\site-packages\\importlib_metadata-8.5.0.dist-info\\top_level.txt',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvl8a.afm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvl8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect_large.png',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncri8a.afm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncri8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Bold.afm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Bold.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8.mplstyle',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Italic.ttf',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Italic.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-BoldItalic.afm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-BoldItalic.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave.svg',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-colorblind.mplstyle',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-colorblind.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options.png',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmsy10.ttf',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmsy10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBol.ttf',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move.pdf',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\move.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplri8a.afm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplri8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back.svg',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\back.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\readme.txt',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\readme.txt',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmr10.ttf',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmr10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkli8a.afm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkli8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-dark.mplstyle',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-dark.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-poster.mplstyle',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-poster.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmri8a.afm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmri8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmb8a.afm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward.svg',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\forward.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica.afm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Oblique.ttf',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Oblique.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniIta.ttf',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniIta.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans.ttf',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_STIX',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_STIX',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvb8an.afm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvb8an.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\fast.mplstyle',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\fast.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmr8a.afm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncbi8a.afm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncbi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymReg.ttf',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help.pdf',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\help.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect.svg',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-dark-palette.mplstyle',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-dark-palette.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move.png',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\move.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putb8a.afm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home.png',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\home.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home.svg',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\home.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Roman.afm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Roman.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back.png',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\back.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrbo8a.afm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrbo8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\ZapfDingbats.afm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\ZapfDingbats.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-muted.mplstyle',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-muted.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\grace_hopper.jpg',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\grace_hopper.jpg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagdo8a.afm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagdo8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Oblique.afm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Oblique.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Bold.ttf',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Bold.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif.ttf',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmr10.afm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmr10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help.svg',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\help.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymBol.ttf',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putr8a.afm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvr8an.afm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvr8an.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymBol.ttf',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Symbol.afm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Symbol.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncr8a.afm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help-symbolic.svg',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\help-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmss10.ttf',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmss10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\eeg.dat',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\eeg.dat',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave_large.png',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\plot_directive\\plot_directive.css',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\plot_directive\\plot_directive.css',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFiveSymReg.ttf',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFiveSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkl8a.afm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkl8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Oblique.ttf',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Oblique.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-BoldOblique.ttf',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-BoldOblique.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pzdr.afm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pzdr.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pzcmi8a.afm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pzcmi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots_large.png',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\dark_background.mplstyle',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\dark_background.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagk8a.afm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagk8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options.svg',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-deep.mplstyle',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-deep.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect.png',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrro8a.afm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrro8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options_large.png',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\classic.mplstyle',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\classic.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib.pdf',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward.pdf',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\forward.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move_large.png',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\move_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneral.ttf',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneral.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\topobathy.npz',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\topobathy.npz',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymReg.ttf',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmmi10.afm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmmi10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Bold.afm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Bold.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\msft.csv',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\msft.csv',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home_large.png',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\home_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansDisplay.ttf',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansDisplay.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-BoldOblique.afm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-BoldOblique.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward-symbolic.svg',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\forward-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmex10.ttf',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmex10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-paper.mplstyle',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-paper.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\tableau-colorblind10.mplstyle',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\tableau-colorblind10.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono.ttf',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrr8a.afm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkdi8a.afm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkdi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\percent_bachelors_degrees_women_usa.csv',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\percent_bachelors_degrees_women_usa.csv',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\Solarize_Light2.mplstyle',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\Solarize_Light2.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-BoldOblique.ttf',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-BoldOblique.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBol.ttf',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-whitegrid.mplstyle',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-whitegrid.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots.png',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots.png',
   'DATA'),
  ('matplotlib\\mpl-data\\matplotlibrc',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\matplotlibrc',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move.svg',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\move.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putri8a.afm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putri8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBolIta.ttf',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBolIta.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagko8a.afm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagko8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrb8a.afm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvbo8a.afm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvbo8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvr8a.afm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib.svg',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmmi10.ttf',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmmi10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagd8a.afm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagd8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\membrane.dat',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\membrane.dat',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBolIta.ttf',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBolIta.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back_large.png',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\back_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmtt10.afm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmtt10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-white.mplstyle',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-white.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots-symbolic.svg',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave-symbolic.svg',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Bold.afm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Bold.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib_large.png',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvro8an.afm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvro8an.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-pastel.mplstyle',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-pastel.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmb10.ttf',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmb10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvbo8an.afm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvbo8an.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect-symbolic.svg',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-bright.mplstyle',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-bright.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkd8a.afm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkd8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmex10.afm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmex10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots.svg',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncb8a.afm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Oblique.afm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Oblique.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Bold.ttf',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Bold.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\jacksboro_fault_dem.npz',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\jacksboro_fault_dem.npz',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\fivethirtyeight.mplstyle',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\fivethirtyeight.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\grayscale.mplstyle',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\grayscale.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\goog.npz',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\goog.npz',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\_classic_test_patch.mplstyle',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\_classic_test_patch.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\s1045.ima.gz',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\s1045.ima.gz',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home.pdf',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\home.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\data_x_x2_x3.csv',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\data_x_x2_x3.csv',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\logo2.png',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\logo2.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward.png',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\forward.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\hand.png',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\hand.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect.pdf',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvro8a.afm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvro8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_DEJAVU',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_DEJAVU',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvb8a.afm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots.pdf',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\embedding_in_wx3.xrc',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\embedding_in_wx3.xrc',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmtt10.ttf',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmtt10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-BoldItalic.ttf',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-BoldItalic.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvlo8a.afm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvlo8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\psyr.afm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\psyr.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymBol.ttf',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\hand.pdf',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\hand.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help_large.png',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\help_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home-symbolic.svg',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\home-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options.pdf',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralItalic.ttf',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralItalic.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave.pdf',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmsy10.afm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmsy10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move-symbolic.svg',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\move-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\kpsewhich.lua',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\kpsewhich.lua',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-BoldOblique.afm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-BoldOblique.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-notebook.mplstyle',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-notebook.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplb8a.afm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplr8a.afm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier.afm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\Minduka_Present_Blue_Pack.png',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\Minduka_Present_Blue_Pack.png',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\_mpl-gallery-nogrid.mplstyle',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\_mpl-gallery-nogrid.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\Stocks.csv',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\Stocks.csv',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymReg.ttf',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\ggplot.mplstyle',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\ggplot.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-darkgrid.mplstyle',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-darkgrid.mplstyle',
   'DATA'),
  ('matplotlib.libs\\.load-order-matplotlib-3.7.5',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib.libs\\.load-order-matplotlib-3.7.5',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymBol.ttf',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\hand.svg',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\hand.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplbi8a.afm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplbi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\bmh.mplstyle',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\bmh.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\_mpl-gallery.mplstyle',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\_mpl-gallery.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave.png',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave.png',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-ticks.mplstyle',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-ticks.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUni.ttf',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUni.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putbi8a.afm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putbi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymReg.ttf',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back.pdf',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\back.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back-symbolic.svg',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\back-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-talk.mplstyle',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-talk.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\axes_grid\\bivariate_normal.npy',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\axes_grid\\bivariate_normal.npy',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerifDisplay.ttf',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerifDisplay.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help.png',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\help.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmbi8a.afm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmbi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Italic.afm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Italic.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward_large.png',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\forward_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Bold.ttf',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Bold.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\README.txt',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\README.txt',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib.png',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib.png',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'c:\\program '
   'files\\python\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'c:\\program '
   'files\\python\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'c:\\program '
   'files\\python\\lib\\site-packages\\lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'c:\\program '
   'files\\python\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'c:\\program '
   'files\\python\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'c:\\program '
   'files\\python\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'c:\\program '
   'files\\python\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'c:\\program '
   'files\\python\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'c:\\program '
   'files\\python\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kosrae',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Famagusta',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port_of_Spain',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Riyadh',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('_tk_data\\ttk\\notebook.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('_tk_data\\msgs\\el.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Maceio',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaSur',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grenada',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('_tcl_data\\msgs\\ta_in.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\HST10',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-jp.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+9',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Blantyre',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guayaquil',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('_tcl_data\\msgs\\es_cr.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp949.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Noumea',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('_tcl_data\\msgs\\fa_in.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('_tcl_data\\clock.tcl',
   'c:\\program files\\python\\tcl\\tcl8.6\\clock.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Saratov',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macao',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('_tcl_data\\msgs\\fo_fo.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\NZ',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashgabat',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-13',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Zulu',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Hongkong',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kamchatka',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('_tcl_data\\tzdata\\Egypt',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Egypt',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Katmandu',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('_tk_data\\ttk\\vistaTheme.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-7',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Marengo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('_tcl_data\\msgs\\ga.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ga.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayenne',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Astrakhan',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Amsterdam',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Ushuaia',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('_tcl_data\\msgs\\eu.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\eu.msg',
   'DATA'),
  ('_tcl_data\\encoding\\gb1988.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp866.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\East',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaNorte',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kashgar',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tegucigalpa',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Costa_Rica',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('_tcl_data\\msgs\\en_gb.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vaduz',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tortola',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Simferopol',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Timbuktu',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('_tcl_data\\encoding\\cp865.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('_tk_data\\ttk\\ttk.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\ttk.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tomsk',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('_tcl_data\\msgs\\cs.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tk_data\\listbox.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\listbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Malabo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-6.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulaanbaatar',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\McMurdo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('_tk_data\\ttk\\classicTheme.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Vincent',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Cordoba',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('_tcl_data\\msgs\\lv.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\lv.msg',
   'DATA'),
  ('_tk_data\\msgs\\eo.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tk_data\\console.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\console.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\EST5EDT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Oral',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grand_Turk',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('_tcl_data\\msgs\\id_id.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novokuznetsk',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mexico_City',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT0',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\South_Georgia',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Antananarivo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayman',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('_tk_data\\msgs\\da.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\EET',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\EET',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bratislava',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('_tcl_data\\msgs\\mr.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\mr.msg',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo.eps',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Uzhgorod',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UTC',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('_tcl_data\\tzdata\\Zulu',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kigali',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('_tcl_data\\msgs\\af.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\af.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+0',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Louisville',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Queensland',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Accra',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cambridge_Bay',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dakar',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('tcl8\\8.5\\msgcat-1.6.1.tm',
   'c:\\program files\\python\\tcl\\tcl8\\8.5\\msgcat-1.6.1.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Caracas',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Havana',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('_tcl_data\\msgs\\zh_cn.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-9.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Moscow',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Isle_of_Man',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Catamarca',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('_tcl_data\\msgs\\gl_es.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tallinn',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT-0',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\GMT-0',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Karachi',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guyana',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('_tcl_data\\msgs\\sh.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\sh.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Luanda',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('_tcl_data\\tzdata\\Cuba',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Cuba',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rosario',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('_tcl_data\\encoding\\ascii.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('_tk_data\\tearoff.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\tearoff.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuala_Lumpur',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Djibouti',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baghdad',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atikokan',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('_tk_data\\ttk\\altTheme.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sarajevo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('_tcl_data\\tzdata\\Kwajalein',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('_tcl_data\\msgs\\es_gt.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Central',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('_tcl_data\\encoding\\macRomania.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Galapagos',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jujuy',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Acre',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nome',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('_tcl_data\\msgs\\en_ca.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ko.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ko.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ojinaga',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('_tk_data\\comdlg.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\comdlg.tcl',
   'DATA'),
  ('_tk_data\\msgs\\fr.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mahe',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('_tcl_data\\encoding\\cp1257.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tongatapu',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('_tk_data\\images\\logo100.gif',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('_tcl_data\\msgs\\he.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\he.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-10',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Windhoek',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('_tcl_data\\tzdata\\Singapore',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('_tcl_data\\msgs\\es_ni.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+7',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('_tcl_data\\encoding\\cp775.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\WET',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\WET',
   'DATA'),
  ('_tcl_data\\msgs\\de_at.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Regina',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('_tcl_data\\msgs\\el.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montserrat',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\La_Paz',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Managua',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('_tk_data\\ttk\\utils.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\utils.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Mariehamn',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('_tcl_data\\msgs\\ar_sy.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_sv.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('_tk_data\\images\\logoMed.gif',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Port_Moresby',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tahiti',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-5.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('_tk_data\\tk.tcl', 'c:\\program files\\python\\tcl\\tk8.6\\tk.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Niue',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pontianak',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('_tk_data\\images\\logo.eps',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dhaka',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\NSW',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Menominee',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kaliningrad',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sitka',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Araguaina',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dacca',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('_tk_data\\dialog.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\dialog.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\euc-jp.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('_tk_data\\choosedir.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\choosedir.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Truk',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('_tcl_data\\tzdata\\Greenwich',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ljubljana',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vienna',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('_tk_data\\msgs\\it.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp857.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('_tcl_data\\opt0.4\\pkgIndex.tcl',
   'c:\\program files\\python\\tcl\\tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo100.gif',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('_tcl_data\\msgs\\ar_lb.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9YDT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wallis',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Urumqi',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('_tk_data\\bgerror.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\bgerror.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bissau',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vatican',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('_tcl_data\\msgs\\mk.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\mk.msg',
   'DATA'),
  ('_tk_data\\ttk\\scrollbar.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Hobart',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('_tk_data\\clrpick.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\clrpick.tcl',
   'DATA'),
  ('_tk_data\\ttk\\menubutton.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ-CHAT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zagreb',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('_tk_data\\scrlbar.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\scrlbar.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Atlantic',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chungking',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('_tcl_data\\msgs\\hi_in.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ms_my.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macCroatian.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Luxembourg',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bahrain',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('_tcl_data\\msgs\\kl.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\kl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\zh_sg.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp936.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montreal',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('_tcl_data\\msgs\\sk.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\sk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-12',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('_tcl_data\\msgs\\es_ar.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\DumontDUrville',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lagos',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nouakchott',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Omsk',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('_tcl_data\\encoding\\euc-cn.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cancun',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Buenos_Aires',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qatar',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zurich',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kampala',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('_tk_data\\images\\logo64.gif',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Manila',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Adelaide',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-kr.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Srednekolymsk',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bogota',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sao_Paulo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Miquelon',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('_tk_data\\ttk\\sizegrip.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Phnom_Penh',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('tcl8\\8.4\\platform-1.0.14.tm',
   'c:\\program files\\python\\tcl\\tcl8\\8.4\\platform-1.0.14.tm',
   'DATA'),
  ('_tcl_data\\msgs\\zh.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\zh.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\EasterIsland',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('_tcl_data\\encoding\\cp850.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Coral_Harbour',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('_tk_data\\icons.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\icons.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\es.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Harare',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('_tk_data\\msgs\\nl.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nipigon',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Funafuti',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Andorra',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yellowknife',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lubumbashi',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Rothera',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Copenhagen',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boa_Vista',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('_tcl_data\\msgs\\hr.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\hr.msg',
   'DATA'),
  ('_tk_data\\ttk\\scale.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\scale.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Podgorica',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Stockholm',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Indianapolis',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pago_Pago',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Halifax',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Skopje',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Shiprock',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Barthelemy',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Irkutsk',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tripoli',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Newfoundland',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('_tcl_data\\msgs\\ru.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Universal',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('_tcl_data\\msgs\\uk.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\uk.msg',
   'DATA'),
  ('_tcl_data\\msgs\\de_be.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('_tcl_data\\msgs\\hi.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\hi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Yancowinna',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('_tcl_data\\encoding\\cp861.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'c:\\program files\\python\\tcl\\tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Monterrey',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Istanbul',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Ponape',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('_tcl_data\\msgs\\es_ec.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novosibirsk',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Mountain',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Warsaw',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('_tcl_data\\msgs\\kok.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\kok.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yangon',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santa_Isabel',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('_tcl_data\\encoding\\gb12345.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('_tk_data\\text.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\text.tcl',
   'DATA'),
  ('_tcl_data\\tm.tcl',
   'c:\\program files\\python\\tcl\\tcl8.6\\tm.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qyzylorda',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Center',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('_tk_data\\msgs\\en.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\en.msg',
   'DATA'),
  ('_tk_data\\images\\tai-ku.gif',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\tai-ku.gif',
   'DATA'),
  ('_tcl_data\\msgs\\eo.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kathmandu',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Monticello',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Danmarkshavn',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('_tcl_data\\msgs\\be.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Abidjan',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Wayne',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('_tk_data\\msgs\\pt.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+3',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Luis',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bujumbura',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('_tcl_data\\msgs\\es_mx.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('_tk_data\\panedwindow.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\ROC',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\ROC',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Yukon',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('_tk_data\\ttk\\treeview.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Detroit',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Calcutta',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tashkent',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mazatlan',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Juan',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('_tcl_data\\tzdata\\Eire',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Eire',
   'DATA'),
  ('_tk_data\\ttk\\defaults.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Barbados',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vladivostok',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Eirunepe',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo150.gif',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\El_Aaiun',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nairobi',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lower_Princes',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Taipei',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('_tcl_data\\msgs\\es_do.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_cl.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Virgin',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('_tcl_data\\msgs\\en_be.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_ie.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maputo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ensenada',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('_tcl_data\\msgs\\ta.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ta.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-9',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-1.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('_tk_data\\ttk\\progress.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Auckland',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Alaska',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santiago',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuwait',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuching',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kiritimati',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Oslo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('_tk_data\\palette.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\palette.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\kw_gb.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Velho',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('_tcl_data\\msgs\\sq.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\sq.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faroe',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Los_Angeles',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yekaterinburg',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Colombo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Gambier',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('_tcl_data\\msgs\\mt.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\mt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6CDT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Minsk',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rainy_River',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Conakry',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Asuncion',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('_tcl_data\\tclIndex',
   'c:\\program files\\python\\tcl\\tcl8.6\\tclIndex',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Casey',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('_tcl_data\\msgs\\ro.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ro.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kwajalein',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Michigan',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\GMT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Creston',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+1',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Saipan',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Saskatchewan',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('_tcl_data\\msgs\\fo.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\fo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macau',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Macquarie',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('_tcl_data\\encoding\\macJapan.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo75.gif',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sofia',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Banjul',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Whitehorse',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('_tcl_data\\msgs\\de.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8PDT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('_tk_data\\ttk\\cursors.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\macUkraine.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Manaus',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Tucuman',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Salta',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('_tk_data\\ttk\\clamTheme.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\kl_gl.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\pl.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tk_data\\entry.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\entry.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\West',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('_tcl_data\\encoding\\cp1255.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo175.gif',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('_tk_data\\msgs\\ru.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Palau',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vevay',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jerusalem',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('_tcl_data\\tzdata\\EST',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\EST',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vilnius',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('_tcl_data\\tzdata\\Portugal',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('_tk_data\\msgs\\pl.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Brunei',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\East-Indiana',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('_tcl_data\\encoding\\macRoman.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('_tk_data\\ttk\\panedwindow.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Honolulu',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('_tcl_data\\encoding\\cp869.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Nauru',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('_tcl_data\\msgs\\kw.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\kw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Atyrau',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('_tcl_data\\tzdata\\Iran',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Iran',
   'DATA'),
  ('_tcl_data\\msgs\\es_bo.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-u.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\Continental',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('_tcl_data\\msgs\\ru_ua.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('_tcl_data\\msgs\\tr.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\tr.msg',
   'DATA'),
  ('_tcl_data\\msgs\\bn.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\bn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('_tcl_data\\encoding\\cp437.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\Acre',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Tell_City',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Johnston',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('_tcl_data\\encoding\\cp874.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Guernsey',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('_tcl_data\\tzdata\\Israel',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Israel',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kabul',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hebron',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Merida',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Thomas',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('_tcl_data\\encoding\\cp1253.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('_tk_data\\ttk\\aquaTheme.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Libya',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Libya',
   'DATA'),
  ('_tcl_data\\msgs\\gv_gb.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Broken_Hill',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chihuahua',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Knox_IN',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('_tcl_data\\encoding\\cp1258.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('_tk_data\\msgs\\es.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Toronto',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Gibraltar',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('_tcl_data\\msgs\\bn_in.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('_tcl_data\\msgs\\fa_ir.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ms.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ms.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Comoro',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Krasnoyarsk',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ho_Chi_Minh',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Mountain',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dubai',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baku',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tbilisi',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('_tk_data\\ttk\\combobox.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-0',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('_tcl_data\\msgs\\en_sg.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('_tk_data\\pkgIndex.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Jamaica',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('_tcl_data\\msgs\\nn.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\nn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belgrade',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guam',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Bougainville',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('_tk_data\\msgs\\en_gb.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tiraspol',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Darwin',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Bermuda',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Aruba',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kinshasa',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('_tk_data\\obsolete.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\obsolete.tcl',
   'DATA'),
  ('_tk_data\\unsupported.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\unsupported.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mendoza',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lindeman',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Saigon',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('_tcl_data\\opt0.4\\optparse.tcl',
   'c:\\program files\\python\\tcl\\tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Azores',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jayapura',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('_tk_data\\license.terms',
   'c:\\program files\\python\\tcl\\tk8.6\\license.terms',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Prague',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('_tcl_data\\package.tcl',
   'c:\\program files\\python\\tcl\\tcl8.6\\package.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+12',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312-raw.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Midway',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belem',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anguilla',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Rome',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Knox',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('_tk_data\\ttk\\spinbox.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Helsinki',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Cape_Verde',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Maldives',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Cocos',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Christmas',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('_tcl_data\\msgs\\kok_in.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1256.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cuiaba',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('_tk_data\\ttk\\button.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\button.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Iceland',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wake',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\London',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Athens',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\La_Rioja',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('_tcl_data\\encoding\\macCyrillic.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+11',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rio_Branco',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('_tk_data\\ttk\\xpTheme.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('_tcl_data\\word.tcl',
   'c:\\program files\\python\\tcl\\tcl8.6\\word.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\North',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Moncton',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-3',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chongqing',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('_tcl_data\\msgs\\af_za.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bangui',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('_tcl_data\\msgs\\nb.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\nb.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macGreek.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_hn.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('_tcl_data\\msgs\\gl.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\gl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pohnpei',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Greenwich',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kralendijk',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Metlakatla',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nassau',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('_tcl_data\\encoding\\ksc5601.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hovd',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Efate',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Mendoza',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('_tcl_data\\msgs\\ga_ie.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kolkata',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fiji',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Panama',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('_tcl_data\\msgs\\ca.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ca.msg',
   'DATA'),
  ('_tcl_data\\msgs\\zh_hk.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\UCT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\UCT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Anadyr',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Paris',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+4',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Nicosia',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Brisbane',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Noronha',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lima',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Matamoros',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tehran',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('_tcl_data\\msgs\\sv.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Chagos',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('_tk_data\\focus.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\focus.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\ko_kr.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Jersey',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Cairo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('_tcl_data\\msgs\\te_in.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Volgograd',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Melbourne',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('_tcl_data\\msgs\\zh_tw.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('_tk_data\\msgs\\cs.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fortaleza',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Madeira',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('_tk_data\\images\\logoLarge.gif',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Martinique',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Juba',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Resolute',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anchorage',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+10',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Curacao',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+8',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('_tcl_data\\msgs\\mr_in.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Monaco',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\South',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('_tcl_data\\encoding\\macIceland.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('_tk_data\\megawidget.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\megawidget.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\macDingbats.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Canary',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Dublin',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\St_Helena',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('_tcl_data\\msgs\\en_in.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1252.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vincennes',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Singapore',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-2.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('_tk_data\\mkpsenc.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\mkpsenc.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Eastern',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Kitts',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('_tcl_data\\encoding\\cp862.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp932.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Berlin',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('_tcl_data\\msgs\\pt.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific-New',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\US\\Pacific-New',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Eucla',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('_tk_data\\msgs\\hu.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+2',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('_tk_data\\ttk\\entry.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\entry.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Edmonton',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('_tcl_data\\msgs\\ja.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ja.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Lisbon',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Makassar',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santarem',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Addis_Ababa',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ndjamena',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('_tcl_data\\msgs\\es_pa.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Petersburg',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('_tcl_data\\msgs\\ar_in.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Marquesas',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Beulah',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('_tcl_data\\encoding\\cp855.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_pr.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Palmer',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Central',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yerevan',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Goose_Bay',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('_tk_data\\iconlist.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\iconlist.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Juneau',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('_tcl_data\\encoding\\symbol.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belize',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('_tcl_data\\msgs\\ar.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ar.msg',
   'DATA'),
  ('_tcl_data\\encoding\\jis0212.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('_tk_data\\xmfbox.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\xmfbox.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\en_au.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-4.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chicago',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\South_Pole',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guatemala',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('_tcl_data\\msgs\\fa.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\fa.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-14',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-8',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guadalcanal',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('_tcl_data\\msgs\\is.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\is.msg',
   'DATA'),
  ('_tcl_data\\history.tcl',
   'c:\\program files\\python\\tcl\\tcl8.6\\history.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kiev',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-5',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Davis',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('_tk_data\\fontchooser.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\fontchooser.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-6',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson_Creek',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('_tcl_data\\encoding\\cp860.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5EDT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Campo_Grande',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Samara',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Gaza',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-2',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Samoa',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dominica',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indianapolis',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Inuvik',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('_tk_data\\ttk\\fonts.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\fonts.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\LHI',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Phoenix',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cordoba',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Mawson',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4ADT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Istanbul',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('_tcl_data\\msgs\\fr.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zaporozhye',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Aleutian',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kirov',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Louisville',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lusaka',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ust-Nera',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Sakhalin',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Winnipeg',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('_tcl_data\\msgs\\id.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\id.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\PST8PDT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fakaofo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Almaty',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Marigot',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('_tcl_data\\encoding\\cp852.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jakarta',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('_tcl_data\\tzdata\\Japan',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Japan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rankin_Inlet',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Victoria',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Lucia',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Riga',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thule',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Jujuy',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bangkok',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Casablanca',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('_tk_data\\menu.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\menu.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\HST',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\HST',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Monrovia',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Libreville',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('_tcl_data\\encoding\\macThai.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('_tk_data\\tclIndex',
   'c:\\program files\\python\\tcl\\tk8.6\\tclIndex',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtau',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Universal',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tirane',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('_tk_data\\optMenu.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\optMenu.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp1251.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Antigua',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashkhabad',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Canberra',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Samarkand',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faeroe',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('_tcl_data\\msgs\\it.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vientiane',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('_tcl_data\\encoding\\macCentEuro.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Chisinau',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('_tk_data\\scale.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\scale.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Kerguelen',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Currie',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Amman',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT+0',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\GMT+0',
   'DATA'),
  ('_tcl_data\\encoding\\cp737.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-r.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mauritius',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('_tk_data\\images\\README',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\README',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Muscat',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Puerto_Rico',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tunis',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hong_Kong',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('_tcl_data\\msgs\\ar_jo.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Tasmania',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('_tcl_data\\encoding\\tis-620.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\tis-620.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\West',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('_tcl_data\\tzdata\\CET',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\CET',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Brazzaville',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mayotte',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Rarotonga',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('_tcl_data\\encoding\\jis0208.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-8.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lord_Howe',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Vostok',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('_tk_data\\ttk\\winTheme.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\da.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp950.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp1254.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montevideo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jamaica',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('_tcl_data\\http1.0\\http.tcl',
   'c:\\program files\\python\\tcl\\tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tel_Aviv',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yakutat',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('_tcl_data\\encoding\\cp863.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7MDT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ceuta',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-16.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Reykjavik',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Arizona',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('_tcl_data\\msgs\\sr.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\sr.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1250.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('_tk_data\\msgs\\sv.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Perth',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dili',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Porto-Novo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Douala',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('_tcl_data\\tzdata\\MET',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\MET',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Beirut',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Shanghai',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Norfolk',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pitcairn',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Buenos_Aires',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santo_Domingo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Scoresbysund',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Troll',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('_tcl_data\\msgs\\en_bw.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('_tcl_data\\http1.0\\pkgIndex.tcl',
   'c:\\program files\\python\\tcl\\tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Seoul',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulan_Bator',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boise',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\ACT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Johannesburg',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mogadishu',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('_tcl_data\\tzdata\\MST7MDT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo200.gif',
   'c:\\program files\\python\\tcl\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\New_York',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Hawaii',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-1',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-15.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Winamac',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-3.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('_tcl_data\\auto.tcl',
   'c:\\program files\\python\\tcl\\tcl8.6\\auto.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT0',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\GMT0',
   'DATA'),
  ('_tcl_data\\msgs\\th.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\th.msg',
   'DATA'),
  ('_tcl_data\\msgs\\nl_be.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('_tcl_data\\msgs\\eu_es.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('_tcl_data\\encoding\\big5.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atka',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bucharest',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('_tcl_data\\init.tcl',
   'c:\\program files\\python\\tcl\\tcl8.6\\init.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-4',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Syowa',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('_tcl_data\\encoding\\shiftjis.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thunder_Bay',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Vancouver',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('_tk_data\\msgs\\de.msg',
   'c:\\program files\\python\\tcl\\tk8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chatham',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Khandyga',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Catamarca',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimphu',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Hermosillo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\San_Marino',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('_tcl_data\\tzdata\\GB',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\GB',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Harbin',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Eastern',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('_tcl_data\\msgs\\en_nz.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Yap',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Madrid',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dushanbe',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bishkek',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('_tcl_data\\msgs\\en_zw.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port-au-Prince',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('_tcl_data\\tzdata\\UTC',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\UTC',
   'DATA'),
  ('_tcl_data\\tzdata\\Navajo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Navajo',
   'DATA'),
  ('_tcl_data\\msgs\\es_pe.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Iqaluit',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('_tcl_data\\msgs\\en_za.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-11',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Reunion',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maseru',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\New_Salem',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('tcl8\\8.6\\http-2.9.0.tm',
   'c:\\program files\\python\\tcl\\tcl8\\8.6\\http-2.9.0.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UCT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('_tcl_data\\msgs\\es_ve.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia_Banderas',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('_tcl_data\\tzdata\\CST6CDT',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Glace_Bay',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chita',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Sao_Tome',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('_tcl_data\\msgs\\te.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\te.msg',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ch.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_co.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('_tk_data\\spinbox.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\East-Saskatchewan',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Canada\\East-Saskatchewan',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Indiana-Starke',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Pangnirtung',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-10.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macTurkish.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+5',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Easter',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Pacific',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Godthab',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('_tcl_data\\encoding\\ebcdic.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aden',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('_tcl_data\\msgs\\fr_be.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_uy.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Nicosia',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Nelson',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Barnaul',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('_tcl_data\\msgs\\bg.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\bg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mbabane',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Samoa',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ujung_Pandang',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('_tk_data\\safetk.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\safetk.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Khartoum',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('_tcl_data\\msgs\\gv.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\gv.msg',
   'DATA'),
  ('_tcl_data\\msgs\\it_ch.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtobe',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('_tcl_data\\encoding\\euc-kr.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-7.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('_tcl_data\\msgs\\en_hk.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yakutsk',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Niamey',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dar_es_Salaam',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('_tk_data\\msgbox.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\msgbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Magadan',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Blanc-Sablon',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('_tcl_data\\msgs\\es_py.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('_tcl_data\\msgs\\pt_br.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('_tk_data\\tkfbox.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\tkfbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Johns',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('_tcl_data\\msgs\\sw.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\sw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmara',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Paramaribo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Denver',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('tcl8\\8.5\\tcltest-2.5.0.tm',
   'c:\\program files\\python\\tcl\\tcl8\\8.5\\tcltest-2.5.0.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Arctic\\Longyearbyen',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Gaborone',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Sydney',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tokyo',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\DeNoronha',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Algiers',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('_tcl_data\\tzdata\\W-SU',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\W-SU',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimbu',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guadeloupe',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('_tcl_data\\encoding\\dingbats.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Busingen',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('_tcl_data\\msgs\\lt.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\lt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chuuk',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('_tcl_data\\safe.tcl',
   'c:\\program files\\python\\tcl\\tcl8.6\\safe.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Jan_Mayen',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Rangoon',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('_tcl_data\\msgs\\vi.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\vi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Poland',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Poland',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ouagadougou',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Stanley',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lome',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\El_Salvador',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\ComodRivadavia',
   'c:\\program '
   'files\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('_tcl_data\\tzdata\\GB-Eire',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tarawa',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pyongyang',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('_tcl_data\\msgs\\nl.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp864.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\General',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('_tcl_data\\msgs\\fi.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\fi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Adak',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('_tcl_data\\msgs\\sl.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\sl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Turkey',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Turkey',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belfast',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ca.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Budapest',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Enderbury',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('_tcl_data\\msgs\\en_ph.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Recife',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+6',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Malta',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('_tcl_data\\tzdata\\PRC',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\PRC',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bamako',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ulyanovsk',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Punta_Arenas',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Freetown',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Brussels',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Damascus',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('_tcl_data\\msgs\\hu.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tijuana',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('_tcl_data\\msgs\\et.msg',
   'c:\\program files\\python\\tcl\\tcl8.6\\msgs\\et.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-13.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-14.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('_tk_data\\button.tcl',
   'c:\\program files\\python\\tcl\\tk8.6\\button.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\ROK',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\ROK',
   'DATA'),
  ('_tcl_data\\parray.tcl',
   'c:\\program files\\python\\tcl\\tcl8.6\\parray.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Swift_Current',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('_tcl_data\\encoding\\jis0201.enc',
   'c:\\program files\\python\\tcl\\tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Apia',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Majuro',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('_tcl_data\\tzdata\\MST',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\MST',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Choibalsan',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmera',
   'c:\\program files\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('setuptools\\_vendor\\typeguard-4.3.0.dist-info\\INSTALLER',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\typeguard-4.3.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\typeguard-4.3.0.dist-info\\entry_points.txt',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\typeguard-4.3.0.dist-info\\entry_points.txt',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.43.0.dist-info\\REQUESTED',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\wheel-0.43.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\typeguard-4.3.0.dist-info\\RECORD',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\typeguard-4.3.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\typeguard-4.3.0.dist-info\\LICENSE',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\typeguard-4.3.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\typeguard-4.3.0.dist-info\\METADATA',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\typeguard-4.3.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.43.0.dist-info\\INSTALLER',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\wheel-0.43.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.43.0.dist-info\\entry_points.txt',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\wheel-0.43.0.dist-info\\entry_points.txt',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.43.0.dist-info\\LICENSE.txt',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\wheel-0.43.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('setuptools\\_vendor\\typeguard-4.3.0.dist-info\\top_level.txt',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\typeguard-4.3.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\typeguard-4.3.0.dist-info\\WHEEL',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\typeguard-4.3.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.43.0.dist-info\\METADATA',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\wheel-0.43.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.43.0.dist-info\\RECORD',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\wheel-0.43.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.43.0.dist-info\\WHEEL',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\wheel-0.43.0.dist-info\\WHEEL',
   'DATA'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\Desktop\\yl\\build\\MyApp\\base_library.zip',
   'DATA')],
 'python38.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)
