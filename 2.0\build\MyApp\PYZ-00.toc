('C:\\Users\\<USER>\\Desktop\\yl\\2.0\\build\\MyApp\\PYZ-00.pyz',
 [('MySQLdb',
   'c:\\program files\\python\\lib\\site-packages\\MySQLdb\\__init__.py',
   'PYMODULE'),
  ('MySQLdb._exceptions',
   'c:\\program files\\python\\lib\\site-packages\\MySQLdb\\_exceptions.py',
   'PYMODULE'),
  ('MySQLdb.connections',
   'c:\\program files\\python\\lib\\site-packages\\MySQLdb\\connections.py',
   'PYMODULE'),
  ('MySQLdb.constants',
   'c:\\program '
   'files\\python\\lib\\site-packages\\MySQLdb\\constants\\__init__.py',
   'PYMODULE'),
  ('MySQLdb.constants.CLIENT',
   'c:\\program '
   'files\\python\\lib\\site-packages\\MySQLdb\\constants\\CLIENT.py',
   'PYMODULE'),
  ('MySQLdb.constants.FIELD_TYPE',
   'c:\\program '
   'files\\python\\lib\\site-packages\\MySQLdb\\constants\\FIELD_TYPE.py',
   'PYMODULE'),
  ('MySQLdb.constants.FLAG',
   'c:\\program files\\python\\lib\\site-packages\\MySQLdb\\constants\\FLAG.py',
   'PYMODULE'),
  ('MySQLdb.converters',
   'c:\\program files\\python\\lib\\site-packages\\MySQLdb\\converters.py',
   'PYMODULE'),
  ('MySQLdb.cursors',
   'c:\\program files\\python\\lib\\site-packages\\MySQLdb\\cursors.py',
   'PYMODULE'),
  ('MySQLdb.release',
   'c:\\program files\\python\\lib\\site-packages\\MySQLdb\\release.py',
   'PYMODULE'),
  ('MySQLdb.times',
   'c:\\program files\\python\\lib\\site-packages\\MySQLdb\\times.py',
   'PYMODULE'),
  ('OpenSSL',
   'c:\\program files\\python\\lib\\site-packages\\OpenSSL\\__init__.py',
   'PYMODULE'),
  ('OpenSSL.SSL',
   'c:\\program files\\python\\lib\\site-packages\\OpenSSL\\SSL.py',
   'PYMODULE'),
  ('OpenSSL._util',
   'c:\\program files\\python\\lib\\site-packages\\OpenSSL\\_util.py',
   'PYMODULE'),
  ('OpenSSL.crypto',
   'c:\\program files\\python\\lib\\site-packages\\OpenSSL\\crypto.py',
   'PYMODULE'),
  ('OpenSSL.version',
   'c:\\program files\\python\\lib\\site-packages\\OpenSSL\\version.py',
   'PYMODULE'),
  ('PIL',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.FitsStubImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\FitsStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.Image',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PyAccess',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\PyAccess.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL._util',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._version',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('PIL.features',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('__future__', 'c:\\program files\\python\\lib\\__future__.py', 'PYMODULE'),
  ('_compat_pickle',
   'c:\\program files\\python\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'c:\\program files\\python\\lib\\_compression.py',
   'PYMODULE'),
  ('_distutils_hack',
   'c:\\program '
   'files\\python\\lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'c:\\program '
   'files\\python\\lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_dummy_thread',
   'c:\\program files\\python\\lib\\_dummy_thread.py',
   'PYMODULE'),
  ('_osx_support',
   'c:\\program files\\python\\lib\\_osx_support.py',
   'PYMODULE'),
  ('_py_abc', 'c:\\program files\\python\\lib\\_py_abc.py', 'PYMODULE'),
  ('_pydecimal', 'c:\\program files\\python\\lib\\_pydecimal.py', 'PYMODULE'),
  ('_pyi_rth_utils',
   'c:\\program '
   'files\\python\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils._win32',
   'c:\\program '
   'files\\python\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\_win32.py',
   'PYMODULE'),
  ('_pyi_rth_utils.tempfile',
   'c:\\program '
   'files\\python\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\tempfile.py',
   'PYMODULE'),
  ('_sitebuiltins',
   'c:\\program files\\python\\lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('_strptime', 'c:\\program files\\python\\lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local',
   'c:\\program files\\python\\lib\\_threading_local.py',
   'PYMODULE'),
  ('adodbapi',
   'c:\\program files\\python\\lib\\site-packages\\adodbapi\\__init__.py',
   'PYMODULE'),
  ('adodbapi.ado_consts',
   'c:\\program files\\python\\lib\\site-packages\\adodbapi\\ado_consts.py',
   'PYMODULE'),
  ('adodbapi.adodbapi',
   'c:\\program files\\python\\lib\\site-packages\\adodbapi\\adodbapi.py',
   'PYMODULE'),
  ('adodbapi.apibase',
   'c:\\program files\\python\\lib\\site-packages\\adodbapi\\apibase.py',
   'PYMODULE'),
  ('adodbapi.is64bit',
   'c:\\program files\\python\\lib\\site-packages\\adodbapi\\is64bit.py',
   'PYMODULE'),
  ('adodbapi.process_connect_string',
   'c:\\program '
   'files\\python\\lib\\site-packages\\adodbapi\\process_connect_string.py',
   'PYMODULE'),
  ('argparse', 'c:\\program files\\python\\lib\\argparse.py', 'PYMODULE'),
  ('ast', 'c:\\program files\\python\\lib\\ast.py', 'PYMODULE'),
  ('asyncio',
   'c:\\program files\\python\\lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'c:\\program files\\python\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'c:\\program files\\python\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'c:\\program files\\python\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'c:\\program files\\python\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'c:\\program files\\python\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'c:\\program files\\python\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'c:\\program files\\python\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'c:\\program files\\python\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'c:\\program files\\python\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'c:\\program files\\python\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'c:\\program files\\python\\lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'c:\\program files\\python\\lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'c:\\program files\\python\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'c:\\program files\\python\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'c:\\program files\\python\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'c:\\program files\\python\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'c:\\program files\\python\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'c:\\program files\\python\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'c:\\program files\\python\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'c:\\program files\\python\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'c:\\program files\\python\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'c:\\program files\\python\\lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.transports',
   'c:\\program files\\python\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'c:\\program files\\python\\lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'c:\\program files\\python\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'c:\\program files\\python\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'c:\\program files\\python\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('autocommand',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\autocommand\\__init__.py',
   'PYMODULE'),
  ('autocommand.autoasync',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\autocommand\\autoasync.py',
   'PYMODULE'),
  ('autocommand.autocommand',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\autocommand\\autocommand.py',
   'PYMODULE'),
  ('autocommand.automain',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\autocommand\\automain.py',
   'PYMODULE'),
  ('autocommand.autoparse',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\autocommand\\autoparse.py',
   'PYMODULE'),
  ('autocommand.errors',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\autocommand\\errors.py',
   'PYMODULE'),
  ('backports',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('backports.tarfile',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('backports.tarfile.compat',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('backports.tarfile.compat.py38',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('base64', 'c:\\program files\\python\\lib\\base64.py', 'PYMODULE'),
  ('bcrypt',
   'c:\\program files\\python\\lib\\site-packages\\bcrypt\\__init__.py',
   'PYMODULE'),
  ('bcrypt.__about__',
   'c:\\program files\\python\\lib\\site-packages\\bcrypt\\__about__.py',
   'PYMODULE'),
  ('bdb', 'c:\\program files\\python\\lib\\bdb.py', 'PYMODULE'),
  ('bisect', 'c:\\program files\\python\\lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'c:\\program files\\python\\lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'c:\\program files\\python\\lib\\calendar.py', 'PYMODULE'),
  ('ccxt',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\__init__.py',
   'PYMODULE'),
  ('ccxt.abstract',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\__init__.py',
   'PYMODULE'),
  ('ccxt.abstract.alpaca',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\alpaca.py',
   'PYMODULE'),
  ('ccxt.abstract.apex',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\apex.py',
   'PYMODULE'),
  ('ccxt.abstract.ascendex',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\ascendex.py',
   'PYMODULE'),
  ('ccxt.abstract.bequant',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\bequant.py',
   'PYMODULE'),
  ('ccxt.abstract.bigone',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\bigone.py',
   'PYMODULE'),
  ('ccxt.abstract.binance',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\binance.py',
   'PYMODULE'),
  ('ccxt.abstract.binancecoinm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\abstract\\binancecoinm.py',
   'PYMODULE'),
  ('ccxt.abstract.binanceus',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\abstract\\binanceus.py',
   'PYMODULE'),
  ('ccxt.abstract.binanceusdm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\abstract\\binanceusdm.py',
   'PYMODULE'),
  ('ccxt.abstract.bingx',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\bingx.py',
   'PYMODULE'),
  ('ccxt.abstract.bit2c',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\bit2c.py',
   'PYMODULE'),
  ('ccxt.abstract.bitbank',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\bitbank.py',
   'PYMODULE'),
  ('ccxt.abstract.bitbns',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\bitbns.py',
   'PYMODULE'),
  ('ccxt.abstract.bitfinex',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\bitfinex.py',
   'PYMODULE'),
  ('ccxt.abstract.bitflyer',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\bitflyer.py',
   'PYMODULE'),
  ('ccxt.abstract.bitget',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\bitget.py',
   'PYMODULE'),
  ('ccxt.abstract.bithumb',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\bithumb.py',
   'PYMODULE'),
  ('ccxt.abstract.bitmart',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\bitmart.py',
   'PYMODULE'),
  ('ccxt.abstract.bitmex',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\bitmex.py',
   'PYMODULE'),
  ('ccxt.abstract.bitopro',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\bitopro.py',
   'PYMODULE'),
  ('ccxt.abstract.bitrue',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\bitrue.py',
   'PYMODULE'),
  ('ccxt.abstract.bitso',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\bitso.py',
   'PYMODULE'),
  ('ccxt.abstract.bitstamp',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\bitstamp.py',
   'PYMODULE'),
  ('ccxt.abstract.bitteam',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\bitteam.py',
   'PYMODULE'),
  ('ccxt.abstract.bittrade',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\bittrade.py',
   'PYMODULE'),
  ('ccxt.abstract.bitvavo',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\bitvavo.py',
   'PYMODULE'),
  ('ccxt.abstract.blockchaincom',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\abstract\\blockchaincom.py',
   'PYMODULE'),
  ('ccxt.abstract.blofin',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\blofin.py',
   'PYMODULE'),
  ('ccxt.abstract.btcalpha',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\btcalpha.py',
   'PYMODULE'),
  ('ccxt.abstract.btcbox',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\btcbox.py',
   'PYMODULE'),
  ('ccxt.abstract.btcmarkets',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\abstract\\btcmarkets.py',
   'PYMODULE'),
  ('ccxt.abstract.btcturk',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\btcturk.py',
   'PYMODULE'),
  ('ccxt.abstract.bybit',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\bybit.py',
   'PYMODULE'),
  ('ccxt.abstract.cex',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\cex.py',
   'PYMODULE'),
  ('ccxt.abstract.coinbase',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\coinbase.py',
   'PYMODULE'),
  ('ccxt.abstract.coinbaseadvanced',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\abstract\\coinbaseadvanced.py',
   'PYMODULE'),
  ('ccxt.abstract.coinbaseexchange',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\abstract\\coinbaseexchange.py',
   'PYMODULE'),
  ('ccxt.abstract.coinbaseinternational',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\abstract\\coinbaseinternational.py',
   'PYMODULE'),
  ('ccxt.abstract.coincatch',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\abstract\\coincatch.py',
   'PYMODULE'),
  ('ccxt.abstract.coincheck',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\abstract\\coincheck.py',
   'PYMODULE'),
  ('ccxt.abstract.coinex',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\coinex.py',
   'PYMODULE'),
  ('ccxt.abstract.coinmate',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\coinmate.py',
   'PYMODULE'),
  ('ccxt.abstract.coinmetro',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\abstract\\coinmetro.py',
   'PYMODULE'),
  ('ccxt.abstract.coinone',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\coinone.py',
   'PYMODULE'),
  ('ccxt.abstract.coinsph',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\coinsph.py',
   'PYMODULE'),
  ('ccxt.abstract.coinspot',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\coinspot.py',
   'PYMODULE'),
  ('ccxt.abstract.cryptocom',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\abstract\\cryptocom.py',
   'PYMODULE'),
  ('ccxt.abstract.cryptomus',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\abstract\\cryptomus.py',
   'PYMODULE'),
  ('ccxt.abstract.defx',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\defx.py',
   'PYMODULE'),
  ('ccxt.abstract.delta',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\delta.py',
   'PYMODULE'),
  ('ccxt.abstract.deribit',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\deribit.py',
   'PYMODULE'),
  ('ccxt.abstract.derive',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\derive.py',
   'PYMODULE'),
  ('ccxt.abstract.digifinex',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\abstract\\digifinex.py',
   'PYMODULE'),
  ('ccxt.abstract.ellipx',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\ellipx.py',
   'PYMODULE'),
  ('ccxt.abstract.exmo',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\exmo.py',
   'PYMODULE'),
  ('ccxt.abstract.fmfwio',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\fmfwio.py',
   'PYMODULE'),
  ('ccxt.abstract.foxbit',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\foxbit.py',
   'PYMODULE'),
  ('ccxt.abstract.gate',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\gate.py',
   'PYMODULE'),
  ('ccxt.abstract.gateio',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\gateio.py',
   'PYMODULE'),
  ('ccxt.abstract.gemini',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\gemini.py',
   'PYMODULE'),
  ('ccxt.abstract.hashkey',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\hashkey.py',
   'PYMODULE'),
  ('ccxt.abstract.hitbtc',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\hitbtc.py',
   'PYMODULE'),
  ('ccxt.abstract.hollaex',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\hollaex.py',
   'PYMODULE'),
  ('ccxt.abstract.htx',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\htx.py',
   'PYMODULE'),
  ('ccxt.abstract.huobi',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\huobi.py',
   'PYMODULE'),
  ('ccxt.abstract.hyperliquid',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\abstract\\hyperliquid.py',
   'PYMODULE'),
  ('ccxt.abstract.independentreserve',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\abstract\\independentreserve.py',
   'PYMODULE'),
  ('ccxt.abstract.indodax',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\indodax.py',
   'PYMODULE'),
  ('ccxt.abstract.kraken',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\kraken.py',
   'PYMODULE'),
  ('ccxt.abstract.krakenfutures',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\abstract\\krakenfutures.py',
   'PYMODULE'),
  ('ccxt.abstract.kucoin',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\kucoin.py',
   'PYMODULE'),
  ('ccxt.abstract.kucoinfutures',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\abstract\\kucoinfutures.py',
   'PYMODULE'),
  ('ccxt.abstract.latoken',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\latoken.py',
   'PYMODULE'),
  ('ccxt.abstract.lbank',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\lbank.py',
   'PYMODULE'),
  ('ccxt.abstract.luno',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\luno.py',
   'PYMODULE'),
  ('ccxt.abstract.mercado',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\mercado.py',
   'PYMODULE'),
  ('ccxt.abstract.mexc',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\mexc.py',
   'PYMODULE'),
  ('ccxt.abstract.modetrade',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\abstract\\modetrade.py',
   'PYMODULE'),
  ('ccxt.abstract.myokx',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\myokx.py',
   'PYMODULE'),
  ('ccxt.abstract.ndax',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\ndax.py',
   'PYMODULE'),
  ('ccxt.abstract.novadax',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\novadax.py',
   'PYMODULE'),
  ('ccxt.abstract.oceanex',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\oceanex.py',
   'PYMODULE'),
  ('ccxt.abstract.okcoin',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\okcoin.py',
   'PYMODULE'),
  ('ccxt.abstract.okx',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\okx.py',
   'PYMODULE'),
  ('ccxt.abstract.okxus',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\okxus.py',
   'PYMODULE'),
  ('ccxt.abstract.onetrading',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\abstract\\onetrading.py',
   'PYMODULE'),
  ('ccxt.abstract.oxfun',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\oxfun.py',
   'PYMODULE'),
  ('ccxt.abstract.p2b',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\p2b.py',
   'PYMODULE'),
  ('ccxt.abstract.paradex',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\paradex.py',
   'PYMODULE'),
  ('ccxt.abstract.paymium',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\paymium.py',
   'PYMODULE'),
  ('ccxt.abstract.phemex',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\phemex.py',
   'PYMODULE'),
  ('ccxt.abstract.poloniex',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\poloniex.py',
   'PYMODULE'),
  ('ccxt.abstract.probit',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\probit.py',
   'PYMODULE'),
  ('ccxt.abstract.timex',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\timex.py',
   'PYMODULE'),
  ('ccxt.abstract.tokocrypto',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\abstract\\tokocrypto.py',
   'PYMODULE'),
  ('ccxt.abstract.tradeogre',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\abstract\\tradeogre.py',
   'PYMODULE'),
  ('ccxt.abstract.upbit',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\upbit.py',
   'PYMODULE'),
  ('ccxt.abstract.vertex',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\vertex.py',
   'PYMODULE'),
  ('ccxt.abstract.wavesexchange',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\abstract\\wavesexchange.py',
   'PYMODULE'),
  ('ccxt.abstract.whitebit',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\whitebit.py',
   'PYMODULE'),
  ('ccxt.abstract.woo',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\woo.py',
   'PYMODULE'),
  ('ccxt.abstract.woofipro',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\woofipro.py',
   'PYMODULE'),
  ('ccxt.abstract.xt',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\xt.py',
   'PYMODULE'),
  ('ccxt.abstract.yobit',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\yobit.py',
   'PYMODULE'),
  ('ccxt.abstract.zaif',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\zaif.py',
   'PYMODULE'),
  ('ccxt.abstract.zonda',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\abstract\\zonda.py',
   'PYMODULE'),
  ('ccxt.alpaca',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\alpaca.py',
   'PYMODULE'),
  ('ccxt.apex',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\apex.py',
   'PYMODULE'),
  ('ccxt.ascendex',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\ascendex.py',
   'PYMODULE'),
  ('ccxt.base',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\base\\__init__.py',
   'PYMODULE'),
  ('ccxt.base.decimal_to_precision',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\base\\decimal_to_precision.py',
   'PYMODULE'),
  ('ccxt.base.errors',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\base\\errors.py',
   'PYMODULE'),
  ('ccxt.base.exchange',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\base\\exchange.py',
   'PYMODULE'),
  ('ccxt.base.precise',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\base\\precise.py',
   'PYMODULE'),
  ('ccxt.base.types',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\base\\types.py',
   'PYMODULE'),
  ('ccxt.bequant',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\bequant.py',
   'PYMODULE'),
  ('ccxt.bigone',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\bigone.py',
   'PYMODULE'),
  ('ccxt.binance',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\binance.py',
   'PYMODULE'),
  ('ccxt.binancecoinm',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\binancecoinm.py',
   'PYMODULE'),
  ('ccxt.binanceus',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\binanceus.py',
   'PYMODULE'),
  ('ccxt.binanceusdm',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\binanceusdm.py',
   'PYMODULE'),
  ('ccxt.bingx',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\bingx.py',
   'PYMODULE'),
  ('ccxt.bit2c',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\bit2c.py',
   'PYMODULE'),
  ('ccxt.bitbank',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\bitbank.py',
   'PYMODULE'),
  ('ccxt.bitbns',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\bitbns.py',
   'PYMODULE'),
  ('ccxt.bitfinex',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\bitfinex.py',
   'PYMODULE'),
  ('ccxt.bitflyer',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\bitflyer.py',
   'PYMODULE'),
  ('ccxt.bitget',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\bitget.py',
   'PYMODULE'),
  ('ccxt.bithumb',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\bithumb.py',
   'PYMODULE'),
  ('ccxt.bitmart',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\bitmart.py',
   'PYMODULE'),
  ('ccxt.bitmex',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\bitmex.py',
   'PYMODULE'),
  ('ccxt.bitopro',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\bitopro.py',
   'PYMODULE'),
  ('ccxt.bitrue',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\bitrue.py',
   'PYMODULE'),
  ('ccxt.bitso',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\bitso.py',
   'PYMODULE'),
  ('ccxt.bitstamp',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\bitstamp.py',
   'PYMODULE'),
  ('ccxt.bitteam',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\bitteam.py',
   'PYMODULE'),
  ('ccxt.bittrade',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\bittrade.py',
   'PYMODULE'),
  ('ccxt.bitvavo',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\bitvavo.py',
   'PYMODULE'),
  ('ccxt.blockchaincom',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\blockchaincom.py',
   'PYMODULE'),
  ('ccxt.blofin',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\blofin.py',
   'PYMODULE'),
  ('ccxt.btcalpha',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\btcalpha.py',
   'PYMODULE'),
  ('ccxt.btcbox',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\btcbox.py',
   'PYMODULE'),
  ('ccxt.btcmarkets',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\btcmarkets.py',
   'PYMODULE'),
  ('ccxt.btcturk',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\btcturk.py',
   'PYMODULE'),
  ('ccxt.bybit',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\bybit.py',
   'PYMODULE'),
  ('ccxt.cex',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\cex.py',
   'PYMODULE'),
  ('ccxt.coinbase',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\coinbase.py',
   'PYMODULE'),
  ('ccxt.coinbaseadvanced',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\coinbaseadvanced.py',
   'PYMODULE'),
  ('ccxt.coinbaseexchange',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\coinbaseexchange.py',
   'PYMODULE'),
  ('ccxt.coinbaseinternational',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\coinbaseinternational.py',
   'PYMODULE'),
  ('ccxt.coincatch',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\coincatch.py',
   'PYMODULE'),
  ('ccxt.coincheck',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\coincheck.py',
   'PYMODULE'),
  ('ccxt.coinex',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\coinex.py',
   'PYMODULE'),
  ('ccxt.coinmate',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\coinmate.py',
   'PYMODULE'),
  ('ccxt.coinmetro',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\coinmetro.py',
   'PYMODULE'),
  ('ccxt.coinone',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\coinone.py',
   'PYMODULE'),
  ('ccxt.coinsph',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\coinsph.py',
   'PYMODULE'),
  ('ccxt.coinspot',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\coinspot.py',
   'PYMODULE'),
  ('ccxt.cryptocom',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\cryptocom.py',
   'PYMODULE'),
  ('ccxt.cryptomus',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\cryptomus.py',
   'PYMODULE'),
  ('ccxt.defx',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\defx.py',
   'PYMODULE'),
  ('ccxt.delta',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\delta.py',
   'PYMODULE'),
  ('ccxt.deribit',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\deribit.py',
   'PYMODULE'),
  ('ccxt.derive',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\derive.py',
   'PYMODULE'),
  ('ccxt.digifinex',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\digifinex.py',
   'PYMODULE'),
  ('ccxt.ellipx',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\ellipx.py',
   'PYMODULE'),
  ('ccxt.exmo',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\exmo.py',
   'PYMODULE'),
  ('ccxt.fmfwio',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\fmfwio.py',
   'PYMODULE'),
  ('ccxt.foxbit',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\foxbit.py',
   'PYMODULE'),
  ('ccxt.gate',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\gate.py',
   'PYMODULE'),
  ('ccxt.gateio',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\gateio.py',
   'PYMODULE'),
  ('ccxt.gemini',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\gemini.py',
   'PYMODULE'),
  ('ccxt.hashkey',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\hashkey.py',
   'PYMODULE'),
  ('ccxt.hitbtc',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\hitbtc.py',
   'PYMODULE'),
  ('ccxt.hollaex',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\hollaex.py',
   'PYMODULE'),
  ('ccxt.htx',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\htx.py',
   'PYMODULE'),
  ('ccxt.huobi',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\huobi.py',
   'PYMODULE'),
  ('ccxt.hyperliquid',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\hyperliquid.py',
   'PYMODULE'),
  ('ccxt.independentreserve',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\independentreserve.py',
   'PYMODULE'),
  ('ccxt.indodax',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\indodax.py',
   'PYMODULE'),
  ('ccxt.kraken',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\kraken.py',
   'PYMODULE'),
  ('ccxt.krakenfutures',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\krakenfutures.py',
   'PYMODULE'),
  ('ccxt.kucoin',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\kucoin.py',
   'PYMODULE'),
  ('ccxt.kucoinfutures',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\kucoinfutures.py',
   'PYMODULE'),
  ('ccxt.latoken',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\latoken.py',
   'PYMODULE'),
  ('ccxt.lbank',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\lbank.py',
   'PYMODULE'),
  ('ccxt.luno',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\luno.py',
   'PYMODULE'),
  ('ccxt.mercado',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\mercado.py',
   'PYMODULE'),
  ('ccxt.mexc',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\mexc.py',
   'PYMODULE'),
  ('ccxt.modetrade',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\modetrade.py',
   'PYMODULE'),
  ('ccxt.myokx',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\myokx.py',
   'PYMODULE'),
  ('ccxt.ndax',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\ndax.py',
   'PYMODULE'),
  ('ccxt.novadax',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\novadax.py',
   'PYMODULE'),
  ('ccxt.oceanex',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\oceanex.py',
   'PYMODULE'),
  ('ccxt.okcoin',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\okcoin.py',
   'PYMODULE'),
  ('ccxt.okx',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\okx.py',
   'PYMODULE'),
  ('ccxt.okxus',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\okxus.py',
   'PYMODULE'),
  ('ccxt.onetrading',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\onetrading.py',
   'PYMODULE'),
  ('ccxt.oxfun',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\oxfun.py',
   'PYMODULE'),
  ('ccxt.p2b',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\p2b.py',
   'PYMODULE'),
  ('ccxt.paradex',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\paradex.py',
   'PYMODULE'),
  ('ccxt.paymium',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\paymium.py',
   'PYMODULE'),
  ('ccxt.phemex',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\phemex.py',
   'PYMODULE'),
  ('ccxt.poloniex',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\poloniex.py',
   'PYMODULE'),
  ('ccxt.probit',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\probit.py',
   'PYMODULE'),
  ('ccxt.static_dependencies',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\__init__.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.ecdsa',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\ecdsa\\__init__.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.ecdsa.curves',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\ecdsa\\curves.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.ecdsa.der',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\ecdsa\\der.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.ecdsa.ecdsa',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\ecdsa\\ecdsa.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.ecdsa.ellipticcurve',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\ecdsa\\ellipticcurve.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.ecdsa.keys',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\ecdsa\\keys.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.ecdsa.numbertheory',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\ecdsa\\numbertheory.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.ecdsa.rfc6979',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\ecdsa\\rfc6979.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.ecdsa.util',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\ecdsa\\util.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.ethereum',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\ethereum\\__init__.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.ethereum.abi',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\ethereum\\abi\\__init__.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.ethereum.abi.abi',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\ethereum\\abi\\abi.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.ethereum.abi.base',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\ethereum\\abi\\base.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.ethereum.abi.codec',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\ethereum\\abi\\codec.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.ethereum.abi.decoding',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\ethereum\\abi\\decoding.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.ethereum.abi.encoding',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\ethereum\\abi\\encoding.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.ethereum.abi.exceptions',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\ethereum\\abi\\exceptions.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.ethereum.abi.grammar',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\ethereum\\abi\\grammar.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.ethereum.abi.registry',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\ethereum\\abi\\registry.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.ethereum.abi.utils',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\ethereum\\abi\\utils\\__init__.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.ethereum.abi.utils.numeric',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\ethereum\\abi\\utils\\numeric.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.ethereum.abi.utils.padding',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\ethereum\\abi\\utils\\padding.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.ethereum.abi.utils.string',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\ethereum\\abi\\utils\\string.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.ethereum.account',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\ethereum\\account\\__init__.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.ethereum.account.encode_typed_data',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\ethereum\\account\\encode_typed_data\\__init__.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.ethereum.account.encode_typed_data.encoding_and_hashing',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\ethereum\\account\\encode_typed_data\\encoding_and_hashing.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.ethereum.account.encode_typed_data.helpers',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\ethereum\\account\\encode_typed_data\\helpers.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.ethereum.account.messages',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\ethereum\\account\\messages.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.ethereum.hexbytes',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\ethereum\\hexbytes\\__init__.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.ethereum.hexbytes._utils',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\ethereum\\hexbytes\\_utils.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.ethereum.hexbytes.main',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\ethereum\\hexbytes\\main.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.ethereum.typing',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\ethereum\\typing\\__init__.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.ethereum.typing.abi',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\ethereum\\typing\\abi.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.ethereum.typing.bls',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\ethereum\\typing\\bls.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.ethereum.typing.discovery',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\ethereum\\typing\\discovery.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.ethereum.typing.encoding',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\ethereum\\typing\\encoding.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.ethereum.typing.enums',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\ethereum\\typing\\enums.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.ethereum.typing.ethpm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\ethereum\\typing\\ethpm.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.ethereum.typing.evm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\ethereum\\typing\\evm.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.ethereum.typing.networks',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\ethereum\\typing\\networks.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.ethereum.utils',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\ethereum\\utils\\__init__.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.ethereum.utils.address',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\ethereum\\utils\\address.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.ethereum.utils.applicators',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\ethereum\\utils\\applicators.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.ethereum.utils.conversions',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\ethereum\\utils\\conversions.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.ethereum.utils.currency',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\ethereum\\utils\\currency.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.ethereum.utils.curried',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\ethereum\\utils\\curried\\__init__.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.ethereum.utils.decorators',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\ethereum\\utils\\decorators.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.ethereum.utils.encoding',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\ethereum\\utils\\encoding.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.ethereum.utils.exceptions',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\ethereum\\utils\\exceptions.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.ethereum.utils.functional',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\ethereum\\utils\\functional.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.ethereum.utils.hexadecimal',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\ethereum\\utils\\hexadecimal.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.ethereum.utils.humanize',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\ethereum\\utils\\humanize.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.ethereum.utils.logging',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\ethereum\\utils\\logging.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.ethereum.utils.module_loading',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\ethereum\\utils\\module_loading.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.ethereum.utils.numeric',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\ethereum\\utils\\numeric.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.ethereum.utils.toolz',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\ethereum\\utils\\toolz.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.ethereum.utils.types',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\ethereum\\utils\\types.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.ethereum.utils.units',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\ethereum\\utils\\units.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.keccak',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\keccak\\__init__.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.keccak.keccak',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\keccak\\keccak.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.marshmallow',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\marshmallow\\__init__.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.marshmallow.base',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\marshmallow\\base.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.marshmallow.class_registry',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\marshmallow\\class_registry.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.marshmallow.decorators',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\marshmallow\\decorators.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.marshmallow.error_store',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\marshmallow\\error_store.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.marshmallow.exceptions',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\marshmallow\\exceptions.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.marshmallow.fields',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\marshmallow\\fields.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.marshmallow.orderedset',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\marshmallow\\orderedset.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.marshmallow.schema',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\marshmallow\\schema.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.marshmallow.types',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\marshmallow\\types.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.marshmallow.utils',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\marshmallow\\utils.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.marshmallow.validate',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\marshmallow\\validate.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.marshmallow.warnings',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\marshmallow\\warnings.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.msgpack',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\msgpack\\__init__.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.msgpack.exceptions',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\msgpack\\exceptions.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.msgpack.ext',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\msgpack\\ext.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.msgpack.fallback',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\msgpack\\fallback.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.parsimonious',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\parsimonious\\__init__.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.parsimonious.exceptions',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\parsimonious\\exceptions.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.parsimonious.expressions',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\parsimonious\\expressions.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.parsimonious.grammar',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\parsimonious\\grammar.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.parsimonious.nodes',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\parsimonious\\nodes.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.parsimonious.utils',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\parsimonious\\utils.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.starknet',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\starknet\\__init__.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.starknet.cairo',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\starknet\\cairo\\__init__.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.starknet.cairo.felt',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\starknet\\cairo\\felt.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.starknet.ccxt_utils',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\starknet\\ccxt_utils.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.starknet.common',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\starknet\\common.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.starknet.constants',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\starknet\\constants.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.starknet.hash',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\starknet\\hash\\__init__.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.starknet.hash.address',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\starknet\\hash\\address.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.starknet.hash.selector',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\starknet\\hash\\selector.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.starknet.hash.utils',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\starknet\\hash\\utils.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.starknet.models',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\starknet\\models\\__init__.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.starknet.models.typed_data',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\starknet\\models\\typed_data.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.starknet.utils',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\starknet\\utils\\__init__.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.starknet.utils.typed_data',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\starknet\\utils\\typed_data.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.starkware',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\starkware\\__init__.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.starkware.crypto',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\starkware\\crypto\\__init__.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.starkware.crypto.fast_pedersen_hash',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\starkware\\crypto\\fast_pedersen_hash.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.starkware.crypto.math_utils',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\starkware\\crypto\\math_utils.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.starkware.crypto.signature',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\starkware\\crypto\\signature.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.starkware.crypto.utils',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\starkware\\crypto\\utils.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.sympy',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\sympy\\__init__.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.sympy.core',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\sympy\\core\\__init__.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.sympy.core.intfunc',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\sympy\\core\\intfunc.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.sympy.external',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\sympy\\external\\__init__.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.sympy.external.gmpy',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\sympy\\external\\gmpy.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.sympy.external.importtools',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\sympy\\external\\importtools.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.sympy.external.ntheory',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\sympy\\external\\ntheory.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.sympy.external.pythonmpq',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\sympy\\external\\pythonmpq.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.toolz',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\toolz\\__init__.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.toolz._signatures',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\toolz\\_signatures.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.toolz._version',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\toolz\\_version.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.toolz.curried',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\toolz\\curried\\__init__.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.toolz.curried.exceptions',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\toolz\\curried\\exceptions.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.toolz.curried.operator',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\toolz\\curried\\operator.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.toolz.dicttoolz',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\toolz\\dicttoolz.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.toolz.functoolz',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\toolz\\functoolz.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.toolz.itertoolz',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\toolz\\itertoolz.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.toolz.recipes',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\toolz\\recipes.py',
   'PYMODULE'),
  ('ccxt.static_dependencies.toolz.utils',
   'c:\\program '
   'files\\python\\lib\\site-packages\\ccxt\\static_dependencies\\toolz\\utils.py',
   'PYMODULE'),
  ('ccxt.timex',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\timex.py',
   'PYMODULE'),
  ('ccxt.tokocrypto',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\tokocrypto.py',
   'PYMODULE'),
  ('ccxt.tradeogre',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\tradeogre.py',
   'PYMODULE'),
  ('ccxt.upbit',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\upbit.py',
   'PYMODULE'),
  ('ccxt.vertex',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\vertex.py',
   'PYMODULE'),
  ('ccxt.wavesexchange',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\wavesexchange.py',
   'PYMODULE'),
  ('ccxt.whitebit',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\whitebit.py',
   'PYMODULE'),
  ('ccxt.woo',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\woo.py',
   'PYMODULE'),
  ('ccxt.woofipro',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\woofipro.py',
   'PYMODULE'),
  ('ccxt.xt',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\xt.py',
   'PYMODULE'),
  ('ccxt.yobit',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\yobit.py',
   'PYMODULE'),
  ('ccxt.zaif',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\zaif.py',
   'PYMODULE'),
  ('ccxt.zonda',
   'c:\\program files\\python\\lib\\site-packages\\ccxt\\zonda.py',
   'PYMODULE'),
  ('certifi',
   'c:\\program files\\python\\lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'c:\\program files\\python\\lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('cffi',
   'c:\\program files\\python\\lib\\site-packages\\cffi\\__init__.py',
   'PYMODULE'),
  ('cffi.api',
   'c:\\program files\\python\\lib\\site-packages\\cffi\\api.py',
   'PYMODULE'),
  ('cffi.cffi_opcode',
   'c:\\program files\\python\\lib\\site-packages\\cffi\\cffi_opcode.py',
   'PYMODULE'),
  ('cffi.commontypes',
   'c:\\program files\\python\\lib\\site-packages\\cffi\\commontypes.py',
   'PYMODULE'),
  ('cffi.cparser',
   'c:\\program files\\python\\lib\\site-packages\\cffi\\cparser.py',
   'PYMODULE'),
  ('cffi.error',
   'c:\\program files\\python\\lib\\site-packages\\cffi\\error.py',
   'PYMODULE'),
  ('cffi.ffiplatform',
   'c:\\program files\\python\\lib\\site-packages\\cffi\\ffiplatform.py',
   'PYMODULE'),
  ('cffi.lock',
   'c:\\program files\\python\\lib\\site-packages\\cffi\\lock.py',
   'PYMODULE'),
  ('cffi.model',
   'c:\\program files\\python\\lib\\site-packages\\cffi\\model.py',
   'PYMODULE'),
  ('cffi.pkgconfig',
   'c:\\program files\\python\\lib\\site-packages\\cffi\\pkgconfig.py',
   'PYMODULE'),
  ('cffi.recompiler',
   'c:\\program files\\python\\lib\\site-packages\\cffi\\recompiler.py',
   'PYMODULE'),
  ('cffi.vengine_cpy',
   'c:\\program files\\python\\lib\\site-packages\\cffi\\vengine_cpy.py',
   'PYMODULE'),
  ('cffi.vengine_gen',
   'c:\\program files\\python\\lib\\site-packages\\cffi\\vengine_gen.py',
   'PYMODULE'),
  ('cffi.verifier',
   'c:\\program files\\python\\lib\\site-packages\\cffi\\verifier.py',
   'PYMODULE'),
  ('cgi', 'c:\\program files\\python\\lib\\cgi.py', 'PYMODULE'),
  ('chardet',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\__init__.py',
   'PYMODULE'),
  ('chardet.big5freq',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\big5freq.py',
   'PYMODULE'),
  ('chardet.big5prober',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\big5prober.py',
   'PYMODULE'),
  ('chardet.chardistribution',
   'c:\\program '
   'files\\python\\lib\\site-packages\\chardet\\chardistribution.py',
   'PYMODULE'),
  ('chardet.charsetgroupprober',
   'c:\\program '
   'files\\python\\lib\\site-packages\\chardet\\charsetgroupprober.py',
   'PYMODULE'),
  ('chardet.charsetprober',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\charsetprober.py',
   'PYMODULE'),
  ('chardet.codingstatemachine',
   'c:\\program '
   'files\\python\\lib\\site-packages\\chardet\\codingstatemachine.py',
   'PYMODULE'),
  ('chardet.compat',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\compat.py',
   'PYMODULE'),
  ('chardet.cp949prober',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\cp949prober.py',
   'PYMODULE'),
  ('chardet.enums',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\enums.py',
   'PYMODULE'),
  ('chardet.escprober',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\escprober.py',
   'PYMODULE'),
  ('chardet.escsm',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\escsm.py',
   'PYMODULE'),
  ('chardet.eucjpprober',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\eucjpprober.py',
   'PYMODULE'),
  ('chardet.euckrfreq',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\euckrfreq.py',
   'PYMODULE'),
  ('chardet.euckrprober',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\euckrprober.py',
   'PYMODULE'),
  ('chardet.euctwfreq',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\euctwfreq.py',
   'PYMODULE'),
  ('chardet.euctwprober',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\euctwprober.py',
   'PYMODULE'),
  ('chardet.gb2312freq',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\gb2312freq.py',
   'PYMODULE'),
  ('chardet.gb2312prober',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\gb2312prober.py',
   'PYMODULE'),
  ('chardet.hebrewprober',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\hebrewprober.py',
   'PYMODULE'),
  ('chardet.jisfreq',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\jisfreq.py',
   'PYMODULE'),
  ('chardet.jpcntx',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\jpcntx.py',
   'PYMODULE'),
  ('chardet.langbulgarianmodel',
   'c:\\program '
   'files\\python\\lib\\site-packages\\chardet\\langbulgarianmodel.py',
   'PYMODULE'),
  ('chardet.langcyrillicmodel',
   'c:\\program '
   'files\\python\\lib\\site-packages\\chardet\\langcyrillicmodel.py',
   'PYMODULE'),
  ('chardet.langgreekmodel',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\langgreekmodel.py',
   'PYMODULE'),
  ('chardet.langhebrewmodel',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\langhebrewmodel.py',
   'PYMODULE'),
  ('chardet.langthaimodel',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\langthaimodel.py',
   'PYMODULE'),
  ('chardet.langturkishmodel',
   'c:\\program '
   'files\\python\\lib\\site-packages\\chardet\\langturkishmodel.py',
   'PYMODULE'),
  ('chardet.latin1prober',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\latin1prober.py',
   'PYMODULE'),
  ('chardet.mbcharsetprober',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\mbcharsetprober.py',
   'PYMODULE'),
  ('chardet.mbcsgroupprober',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\mbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.mbcssm',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\mbcssm.py',
   'PYMODULE'),
  ('chardet.sbcharsetprober',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\sbcharsetprober.py',
   'PYMODULE'),
  ('chardet.sbcsgroupprober',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\sbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.sjisprober',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\sjisprober.py',
   'PYMODULE'),
  ('chardet.universaldetector',
   'c:\\program '
   'files\\python\\lib\\site-packages\\chardet\\universaldetector.py',
   'PYMODULE'),
  ('chardet.utf8prober',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\utf8prober.py',
   'PYMODULE'),
  ('chardet.version',
   'c:\\program files\\python\\lib\\site-packages\\chardet\\version.py',
   'PYMODULE'),
  ('charset_normalizer',
   'c:\\program '
   'files\\python\\lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'c:\\program files\\python\\lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'c:\\program files\\python\\lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'c:\\program '
   'files\\python\\lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'c:\\program '
   'files\\python\\lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'c:\\program '
   'files\\python\\lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'c:\\program '
   'files\\python\\lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'c:\\program '
   'files\\python\\lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('cmd', 'c:\\program files\\python\\lib\\cmd.py', 'PYMODULE'),
  ('code', 'c:\\program files\\python\\lib\\code.py', 'PYMODULE'),
  ('codeop', 'c:\\program files\\python\\lib\\codeop.py', 'PYMODULE'),
  ('colorsys', 'c:\\program files\\python\\lib\\colorsys.py', 'PYMODULE'),
  ('commctrl',
   'c:\\program files\\python\\lib\\site-packages\\win32\\lib\\commctrl.py',
   'PYMODULE'),
  ('concurrent',
   'c:\\program files\\python\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'c:\\program files\\python\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'c:\\program files\\python\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'c:\\program files\\python\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'c:\\program files\\python\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('config',
   'C:\\Users\\<USER>\\Desktop\\yl\\2.0\\config.py',
   'PYMODULE'),
  ('configparser',
   'c:\\program files\\python\\lib\\configparser.py',
   'PYMODULE'),
  ('contextlib', 'c:\\program files\\python\\lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'c:\\program files\\python\\lib\\contextvars.py', 'PYMODULE'),
  ('contourpy',
   'c:\\program files\\python\\lib\\site-packages\\contourpy\\__init__.py',
   'PYMODULE'),
  ('contourpy._version',
   'c:\\program files\\python\\lib\\site-packages\\contourpy\\_version.py',
   'PYMODULE'),
  ('contourpy.chunk',
   'c:\\program files\\python\\lib\\site-packages\\contourpy\\chunk.py',
   'PYMODULE'),
  ('contourpy.enum_util',
   'c:\\program files\\python\\lib\\site-packages\\contourpy\\enum_util.py',
   'PYMODULE'),
  ('copy', 'c:\\program files\\python\\lib\\copy.py', 'PYMODULE'),
  ('cryptography',
   'c:\\program files\\python\\lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'c:\\program files\\python\\lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'c:\\program files\\python\\lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.fernet',
   'c:\\program files\\python\\lib\\site-packages\\cryptography\\fernet.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\decrepit\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers.algorithms',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hmac',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\hmac.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.kdf',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\kdf\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.kdf.pbkdf2',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\kdf\\pbkdf2.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.padding',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('cryptography.utils',
   'c:\\program files\\python\\lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('cryptography.x509',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'c:\\program files\\python\\lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'c:\\program files\\python\\lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'c:\\program files\\python\\lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('cryptography.x509.verification',
   'c:\\program '
   'files\\python\\lib\\site-packages\\cryptography\\x509\\verification.py',
   'PYMODULE'),
  ('csv', 'c:\\program files\\python\\lib\\csv.py', 'PYMODULE'),
  ('ctypes', 'c:\\program files\\python\\lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._aix',
   'c:\\program files\\python\\lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes._endian',
   'c:\\program files\\python\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'c:\\program files\\python\\lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'c:\\program files\\python\\lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'c:\\program files\\python\\lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'c:\\program files\\python\\lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util',
   'c:\\program files\\python\\lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'c:\\program files\\python\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('cycler',
   'c:\\program files\\python\\lib\\site-packages\\cycler\\__init__.py',
   'PYMODULE'),
  ('dataclasses', 'c:\\program files\\python\\lib\\dataclasses.py', 'PYMODULE'),
  ('datetime', 'c:\\program files\\python\\lib\\datetime.py', 'PYMODULE'),
  ('dateutil',
   'c:\\program files\\python\\lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil._common',
   'c:\\program files\\python\\lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('dateutil._version',
   'c:\\program files\\python\\lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('dateutil.easter',
   'c:\\program files\\python\\lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil.parser',
   'c:\\program '
   'files\\python\\lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'c:\\program '
   'files\\python\\lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'c:\\program '
   'files\\python\\lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'c:\\program files\\python\\lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'c:\\program files\\python\\lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.tz',
   'c:\\program files\\python\\lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'c:\\program files\\python\\lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'c:\\program files\\python\\lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'c:\\program files\\python\\lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'c:\\program files\\python\\lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'c:\\program '
   'files\\python\\lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('decimal', 'c:\\program files\\python\\lib\\decimal.py', 'PYMODULE'),
  ('difflib', 'c:\\program files\\python\\lib\\difflib.py', 'PYMODULE'),
  ('dis', 'c:\\program files\\python\\lib\\dis.py', 'PYMODULE'),
  ('distutils',
   'c:\\program files\\python\\lib\\distutils\\__init__.py',
   'PYMODULE'),
  ('distutils._msvccompiler',
   'c:\\program files\\python\\lib\\distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('distutils.archive_util',
   'c:\\program files\\python\\lib\\distutils\\archive_util.py',
   'PYMODULE'),
  ('distutils.ccompiler',
   'c:\\program files\\python\\lib\\distutils\\ccompiler.py',
   'PYMODULE'),
  ('distutils.cmd',
   'c:\\program files\\python\\lib\\distutils\\cmd.py',
   'PYMODULE'),
  ('distutils.command',
   'c:\\program files\\python\\lib\\distutils\\command\\__init__.py',
   'PYMODULE'),
  ('distutils.command.bdist',
   'c:\\program files\\python\\lib\\distutils\\command\\bdist.py',
   'PYMODULE'),
  ('distutils.command.build',
   'c:\\program files\\python\\lib\\distutils\\command\\build.py',
   'PYMODULE'),
  ('distutils.command.build_ext',
   'c:\\program files\\python\\lib\\distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('distutils.command.sdist',
   'c:\\program files\\python\\lib\\distutils\\command\\sdist.py',
   'PYMODULE'),
  ('distutils.config',
   'c:\\program files\\python\\lib\\distutils\\config.py',
   'PYMODULE'),
  ('distutils.core',
   'c:\\program files\\python\\lib\\distutils\\core.py',
   'PYMODULE'),
  ('distutils.debug',
   'c:\\program files\\python\\lib\\distutils\\debug.py',
   'PYMODULE'),
  ('distutils.dep_util',
   'c:\\program files\\python\\lib\\distutils\\dep_util.py',
   'PYMODULE'),
  ('distutils.dir_util',
   'c:\\program files\\python\\lib\\distutils\\dir_util.py',
   'PYMODULE'),
  ('distutils.dist',
   'c:\\program files\\python\\lib\\distutils\\dist.py',
   'PYMODULE'),
  ('distutils.errors',
   'c:\\program files\\python\\lib\\distutils\\errors.py',
   'PYMODULE'),
  ('distutils.extension',
   'c:\\program files\\python\\lib\\distutils\\extension.py',
   'PYMODULE'),
  ('distutils.fancy_getopt',
   'c:\\program files\\python\\lib\\distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('distutils.file_util',
   'c:\\program files\\python\\lib\\distutils\\file_util.py',
   'PYMODULE'),
  ('distutils.filelist',
   'c:\\program files\\python\\lib\\distutils\\filelist.py',
   'PYMODULE'),
  ('distutils.log',
   'c:\\program files\\python\\lib\\distutils\\log.py',
   'PYMODULE'),
  ('distutils.msvc9compiler',
   'c:\\program files\\python\\lib\\distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('distutils.spawn',
   'c:\\program files\\python\\lib\\distutils\\spawn.py',
   'PYMODULE'),
  ('distutils.sysconfig',
   'c:\\program files\\python\\lib\\distutils\\sysconfig.py',
   'PYMODULE'),
  ('distutils.text_file',
   'c:\\program files\\python\\lib\\distutils\\text_file.py',
   'PYMODULE'),
  ('distutils.util',
   'c:\\program files\\python\\lib\\distutils\\util.py',
   'PYMODULE'),
  ('distutils.version',
   'c:\\program files\\python\\lib\\distutils\\version.py',
   'PYMODULE'),
  ('distutils.versionpredicate',
   'c:\\program files\\python\\lib\\distutils\\versionpredicate.py',
   'PYMODULE'),
  ('doctest', 'c:\\program files\\python\\lib\\doctest.py', 'PYMODULE'),
  ('dummy_threading',
   'c:\\program files\\python\\lib\\dummy_threading.py',
   'PYMODULE'),
  ('email', 'c:\\program files\\python\\lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'c:\\program files\\python\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'c:\\program files\\python\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'c:\\program files\\python\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'c:\\program files\\python\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'c:\\program files\\python\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'c:\\program files\\python\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'c:\\program files\\python\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'c:\\program files\\python\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'c:\\program files\\python\\lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'c:\\program files\\python\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'c:\\program files\\python\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'c:\\program files\\python\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'c:\\program files\\python\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'c:\\program files\\python\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'c:\\program files\\python\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'c:\\program files\\python\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'c:\\program files\\python\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'c:\\program files\\python\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'c:\\program files\\python\\lib\\email\\utils.py',
   'PYMODULE'),
  ('environment_control_panel',
   'C:\\Users\\<USER>\\Desktop\\yl\\2.0\\environment_control_panel.py',
   'PYMODULE'),
  ('environment_manager',
   'C:\\Users\\<USER>\\Desktop\\yl\\2.0\\environment_manager.py',
   'PYMODULE'),
  ('error_handler',
   'C:\\Users\\<USER>\\Desktop\\yl\\2.0\\error_handler.py',
   'PYMODULE'),
  ('exchange_manager',
   'C:\\Users\\<USER>\\Desktop\\yl\\2.0\\exchange_manager.py',
   'PYMODULE'),
  ('fnmatch', 'c:\\program files\\python\\lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'c:\\program files\\python\\lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'c:\\program files\\python\\lib\\ftplib.py', 'PYMODULE'),
  ('gate_adapter',
   'C:\\Users\\<USER>\\Desktop\\yl\\2.0\\gate_adapter.py',
   'PYMODULE'),
  ('getopt', 'c:\\program files\\python\\lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'c:\\program files\\python\\lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'c:\\program files\\python\\lib\\gettext.py', 'PYMODULE'),
  ('glob', 'c:\\program files\\python\\lib\\glob.py', 'PYMODULE'),
  ('gzip', 'c:\\program files\\python\\lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'c:\\program files\\python\\lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'c:\\program files\\python\\lib\\hmac.py', 'PYMODULE'),
  ('html', 'c:\\program files\\python\\lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities',
   'c:\\program files\\python\\lib\\html\\entities.py',
   'PYMODULE'),
  ('http', 'c:\\program files\\python\\lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client',
   'c:\\program files\\python\\lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'c:\\program files\\python\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.cookies',
   'c:\\program files\\python\\lib\\http\\cookies.py',
   'PYMODULE'),
  ('http.server',
   'c:\\program files\\python\\lib\\http\\server.py',
   'PYMODULE'),
  ('idna',
   'c:\\program files\\python\\lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'c:\\program files\\python\\lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'c:\\program files\\python\\lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'c:\\program files\\python\\lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'c:\\program files\\python\\lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'c:\\program files\\python\\lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('imp', 'c:\\program files\\python\\lib\\imp.py', 'PYMODULE'),
  ('importlib',
   'c:\\program files\\python\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'c:\\program files\\python\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'c:\\program files\\python\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'c:\\program files\\python\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'c:\\program files\\python\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'c:\\program files\\python\\lib\\importlib\\metadata.py',
   'PYMODULE'),
  ('importlib.resources',
   'c:\\program files\\python\\lib\\importlib\\resources.py',
   'PYMODULE'),
  ('importlib.util',
   'c:\\program files\\python\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('importlib_metadata',
   'c:\\program '
   'files\\python\\lib\\site-packages\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('importlib_metadata._adapters',
   'c:\\program '
   'files\\python\\lib\\site-packages\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib_metadata._collections',
   'c:\\program '
   'files\\python\\lib\\site-packages\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('importlib_metadata._compat',
   'c:\\program '
   'files\\python\\lib\\site-packages\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('importlib_metadata._functools',
   'c:\\program '
   'files\\python\\lib\\site-packages\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('importlib_metadata._itertools',
   'c:\\program '
   'files\\python\\lib\\site-packages\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib_metadata._meta',
   'c:\\program '
   'files\\python\\lib\\site-packages\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('importlib_metadata._text',
   'c:\\program '
   'files\\python\\lib\\site-packages\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('importlib_metadata.compat',
   'c:\\program '
   'files\\python\\lib\\site-packages\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('importlib_metadata.compat.py311',
   'c:\\program '
   'files\\python\\lib\\site-packages\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('importlib_metadata.compat.py39',
   'c:\\program '
   'files\\python\\lib\\site-packages\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('importlib_resources',
   'c:\\program '
   'files\\python\\lib\\site-packages\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('importlib_resources._adapters',
   'c:\\program '
   'files\\python\\lib\\site-packages\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('importlib_resources._common',
   'c:\\program '
   'files\\python\\lib\\site-packages\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('importlib_resources._functional',
   'c:\\program '
   'files\\python\\lib\\site-packages\\importlib_resources\\_functional.py',
   'PYMODULE'),
  ('importlib_resources._itertools',
   'c:\\program '
   'files\\python\\lib\\site-packages\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('importlib_resources.abc',
   'c:\\program files\\python\\lib\\site-packages\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('importlib_resources.compat',
   'c:\\program '
   'files\\python\\lib\\site-packages\\importlib_resources\\compat\\__init__.py',
   'PYMODULE'),
  ('importlib_resources.compat.py38',
   'c:\\program '
   'files\\python\\lib\\site-packages\\importlib_resources\\compat\\py38.py',
   'PYMODULE'),
  ('importlib_resources.compat.py39',
   'c:\\program '
   'files\\python\\lib\\site-packages\\importlib_resources\\compat\\py39.py',
   'PYMODULE'),
  ('importlib_resources.future',
   'c:\\program '
   'files\\python\\lib\\site-packages\\importlib_resources\\future\\__init__.py',
   'PYMODULE'),
  ('importlib_resources.future.adapters',
   'c:\\program '
   'files\\python\\lib\\site-packages\\importlib_resources\\future\\adapters.py',
   'PYMODULE'),
  ('importlib_resources.readers',
   'c:\\program '
   'files\\python\\lib\\site-packages\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('inspect', 'c:\\program files\\python\\lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'c:\\program files\\python\\lib\\ipaddress.py', 'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('jaraco.context',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('jaraco.functools',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('jaraco.text',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('jinja2',
   'c:\\program files\\python\\lib\\site-packages\\jinja2\\__init__.py',
   'PYMODULE'),
  ('jinja2._identifier',
   'c:\\program files\\python\\lib\\site-packages\\jinja2\\_identifier.py',
   'PYMODULE'),
  ('jinja2.async_utils',
   'c:\\program files\\python\\lib\\site-packages\\jinja2\\async_utils.py',
   'PYMODULE'),
  ('jinja2.bccache',
   'c:\\program files\\python\\lib\\site-packages\\jinja2\\bccache.py',
   'PYMODULE'),
  ('jinja2.compiler',
   'c:\\program files\\python\\lib\\site-packages\\jinja2\\compiler.py',
   'PYMODULE'),
  ('jinja2.constants',
   'c:\\program files\\python\\lib\\site-packages\\jinja2\\constants.py',
   'PYMODULE'),
  ('jinja2.debug',
   'c:\\program files\\python\\lib\\site-packages\\jinja2\\debug.py',
   'PYMODULE'),
  ('jinja2.defaults',
   'c:\\program files\\python\\lib\\site-packages\\jinja2\\defaults.py',
   'PYMODULE'),
  ('jinja2.environment',
   'c:\\program files\\python\\lib\\site-packages\\jinja2\\environment.py',
   'PYMODULE'),
  ('jinja2.exceptions',
   'c:\\program files\\python\\lib\\site-packages\\jinja2\\exceptions.py',
   'PYMODULE'),
  ('jinja2.ext',
   'c:\\program files\\python\\lib\\site-packages\\jinja2\\ext.py',
   'PYMODULE'),
  ('jinja2.filters',
   'c:\\program files\\python\\lib\\site-packages\\jinja2\\filters.py',
   'PYMODULE'),
  ('jinja2.idtracking',
   'c:\\program files\\python\\lib\\site-packages\\jinja2\\idtracking.py',
   'PYMODULE'),
  ('jinja2.lexer',
   'c:\\program files\\python\\lib\\site-packages\\jinja2\\lexer.py',
   'PYMODULE'),
  ('jinja2.loaders',
   'c:\\program files\\python\\lib\\site-packages\\jinja2\\loaders.py',
   'PYMODULE'),
  ('jinja2.nodes',
   'c:\\program files\\python\\lib\\site-packages\\jinja2\\nodes.py',
   'PYMODULE'),
  ('jinja2.optimizer',
   'c:\\program files\\python\\lib\\site-packages\\jinja2\\optimizer.py',
   'PYMODULE'),
  ('jinja2.parser',
   'c:\\program files\\python\\lib\\site-packages\\jinja2\\parser.py',
   'PYMODULE'),
  ('jinja2.runtime',
   'c:\\program files\\python\\lib\\site-packages\\jinja2\\runtime.py',
   'PYMODULE'),
  ('jinja2.sandbox',
   'c:\\program files\\python\\lib\\site-packages\\jinja2\\sandbox.py',
   'PYMODULE'),
  ('jinja2.tests',
   'c:\\program files\\python\\lib\\site-packages\\jinja2\\tests.py',
   'PYMODULE'),
  ('jinja2.utils',
   'c:\\program files\\python\\lib\\site-packages\\jinja2\\utils.py',
   'PYMODULE'),
  ('jinja2.visitor',
   'c:\\program files\\python\\lib\\site-packages\\jinja2\\visitor.py',
   'PYMODULE'),
  ('json', 'c:\\program files\\python\\lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder',
   'c:\\program files\\python\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'c:\\program files\\python\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'c:\\program files\\python\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('kiwisolver',
   'c:\\program files\\python\\lib\\site-packages\\kiwisolver\\__init__.py',
   'PYMODULE'),
  ('kiwisolver.exceptions',
   'c:\\program files\\python\\lib\\site-packages\\kiwisolver\\exceptions.py',
   'PYMODULE'),
  ('logger',
   'C:\\Users\\<USER>\\Desktop\\yl\\2.0\\logger.py',
   'PYMODULE'),
  ('logging',
   'c:\\program files\\python\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lxml',
   'c:\\program files\\python\\lib\\site-packages\\lxml\\__init__.py',
   'PYMODULE'),
  ('lxml.ElementInclude',
   'c:\\program files\\python\\lib\\site-packages\\lxml\\ElementInclude.py',
   'PYMODULE'),
  ('lxml.cssselect',
   'c:\\program files\\python\\lib\\site-packages\\lxml\\cssselect.py',
   'PYMODULE'),
  ('lxml.doctestcompare',
   'c:\\program files\\python\\lib\\site-packages\\lxml\\doctestcompare.py',
   'PYMODULE'),
  ('lxml.html',
   'c:\\program files\\python\\lib\\site-packages\\lxml\\html\\__init__.py',
   'PYMODULE'),
  ('lxml.html.ElementSoup',
   'c:\\program files\\python\\lib\\site-packages\\lxml\\html\\ElementSoup.py',
   'PYMODULE'),
  ('lxml.html._diffcommand',
   'c:\\program files\\python\\lib\\site-packages\\lxml\\html\\_diffcommand.py',
   'PYMODULE'),
  ('lxml.html._html5builder',
   'c:\\program '
   'files\\python\\lib\\site-packages\\lxml\\html\\_html5builder.py',
   'PYMODULE'),
  ('lxml.html._setmixin',
   'c:\\program files\\python\\lib\\site-packages\\lxml\\html\\_setmixin.py',
   'PYMODULE'),
  ('lxml.html.builder',
   'c:\\program files\\python\\lib\\site-packages\\lxml\\html\\builder.py',
   'PYMODULE'),
  ('lxml.html.defs',
   'c:\\program files\\python\\lib\\site-packages\\lxml\\html\\defs.py',
   'PYMODULE'),
  ('lxml.html.formfill',
   'c:\\program files\\python\\lib\\site-packages\\lxml\\html\\formfill.py',
   'PYMODULE'),
  ('lxml.html.html5parser',
   'c:\\program files\\python\\lib\\site-packages\\lxml\\html\\html5parser.py',
   'PYMODULE'),
  ('lxml.html.soupparser',
   'c:\\program files\\python\\lib\\site-packages\\lxml\\html\\soupparser.py',
   'PYMODULE'),
  ('lxml.html.usedoctest',
   'c:\\program files\\python\\lib\\site-packages\\lxml\\html\\usedoctest.py',
   'PYMODULE'),
  ('lxml.includes',
   'c:\\program files\\python\\lib\\site-packages\\lxml\\includes\\__init__.py',
   'PYMODULE'),
  ('lxml.isoschematron',
   'c:\\program '
   'files\\python\\lib\\site-packages\\lxml\\isoschematron\\__init__.py',
   'PYMODULE'),
  ('lxml.pyclasslookup',
   'c:\\program files\\python\\lib\\site-packages\\lxml\\pyclasslookup.py',
   'PYMODULE'),
  ('lxml.usedoctest',
   'c:\\program files\\python\\lib\\site-packages\\lxml\\usedoctest.py',
   'PYMODULE'),
  ('lzma', 'c:\\program files\\python\\lib\\lzma.py', 'PYMODULE'),
  ('main_gui',
   'C:\\Users\\<USER>\\Desktop\\yl\\2.0\\main_gui.py',
   'PYMODULE'),
  ('markupsafe',
   'c:\\program files\\python\\lib\\site-packages\\markupsafe\\__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   'c:\\program files\\python\\lib\\site-packages\\markupsafe\\_native.py',
   'PYMODULE'),
  ('matplotlib',
   'c:\\program files\\python\\lib\\site-packages\\matplotlib\\__init__.py',
   'PYMODULE'),
  ('matplotlib._afm',
   'c:\\program files\\python\\lib\\site-packages\\matplotlib\\_afm.py',
   'PYMODULE'),
  ('matplotlib._api',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\_api\\__init__.py',
   'PYMODULE'),
  ('matplotlib._api.deprecation',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\_api\\deprecation.py',
   'PYMODULE'),
  ('matplotlib._blocking_input',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\_blocking_input.py',
   'PYMODULE'),
  ('matplotlib._cm',
   'c:\\program files\\python\\lib\\site-packages\\matplotlib\\_cm.py',
   'PYMODULE'),
  ('matplotlib._cm_listed',
   'c:\\program files\\python\\lib\\site-packages\\matplotlib\\_cm_listed.py',
   'PYMODULE'),
  ('matplotlib._color_data',
   'c:\\program files\\python\\lib\\site-packages\\matplotlib\\_color_data.py',
   'PYMODULE'),
  ('matplotlib._constrained_layout',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\_constrained_layout.py',
   'PYMODULE'),
  ('matplotlib._docstring',
   'c:\\program files\\python\\lib\\site-packages\\matplotlib\\_docstring.py',
   'PYMODULE'),
  ('matplotlib._enums',
   'c:\\program files\\python\\lib\\site-packages\\matplotlib\\_enums.py',
   'PYMODULE'),
  ('matplotlib._fontconfig_pattern',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\_fontconfig_pattern.py',
   'PYMODULE'),
  ('matplotlib._layoutgrid',
   'c:\\program files\\python\\lib\\site-packages\\matplotlib\\_layoutgrid.py',
   'PYMODULE'),
  ('matplotlib._mathtext',
   'c:\\program files\\python\\lib\\site-packages\\matplotlib\\_mathtext.py',
   'PYMODULE'),
  ('matplotlib._mathtext_data',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\_mathtext_data.py',
   'PYMODULE'),
  ('matplotlib._pylab_helpers',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\_pylab_helpers.py',
   'PYMODULE'),
  ('matplotlib._text_helpers',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\_text_helpers.py',
   'PYMODULE'),
  ('matplotlib._tight_bbox',
   'c:\\program files\\python\\lib\\site-packages\\matplotlib\\_tight_bbox.py',
   'PYMODULE'),
  ('matplotlib._tight_layout',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\_tight_layout.py',
   'PYMODULE'),
  ('matplotlib._version',
   'c:\\program files\\python\\lib\\site-packages\\matplotlib\\_version.py',
   'PYMODULE'),
  ('matplotlib.artist',
   'c:\\program files\\python\\lib\\site-packages\\matplotlib\\artist.py',
   'PYMODULE'),
  ('matplotlib.axes',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\axes\\__init__.py',
   'PYMODULE'),
  ('matplotlib.axes._axes',
   'c:\\program files\\python\\lib\\site-packages\\matplotlib\\axes\\_axes.py',
   'PYMODULE'),
  ('matplotlib.axes._base',
   'c:\\program files\\python\\lib\\site-packages\\matplotlib\\axes\\_base.py',
   'PYMODULE'),
  ('matplotlib.axes._secondary_axes',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\axes\\_secondary_axes.py',
   'PYMODULE'),
  ('matplotlib.axis',
   'c:\\program files\\python\\lib\\site-packages\\matplotlib\\axis.py',
   'PYMODULE'),
  ('matplotlib.backend_bases',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\backend_bases.py',
   'PYMODULE'),
  ('matplotlib.backend_managers',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\backend_managers.py',
   'PYMODULE'),
  ('matplotlib.backend_tools',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\backend_tools.py',
   'PYMODULE'),
  ('matplotlib.backends',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\backends\\__init__.py',
   'PYMODULE'),
  ('matplotlib.backends._backend_tk',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\backends\\_backend_tk.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_agg',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\backends\\backend_agg.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_tkagg',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\backends\\backend_tkagg.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_webagg',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\backends\\backend_webagg.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_webagg_core',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\backends\\backend_webagg_core.py',
   'PYMODULE'),
  ('matplotlib.bezier',
   'c:\\program files\\python\\lib\\site-packages\\matplotlib\\bezier.py',
   'PYMODULE'),
  ('matplotlib.category',
   'c:\\program files\\python\\lib\\site-packages\\matplotlib\\category.py',
   'PYMODULE'),
  ('matplotlib.cbook',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\cbook\\__init__.py',
   'PYMODULE'),
  ('matplotlib.cm',
   'c:\\program files\\python\\lib\\site-packages\\matplotlib\\cm.py',
   'PYMODULE'),
  ('matplotlib.collections',
   'c:\\program files\\python\\lib\\site-packages\\matplotlib\\collections.py',
   'PYMODULE'),
  ('matplotlib.colorbar',
   'c:\\program files\\python\\lib\\site-packages\\matplotlib\\colorbar.py',
   'PYMODULE'),
  ('matplotlib.colors',
   'c:\\program files\\python\\lib\\site-packages\\matplotlib\\colors.py',
   'PYMODULE'),
  ('matplotlib.container',
   'c:\\program files\\python\\lib\\site-packages\\matplotlib\\container.py',
   'PYMODULE'),
  ('matplotlib.contour',
   'c:\\program files\\python\\lib\\site-packages\\matplotlib\\contour.py',
   'PYMODULE'),
  ('matplotlib.dates',
   'c:\\program files\\python\\lib\\site-packages\\matplotlib\\dates.py',
   'PYMODULE'),
  ('matplotlib.dviread',
   'c:\\program files\\python\\lib\\site-packages\\matplotlib\\dviread.py',
   'PYMODULE'),
  ('matplotlib.figure',
   'c:\\program files\\python\\lib\\site-packages\\matplotlib\\figure.py',
   'PYMODULE'),
  ('matplotlib.font_manager',
   'c:\\program files\\python\\lib\\site-packages\\matplotlib\\font_manager.py',
   'PYMODULE'),
  ('matplotlib.gridspec',
   'c:\\program files\\python\\lib\\site-packages\\matplotlib\\gridspec.py',
   'PYMODULE'),
  ('matplotlib.hatch',
   'c:\\program files\\python\\lib\\site-packages\\matplotlib\\hatch.py',
   'PYMODULE'),
  ('matplotlib.image',
   'c:\\program files\\python\\lib\\site-packages\\matplotlib\\image.py',
   'PYMODULE'),
  ('matplotlib.layout_engine',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\layout_engine.py',
   'PYMODULE'),
  ('matplotlib.legend',
   'c:\\program files\\python\\lib\\site-packages\\matplotlib\\legend.py',
   'PYMODULE'),
  ('matplotlib.legend_handler',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\legend_handler.py',
   'PYMODULE'),
  ('matplotlib.lines',
   'c:\\program files\\python\\lib\\site-packages\\matplotlib\\lines.py',
   'PYMODULE'),
  ('matplotlib.markers',
   'c:\\program files\\python\\lib\\site-packages\\matplotlib\\markers.py',
   'PYMODULE'),
  ('matplotlib.mathtext',
   'c:\\program files\\python\\lib\\site-packages\\matplotlib\\mathtext.py',
   'PYMODULE'),
  ('matplotlib.mlab',
   'c:\\program files\\python\\lib\\site-packages\\matplotlib\\mlab.py',
   'PYMODULE'),
  ('matplotlib.offsetbox',
   'c:\\program files\\python\\lib\\site-packages\\matplotlib\\offsetbox.py',
   'PYMODULE'),
  ('matplotlib.patches',
   'c:\\program files\\python\\lib\\site-packages\\matplotlib\\patches.py',
   'PYMODULE'),
  ('matplotlib.path',
   'c:\\program files\\python\\lib\\site-packages\\matplotlib\\path.py',
   'PYMODULE'),
  ('matplotlib.patheffects',
   'c:\\program files\\python\\lib\\site-packages\\matplotlib\\patheffects.py',
   'PYMODULE'),
  ('matplotlib.projections',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\projections\\__init__.py',
   'PYMODULE'),
  ('matplotlib.projections.geo',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\projections\\geo.py',
   'PYMODULE'),
  ('matplotlib.projections.polar',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\projections\\polar.py',
   'PYMODULE'),
  ('matplotlib.pylab',
   'c:\\program files\\python\\lib\\site-packages\\matplotlib\\pylab.py',
   'PYMODULE'),
  ('matplotlib.pyplot',
   'c:\\program files\\python\\lib\\site-packages\\matplotlib\\pyplot.py',
   'PYMODULE'),
  ('matplotlib.quiver',
   'c:\\program files\\python\\lib\\site-packages\\matplotlib\\quiver.py',
   'PYMODULE'),
  ('matplotlib.rcsetup',
   'c:\\program files\\python\\lib\\site-packages\\matplotlib\\rcsetup.py',
   'PYMODULE'),
  ('matplotlib.scale',
   'c:\\program files\\python\\lib\\site-packages\\matplotlib\\scale.py',
   'PYMODULE'),
  ('matplotlib.spines',
   'c:\\program files\\python\\lib\\site-packages\\matplotlib\\spines.py',
   'PYMODULE'),
  ('matplotlib.stackplot',
   'c:\\program files\\python\\lib\\site-packages\\matplotlib\\stackplot.py',
   'PYMODULE'),
  ('matplotlib.streamplot',
   'c:\\program files\\python\\lib\\site-packages\\matplotlib\\streamplot.py',
   'PYMODULE'),
  ('matplotlib.style',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\style\\__init__.py',
   'PYMODULE'),
  ('matplotlib.style.core',
   'c:\\program files\\python\\lib\\site-packages\\matplotlib\\style\\core.py',
   'PYMODULE'),
  ('matplotlib.table',
   'c:\\program files\\python\\lib\\site-packages\\matplotlib\\table.py',
   'PYMODULE'),
  ('matplotlib.texmanager',
   'c:\\program files\\python\\lib\\site-packages\\matplotlib\\texmanager.py',
   'PYMODULE'),
  ('matplotlib.text',
   'c:\\program files\\python\\lib\\site-packages\\matplotlib\\text.py',
   'PYMODULE'),
  ('matplotlib.textpath',
   'c:\\program files\\python\\lib\\site-packages\\matplotlib\\textpath.py',
   'PYMODULE'),
  ('matplotlib.ticker',
   'c:\\program files\\python\\lib\\site-packages\\matplotlib\\ticker.py',
   'PYMODULE'),
  ('matplotlib.transforms',
   'c:\\program files\\python\\lib\\site-packages\\matplotlib\\transforms.py',
   'PYMODULE'),
  ('matplotlib.tri',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\tri\\__init__.py',
   'PYMODULE'),
  ('matplotlib.tri._triangulation',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\tri\\_triangulation.py',
   'PYMODULE'),
  ('matplotlib.tri._tricontour',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\tri\\_tricontour.py',
   'PYMODULE'),
  ('matplotlib.tri._trifinder',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\tri\\_trifinder.py',
   'PYMODULE'),
  ('matplotlib.tri._triinterpolate',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\tri\\_triinterpolate.py',
   'PYMODULE'),
  ('matplotlib.tri._tripcolor',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\tri\\_tripcolor.py',
   'PYMODULE'),
  ('matplotlib.tri._triplot',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\tri\\_triplot.py',
   'PYMODULE'),
  ('matplotlib.tri._trirefine',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\tri\\_trirefine.py',
   'PYMODULE'),
  ('matplotlib.tri._tritools',
   'c:\\program '
   'files\\python\\lib\\site-packages\\matplotlib\\tri\\_tritools.py',
   'PYMODULE'),
  ('matplotlib.units',
   'c:\\program files\\python\\lib\\site-packages\\matplotlib\\units.py',
   'PYMODULE'),
  ('matplotlib.widgets',
   'c:\\program files\\python\\lib\\site-packages\\matplotlib\\widgets.py',
   'PYMODULE'),
  ('mimetypes', 'c:\\program files\\python\\lib\\mimetypes.py', 'PYMODULE'),
  ('more_itertools',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('more_itertools.more',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('more_itertools.recipes',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('mpl_toolkits',
   'c:\\program files\\python\\lib\\site-packages\\mpl_toolkits\\__init__.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d',
   'c:\\program '
   'files\\python\\lib\\site-packages\\mpl_toolkits\\mplot3d\\__init__.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.art3d',
   'c:\\program '
   'files\\python\\lib\\site-packages\\mpl_toolkits\\mplot3d\\art3d.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.axes3d',
   'c:\\program '
   'files\\python\\lib\\site-packages\\mpl_toolkits\\mplot3d\\axes3d.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.axis3d',
   'c:\\program '
   'files\\python\\lib\\site-packages\\mpl_toolkits\\mplot3d\\axis3d.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.proj3d',
   'c:\\program '
   'files\\python\\lib\\site-packages\\mpl_toolkits\\mplot3d\\proj3d.py',
   'PYMODULE'),
  ('multiprocessing',
   'c:\\program files\\python\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'c:\\program files\\python\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'c:\\program files\\python\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'c:\\program files\\python\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'c:\\program files\\python\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'c:\\program files\\python\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'c:\\program files\\python\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'c:\\program files\\python\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'c:\\program files\\python\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'c:\\program files\\python\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'c:\\program files\\python\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'c:\\program files\\python\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'c:\\program files\\python\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'c:\\program files\\python\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'c:\\program files\\python\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'c:\\program files\\python\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'c:\\program files\\python\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'c:\\program files\\python\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'c:\\program files\\python\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'c:\\program files\\python\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'c:\\program files\\python\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'c:\\program files\\python\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'c:\\program files\\python\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netbios',
   'c:\\program files\\python\\lib\\site-packages\\win32\\lib\\netbios.py',
   'PYMODULE'),
  ('netrc', 'c:\\program files\\python\\lib\\netrc.py', 'PYMODULE'),
  ('nturl2path', 'c:\\program files\\python\\lib\\nturl2path.py', 'PYMODULE'),
  ('numbers', 'c:\\program files\\python\\lib\\numbers.py', 'PYMODULE'),
  ('numpy',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._globals',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._generic_alias',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\_typing\\_generic_alias.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._version',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\_version.py',
   'PYMODULE'),
  ('numpy.array_api',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\array_api\\__init__.py',
   'PYMODULE'),
  ('numpy.array_api._array_object',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\array_api\\_array_object.py',
   'PYMODULE'),
  ('numpy.array_api._constants',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\array_api\\_constants.py',
   'PYMODULE'),
  ('numpy.array_api._creation_functions',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\array_api\\_creation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._data_type_functions',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\array_api\\_data_type_functions.py',
   'PYMODULE'),
  ('numpy.array_api._dtypes',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\array_api\\_dtypes.py',
   'PYMODULE'),
  ('numpy.array_api._elementwise_functions',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\array_api\\_elementwise_functions.py',
   'PYMODULE'),
  ('numpy.array_api._manipulation_functions',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\array_api\\_manipulation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._searching_functions',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\array_api\\_searching_functions.py',
   'PYMODULE'),
  ('numpy.array_api._set_functions',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\array_api\\_set_functions.py',
   'PYMODULE'),
  ('numpy.array_api._sorting_functions',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\array_api\\_sorting_functions.py',
   'PYMODULE'),
  ('numpy.array_api._statistical_functions',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\array_api\\_statistical_functions.py',
   'PYMODULE'),
  ('numpy.array_api._typing',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\array_api\\_typing.py',
   'PYMODULE'),
  ('numpy.array_api._utility_functions',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\array_api\\_utility_functions.py',
   'PYMODULE'),
  ('numpy.array_api.linalg',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\array_api\\linalg.py',
   'PYMODULE'),
  ('numpy.compat',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\compat\\__init__.py',
   'PYMODULE'),
  ('numpy.compat._inspect',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\compat\\_inspect.py',
   'PYMODULE'),
  ('numpy.compat.py3k',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\compat\\py3k.py',
   'PYMODULE'),
  ('numpy.core',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs_scalars',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy.core._asarray',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\core\\_asarray.py',
   'PYMODULE'),
  ('numpy.core._dtype',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\core\\_dtype.py',
   'PYMODULE'),
  ('numpy.core._dtype_ctypes',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy.core._exceptions',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\core\\_exceptions.py',
   'PYMODULE'),
  ('numpy.core._internal',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\core\\_internal.py',
   'PYMODULE'),
  ('numpy.core._machar',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\core\\_machar.py',
   'PYMODULE'),
  ('numpy.core._methods',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\core\\_methods.py',
   'PYMODULE'),
  ('numpy.core._string_helpers',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy.core._type_aliases',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy.core._ufunc_config',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy.core.arrayprint',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\core\\arrayprint.py',
   'PYMODULE'),
  ('numpy.core.defchararray',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\core\\defchararray.py',
   'PYMODULE'),
  ('numpy.core.einsumfunc',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.core.fromnumeric',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy.core.function_base',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\core\\function_base.py',
   'PYMODULE'),
  ('numpy.core.getlimits',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\core\\getlimits.py',
   'PYMODULE'),
  ('numpy.core.memmap',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\core\\memmap.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\core\\numeric.py',
   'PYMODULE'),
  ('numpy.core.numerictypes',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\core\\numerictypes.py',
   'PYMODULE'),
  ('numpy.core.overrides',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\core\\overrides.py',
   'PYMODULE'),
  ('numpy.core.records',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\core\\records.py',
   'PYMODULE'),
  ('numpy.core.shape_base',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\core\\shape_base.py',
   'PYMODULE'),
  ('numpy.core.umath',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\core\\umath.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.fft',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.arraypad',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\lib\\arraypad.py',
   'PYMODULE'),
  ('numpy.lib.arraysetops',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\lib\\arraysetops.py',
   'PYMODULE'),
  ('numpy.lib.arrayterator',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\lib\\arrayterator.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.function_base',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\lib\\function_base.py',
   'PYMODULE'),
  ('numpy.lib.histograms',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\lib\\histograms.py',
   'PYMODULE'),
  ('numpy.lib.index_tricks',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\lib\\index_tricks.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.nanfunctions',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\lib\\nanfunctions.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.polynomial',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\lib\\polynomial.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.shape_base',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\lib\\shape_base.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.lib.twodim_base',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\lib\\twodim_base.py',
   'PYMODULE'),
  ('numpy.lib.type_check',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\lib\\type_check.py',
   'PYMODULE'),
  ('numpy.lib.ufunclike',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\lib\\ufunclike.py',
   'PYMODULE'),
  ('numpy.lib.utils',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\lib\\utils.py',
   'PYMODULE'),
  ('numpy.linalg',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.testing',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.decorators',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\testing\\_private\\decorators.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.noseclasses',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\testing\\_private\\noseclasses.py',
   'PYMODULE'),
  ('numpy.testing._private.nosetester',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\testing\\_private\\nosetester.py',
   'PYMODULE'),
  ('numpy.testing._private.parameterized',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\testing\\_private\\parameterized.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'c:\\program '
   'files\\python\\lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.typing',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'c:\\program files\\python\\lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode', 'c:\\program files\\python\\lib\\opcode.py', 'PYMODULE'),
  ('optparse', 'c:\\program files\\python\\lib\\optparse.py', 'PYMODULE'),
  ('packaging',
   'c:\\program files\\python\\lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'c:\\program files\\python\\lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'c:\\program files\\python\\lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'c:\\program files\\python\\lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'c:\\program files\\python\\lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'c:\\program files\\python\\lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'c:\\program files\\python\\lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   'c:\\program '
   'files\\python\\lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'c:\\program '
   'files\\python\\lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   'c:\\program files\\python\\lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.metadata',
   'c:\\program files\\python\\lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging.requirements',
   'c:\\program files\\python\\lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'c:\\program files\\python\\lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'c:\\program files\\python\\lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'c:\\program files\\python\\lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'c:\\program files\\python\\lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pandas',
   'c:\\program files\\python\\lib\\site-packages\\pandas\\__init__.py',
   'PYMODULE'),
  ('pandas._config',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\_config\\__init__.py',
   'PYMODULE'),
  ('pandas._config.config',
   'c:\\program files\\python\\lib\\site-packages\\pandas\\_config\\config.py',
   'PYMODULE'),
  ('pandas._config.dates',
   'c:\\program files\\python\\lib\\site-packages\\pandas\\_config\\dates.py',
   'PYMODULE'),
  ('pandas._config.display',
   'c:\\program files\\python\\lib\\site-packages\\pandas\\_config\\display.py',
   'PYMODULE'),
  ('pandas._config.localization',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\_config\\localization.py',
   'PYMODULE'),
  ('pandas._libs',
   'c:\\program files\\python\\lib\\site-packages\\pandas\\_libs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.tslibs',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\_libs\\tslibs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.window',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\_libs\\window\\__init__.py',
   'PYMODULE'),
  ('pandas._testing',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\_testing\\__init__.py',
   'PYMODULE'),
  ('pandas._testing._io',
   'c:\\program files\\python\\lib\\site-packages\\pandas\\_testing\\_io.py',
   'PYMODULE'),
  ('pandas._testing._random',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\_testing\\_random.py',
   'PYMODULE'),
  ('pandas._testing._warnings',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\_testing\\_warnings.py',
   'PYMODULE'),
  ('pandas._testing.asserters',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\_testing\\asserters.py',
   'PYMODULE'),
  ('pandas._testing.compat',
   'c:\\program files\\python\\lib\\site-packages\\pandas\\_testing\\compat.py',
   'PYMODULE'),
  ('pandas._testing.contexts',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\_testing\\contexts.py',
   'PYMODULE'),
  ('pandas._typing',
   'c:\\program files\\python\\lib\\site-packages\\pandas\\_typing.py',
   'PYMODULE'),
  ('pandas._version',
   'c:\\program files\\python\\lib\\site-packages\\pandas\\_version.py',
   'PYMODULE'),
  ('pandas.api',
   'c:\\program files\\python\\lib\\site-packages\\pandas\\api\\__init__.py',
   'PYMODULE'),
  ('pandas.api.extensions',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\api\\extensions\\__init__.py',
   'PYMODULE'),
  ('pandas.api.indexers',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\api\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.api.interchange',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\api\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.api.types',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\api\\types\\__init__.py',
   'PYMODULE'),
  ('pandas.arrays',
   'c:\\program files\\python\\lib\\site-packages\\pandas\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.compat',
   'c:\\program files\\python\\lib\\site-packages\\pandas\\compat\\__init__.py',
   'PYMODULE'),
  ('pandas.compat._constants',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\compat\\_constants.py',
   'PYMODULE'),
  ('pandas.compat._optional',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\compat\\_optional.py',
   'PYMODULE'),
  ('pandas.compat.compressors',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\compat\\compressors.py',
   'PYMODULE'),
  ('pandas.compat.numpy',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\compat\\numpy\\__init__.py',
   'PYMODULE'),
  ('pandas.compat.numpy.function',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\compat\\numpy\\function.py',
   'PYMODULE'),
  ('pandas.compat.pickle_compat',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\compat\\pickle_compat.py',
   'PYMODULE'),
  ('pandas.compat.pyarrow',
   'c:\\program files\\python\\lib\\site-packages\\pandas\\compat\\pyarrow.py',
   'PYMODULE'),
  ('pandas.core',
   'c:\\program files\\python\\lib\\site-packages\\pandas\\core\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\_numba\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.executor',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\_numba\\executor.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.mean_',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\mean_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.min_max_',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\min_max_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.shared',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\shared.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.sum_',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\sum_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.var_',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\var_.py',
   'PYMODULE'),
  ('pandas.core.accessor',
   'c:\\program files\\python\\lib\\site-packages\\pandas\\core\\accessor.py',
   'PYMODULE'),
  ('pandas.core.algorithms',
   'c:\\program files\\python\\lib\\site-packages\\pandas\\core\\algorithms.py',
   'PYMODULE'),
  ('pandas.core.api',
   'c:\\program files\\python\\lib\\site-packages\\pandas\\core\\api.py',
   'PYMODULE'),
  ('pandas.core.apply',
   'c:\\program files\\python\\lib\\site-packages\\pandas\\core\\apply.py',
   'PYMODULE'),
  ('pandas.core.array_algos',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\array_algos\\__init__.py',
   'PYMODULE'),
  ('pandas.core.array_algos.datetimelike_accumulations',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\array_algos\\datetimelike_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_accumulations',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\array_algos\\masked_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_reductions',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\array_algos\\masked_reductions.py',
   'PYMODULE'),
  ('pandas.core.array_algos.putmask',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\array_algos\\putmask.py',
   'PYMODULE'),
  ('pandas.core.array_algos.quantile',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\array_algos\\quantile.py',
   'PYMODULE'),
  ('pandas.core.array_algos.replace',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\array_algos\\replace.py',
   'PYMODULE'),
  ('pandas.core.array_algos.take',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\array_algos\\take.py',
   'PYMODULE'),
  ('pandas.core.array_algos.transforms',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\array_algos\\transforms.py',
   'PYMODULE'),
  ('pandas.core.arraylike',
   'c:\\program files\\python\\lib\\site-packages\\pandas\\core\\arraylike.py',
   'PYMODULE'),
  ('pandas.core.arrays',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays._mixins',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\arrays\\_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._ranges',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\arrays\\_ranges.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow._arrow_utils',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\_arrow_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.array',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.dtype',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\dtype.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.extension_types',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\extension_types.py',
   'PYMODULE'),
  ('pandas.core.arrays.base',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\arrays\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays.boolean',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\arrays\\boolean.py',
   'PYMODULE'),
  ('pandas.core.arrays.categorical',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\arrays\\categorical.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimelike',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\arrays\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimes',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\arrays\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays.floating',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\arrays\\floating.py',
   'PYMODULE'),
  ('pandas.core.arrays.integer',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\arrays\\integer.py',
   'PYMODULE'),
  ('pandas.core.arrays.interval',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\arrays\\interval.py',
   'PYMODULE'),
  ('pandas.core.arrays.masked',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\arrays\\masked.py',
   'PYMODULE'),
  ('pandas.core.arrays.numeric',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\arrays\\numeric.py',
   'PYMODULE'),
  ('pandas.core.arrays.numpy_',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\arrays\\numpy_.py',
   'PYMODULE'),
  ('pandas.core.arrays.period',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\arrays\\period.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.accessor',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\accessor.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.array',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.dtype',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\dtype.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\scipy_sparse.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\arrays\\string_.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_arrow',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\arrays\\string_arrow.py',
   'PYMODULE'),
  ('pandas.core.arrays.timedeltas',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\arrays\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.base',
   'c:\\program files\\python\\lib\\site-packages\\pandas\\core\\base.py',
   'PYMODULE'),
  ('pandas.core.common',
   'c:\\program files\\python\\lib\\site-packages\\pandas\\core\\common.py',
   'PYMODULE'),
  ('pandas.core.computation',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\computation\\__init__.py',
   'PYMODULE'),
  ('pandas.core.computation.align',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\computation\\align.py',
   'PYMODULE'),
  ('pandas.core.computation.api',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\computation\\api.py',
   'PYMODULE'),
  ('pandas.core.computation.check',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\computation\\check.py',
   'PYMODULE'),
  ('pandas.core.computation.common',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\computation\\common.py',
   'PYMODULE'),
  ('pandas.core.computation.engines',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\computation\\engines.py',
   'PYMODULE'),
  ('pandas.core.computation.eval',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\computation\\eval.py',
   'PYMODULE'),
  ('pandas.core.computation.expr',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\computation\\expr.py',
   'PYMODULE'),
  ('pandas.core.computation.expressions',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\computation\\expressions.py',
   'PYMODULE'),
  ('pandas.core.computation.ops',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\computation\\ops.py',
   'PYMODULE'),
  ('pandas.core.computation.parsing',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\computation\\parsing.py',
   'PYMODULE'),
  ('pandas.core.computation.pytables',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\computation\\pytables.py',
   'PYMODULE'),
  ('pandas.core.computation.scope',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\computation\\scope.py',
   'PYMODULE'),
  ('pandas.core.config_init',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\config_init.py',
   'PYMODULE'),
  ('pandas.core.construction',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\construction.py',
   'PYMODULE'),
  ('pandas.core.dtypes',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\dtypes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.dtypes.api',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\dtypes\\api.py',
   'PYMODULE'),
  ('pandas.core.dtypes.astype',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\dtypes\\astype.py',
   'PYMODULE'),
  ('pandas.core.dtypes.base',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\dtypes\\base.py',
   'PYMODULE'),
  ('pandas.core.dtypes.cast',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\dtypes\\cast.py',
   'PYMODULE'),
  ('pandas.core.dtypes.common',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\dtypes\\common.py',
   'PYMODULE'),
  ('pandas.core.dtypes.concat',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\dtypes\\concat.py',
   'PYMODULE'),
  ('pandas.core.dtypes.dtypes',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\dtypes\\dtypes.py',
   'PYMODULE'),
  ('pandas.core.dtypes.generic',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\dtypes\\generic.py',
   'PYMODULE'),
  ('pandas.core.dtypes.inference',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\dtypes\\inference.py',
   'PYMODULE'),
  ('pandas.core.dtypes.missing',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\dtypes\\missing.py',
   'PYMODULE'),
  ('pandas.core.flags',
   'c:\\program files\\python\\lib\\site-packages\\pandas\\core\\flags.py',
   'PYMODULE'),
  ('pandas.core.frame',
   'c:\\program files\\python\\lib\\site-packages\\pandas\\core\\frame.py',
   'PYMODULE'),
  ('pandas.core.generic',
   'c:\\program files\\python\\lib\\site-packages\\pandas\\core\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\groupby\\__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.base',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\groupby\\base.py',
   'PYMODULE'),
  ('pandas.core.groupby.categorical',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\groupby\\categorical.py',
   'PYMODULE'),
  ('pandas.core.groupby.generic',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\groupby\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby.groupby',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\groupby\\groupby.py',
   'PYMODULE'),
  ('pandas.core.groupby.grouper',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\groupby\\grouper.py',
   'PYMODULE'),
  ('pandas.core.groupby.indexing',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\groupby\\indexing.py',
   'PYMODULE'),
  ('pandas.core.groupby.numba_',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\groupby\\numba_.py',
   'PYMODULE'),
  ('pandas.core.groupby.ops',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\groupby\\ops.py',
   'PYMODULE'),
  ('pandas.core.indexers',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexers.objects',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\indexers\\objects.py',
   'PYMODULE'),
  ('pandas.core.indexers.utils',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\indexers\\utils.py',
   'PYMODULE'),
  ('pandas.core.indexes',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\indexes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.accessors',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\indexes\\accessors.py',
   'PYMODULE'),
  ('pandas.core.indexes.api',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\indexes\\api.py',
   'PYMODULE'),
  ('pandas.core.indexes.base',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\indexes\\base.py',
   'PYMODULE'),
  ('pandas.core.indexes.category',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\indexes\\category.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimelike',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\indexes\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimes',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\indexes\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.indexes.extension',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\indexes\\extension.py',
   'PYMODULE'),
  ('pandas.core.indexes.frozen',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\indexes\\frozen.py',
   'PYMODULE'),
  ('pandas.core.indexes.interval',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\indexes\\interval.py',
   'PYMODULE'),
  ('pandas.core.indexes.multi',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\indexes\\multi.py',
   'PYMODULE'),
  ('pandas.core.indexes.period',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\indexes\\period.py',
   'PYMODULE'),
  ('pandas.core.indexes.range',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\indexes\\range.py',
   'PYMODULE'),
  ('pandas.core.indexes.timedeltas',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\indexes\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.indexing',
   'c:\\program files\\python\\lib\\site-packages\\pandas\\core\\indexing.py',
   'PYMODULE'),
  ('pandas.core.interchange',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.buffer',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\interchange\\buffer.py',
   'PYMODULE'),
  ('pandas.core.interchange.column',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\interchange\\column.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\interchange\\dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe_protocol',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\interchange\\dataframe_protocol.py',
   'PYMODULE'),
  ('pandas.core.interchange.from_dataframe',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\interchange\\from_dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.utils',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\interchange\\utils.py',
   'PYMODULE'),
  ('pandas.core.internals',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\internals\\__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.api',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\internals\\api.py',
   'PYMODULE'),
  ('pandas.core.internals.array_manager',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\internals\\array_manager.py',
   'PYMODULE'),
  ('pandas.core.internals.base',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\internals\\base.py',
   'PYMODULE'),
  ('pandas.core.internals.blocks',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\internals\\blocks.py',
   'PYMODULE'),
  ('pandas.core.internals.concat',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\internals\\concat.py',
   'PYMODULE'),
  ('pandas.core.internals.construction',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\internals\\construction.py',
   'PYMODULE'),
  ('pandas.core.internals.managers',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\internals\\managers.py',
   'PYMODULE'),
  ('pandas.core.internals.ops',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\internals\\ops.py',
   'PYMODULE'),
  ('pandas.core.methods',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.core.methods.describe',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\methods\\describe.py',
   'PYMODULE'),
  ('pandas.core.methods.selectn',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\methods\\selectn.py',
   'PYMODULE'),
  ('pandas.core.methods.to_dict',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\methods\\to_dict.py',
   'PYMODULE'),
  ('pandas.core.missing',
   'c:\\program files\\python\\lib\\site-packages\\pandas\\core\\missing.py',
   'PYMODULE'),
  ('pandas.core.nanops',
   'c:\\program files\\python\\lib\\site-packages\\pandas\\core\\nanops.py',
   'PYMODULE'),
  ('pandas.core.ops',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\ops\\__init__.py',
   'PYMODULE'),
  ('pandas.core.ops.array_ops',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\ops\\array_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.common',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\ops\\common.py',
   'PYMODULE'),
  ('pandas.core.ops.dispatch',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\ops\\dispatch.py',
   'PYMODULE'),
  ('pandas.core.ops.docstrings',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\ops\\docstrings.py',
   'PYMODULE'),
  ('pandas.core.ops.invalid',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\ops\\invalid.py',
   'PYMODULE'),
  ('pandas.core.ops.mask_ops',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\ops\\mask_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.methods',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\ops\\methods.py',
   'PYMODULE'),
  ('pandas.core.ops.missing',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\ops\\missing.py',
   'PYMODULE'),
  ('pandas.core.resample',
   'c:\\program files\\python\\lib\\site-packages\\pandas\\core\\resample.py',
   'PYMODULE'),
  ('pandas.core.reshape',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\reshape\\__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.api',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\reshape\\api.py',
   'PYMODULE'),
  ('pandas.core.reshape.concat',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\reshape\\concat.py',
   'PYMODULE'),
  ('pandas.core.reshape.encoding',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\reshape\\encoding.py',
   'PYMODULE'),
  ('pandas.core.reshape.melt',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\reshape\\melt.py',
   'PYMODULE'),
  ('pandas.core.reshape.merge',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\reshape\\merge.py',
   'PYMODULE'),
  ('pandas.core.reshape.pivot',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\reshape\\pivot.py',
   'PYMODULE'),
  ('pandas.core.reshape.reshape',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\reshape\\reshape.py',
   'PYMODULE'),
  ('pandas.core.reshape.tile',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\reshape\\tile.py',
   'PYMODULE'),
  ('pandas.core.reshape.util',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\reshape\\util.py',
   'PYMODULE'),
  ('pandas.core.roperator',
   'c:\\program files\\python\\lib\\site-packages\\pandas\\core\\roperator.py',
   'PYMODULE'),
  ('pandas.core.sample',
   'c:\\program files\\python\\lib\\site-packages\\pandas\\core\\sample.py',
   'PYMODULE'),
  ('pandas.core.series',
   'c:\\program files\\python\\lib\\site-packages\\pandas\\core\\series.py',
   'PYMODULE'),
  ('pandas.core.shared_docs',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\shared_docs.py',
   'PYMODULE'),
  ('pandas.core.sorting',
   'c:\\program files\\python\\lib\\site-packages\\pandas\\core\\sorting.py',
   'PYMODULE'),
  ('pandas.core.strings',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\strings\\__init__.py',
   'PYMODULE'),
  ('pandas.core.strings.accessor',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\strings\\accessor.py',
   'PYMODULE'),
  ('pandas.core.strings.base',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\strings\\base.py',
   'PYMODULE'),
  ('pandas.core.strings.object_array',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\strings\\object_array.py',
   'PYMODULE'),
  ('pandas.core.tools',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\tools\\__init__.py',
   'PYMODULE'),
  ('pandas.core.tools.datetimes',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\tools\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.tools.numeric',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\tools\\numeric.py',
   'PYMODULE'),
  ('pandas.core.tools.timedeltas',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\tools\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.tools.times',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\tools\\times.py',
   'PYMODULE'),
  ('pandas.core.util',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.core.util.hashing',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\util\\hashing.py',
   'PYMODULE'),
  ('pandas.core.util.numba_',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\util\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\window\\__init__.py',
   'PYMODULE'),
  ('pandas.core.window.common',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\window\\common.py',
   'PYMODULE'),
  ('pandas.core.window.doc',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\window\\doc.py',
   'PYMODULE'),
  ('pandas.core.window.ewm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\window\\ewm.py',
   'PYMODULE'),
  ('pandas.core.window.expanding',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\window\\expanding.py',
   'PYMODULE'),
  ('pandas.core.window.numba_',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\window\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window.online',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\window\\online.py',
   'PYMODULE'),
  ('pandas.core.window.rolling',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\core\\window\\rolling.py',
   'PYMODULE'),
  ('pandas.errors',
   'c:\\program files\\python\\lib\\site-packages\\pandas\\errors\\__init__.py',
   'PYMODULE'),
  ('pandas.io',
   'c:\\program files\\python\\lib\\site-packages\\pandas\\io\\__init__.py',
   'PYMODULE'),
  ('pandas.io._util',
   'c:\\program files\\python\\lib\\site-packages\\pandas\\io\\_util.py',
   'PYMODULE'),
  ('pandas.io.api',
   'c:\\program files\\python\\lib\\site-packages\\pandas\\io\\api.py',
   'PYMODULE'),
  ('pandas.io.clipboard',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\io\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pandas.io.clipboards',
   'c:\\program files\\python\\lib\\site-packages\\pandas\\io\\clipboards.py',
   'PYMODULE'),
  ('pandas.io.common',
   'c:\\program files\\python\\lib\\site-packages\\pandas\\io\\common.py',
   'PYMODULE'),
  ('pandas.io.excel',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\io\\excel\\__init__.py',
   'PYMODULE'),
  ('pandas.io.excel._base',
   'c:\\program files\\python\\lib\\site-packages\\pandas\\io\\excel\\_base.py',
   'PYMODULE'),
  ('pandas.io.excel._odfreader',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\io\\excel\\_odfreader.py',
   'PYMODULE'),
  ('pandas.io.excel._odswriter',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\io\\excel\\_odswriter.py',
   'PYMODULE'),
  ('pandas.io.excel._openpyxl',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\io\\excel\\_openpyxl.py',
   'PYMODULE'),
  ('pandas.io.excel._pyxlsb',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\io\\excel\\_pyxlsb.py',
   'PYMODULE'),
  ('pandas.io.excel._util',
   'c:\\program files\\python\\lib\\site-packages\\pandas\\io\\excel\\_util.py',
   'PYMODULE'),
  ('pandas.io.excel._xlrd',
   'c:\\program files\\python\\lib\\site-packages\\pandas\\io\\excel\\_xlrd.py',
   'PYMODULE'),
  ('pandas.io.excel._xlsxwriter',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\io\\excel\\_xlsxwriter.py',
   'PYMODULE'),
  ('pandas.io.feather_format',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\io\\feather_format.py',
   'PYMODULE'),
  ('pandas.io.formats',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\io\\formats\\__init__.py',
   'PYMODULE'),
  ('pandas.io.formats._color_data',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\io\\formats\\_color_data.py',
   'PYMODULE'),
  ('pandas.io.formats.console',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\io\\formats\\console.py',
   'PYMODULE'),
  ('pandas.io.formats.css',
   'c:\\program files\\python\\lib\\site-packages\\pandas\\io\\formats\\css.py',
   'PYMODULE'),
  ('pandas.io.formats.csvs',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\io\\formats\\csvs.py',
   'PYMODULE'),
  ('pandas.io.formats.excel',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\io\\formats\\excel.py',
   'PYMODULE'),
  ('pandas.io.formats.format',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\io\\formats\\format.py',
   'PYMODULE'),
  ('pandas.io.formats.html',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\io\\formats\\html.py',
   'PYMODULE'),
  ('pandas.io.formats.info',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\io\\formats\\info.py',
   'PYMODULE'),
  ('pandas.io.formats.latex',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\io\\formats\\latex.py',
   'PYMODULE'),
  ('pandas.io.formats.printing',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\io\\formats\\printing.py',
   'PYMODULE'),
  ('pandas.io.formats.string',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\io\\formats\\string.py',
   'PYMODULE'),
  ('pandas.io.formats.style',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\io\\formats\\style.py',
   'PYMODULE'),
  ('pandas.io.formats.style_render',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\io\\formats\\style_render.py',
   'PYMODULE'),
  ('pandas.io.formats.xml',
   'c:\\program files\\python\\lib\\site-packages\\pandas\\io\\formats\\xml.py',
   'PYMODULE'),
  ('pandas.io.gbq',
   'c:\\program files\\python\\lib\\site-packages\\pandas\\io\\gbq.py',
   'PYMODULE'),
  ('pandas.io.html',
   'c:\\program files\\python\\lib\\site-packages\\pandas\\io\\html.py',
   'PYMODULE'),
  ('pandas.io.json',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\io\\json\\__init__.py',
   'PYMODULE'),
  ('pandas.io.json._json',
   'c:\\program files\\python\\lib\\site-packages\\pandas\\io\\json\\_json.py',
   'PYMODULE'),
  ('pandas.io.json._normalize',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\io\\json\\_normalize.py',
   'PYMODULE'),
  ('pandas.io.json._table_schema',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\io\\json\\_table_schema.py',
   'PYMODULE'),
  ('pandas.io.orc',
   'c:\\program files\\python\\lib\\site-packages\\pandas\\io\\orc.py',
   'PYMODULE'),
  ('pandas.io.parquet',
   'c:\\program files\\python\\lib\\site-packages\\pandas\\io\\parquet.py',
   'PYMODULE'),
  ('pandas.io.parsers',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\io\\parsers\\__init__.py',
   'PYMODULE'),
  ('pandas.io.parsers.arrow_parser_wrapper',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\io\\parsers\\arrow_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.base_parser',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\io\\parsers\\base_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.c_parser_wrapper',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\io\\parsers\\c_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.python_parser',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\io\\parsers\\python_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.readers',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\io\\parsers\\readers.py',
   'PYMODULE'),
  ('pandas.io.pickle',
   'c:\\program files\\python\\lib\\site-packages\\pandas\\io\\pickle.py',
   'PYMODULE'),
  ('pandas.io.pytables',
   'c:\\program files\\python\\lib\\site-packages\\pandas\\io\\pytables.py',
   'PYMODULE'),
  ('pandas.io.sas',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\io\\sas\\__init__.py',
   'PYMODULE'),
  ('pandas.io.sas.sas7bdat',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\io\\sas\\sas7bdat.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_constants',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\io\\sas\\sas_constants.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_xport',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\io\\sas\\sas_xport.py',
   'PYMODULE'),
  ('pandas.io.sas.sasreader',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\io\\sas\\sasreader.py',
   'PYMODULE'),
  ('pandas.io.spss',
   'c:\\program files\\python\\lib\\site-packages\\pandas\\io\\spss.py',
   'PYMODULE'),
  ('pandas.io.sql',
   'c:\\program files\\python\\lib\\site-packages\\pandas\\io\\sql.py',
   'PYMODULE'),
  ('pandas.io.stata',
   'c:\\program files\\python\\lib\\site-packages\\pandas\\io\\stata.py',
   'PYMODULE'),
  ('pandas.io.xml',
   'c:\\program files\\python\\lib\\site-packages\\pandas\\io\\xml.py',
   'PYMODULE'),
  ('pandas.plotting',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\plotting\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._core',
   'c:\\program files\\python\\lib\\site-packages\\pandas\\plotting\\_core.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.boxplot',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\boxplot.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.converter',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\converter.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.core',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\core.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.groupby',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\groupby.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.hist',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\hist.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.misc',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\misc.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.style',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\style.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.timeseries',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\timeseries.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.tools',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\tools.py',
   'PYMODULE'),
  ('pandas.plotting._misc',
   'c:\\program files\\python\\lib\\site-packages\\pandas\\plotting\\_misc.py',
   'PYMODULE'),
  ('pandas.testing',
   'c:\\program files\\python\\lib\\site-packages\\pandas\\testing.py',
   'PYMODULE'),
  ('pandas.tseries',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\tseries\\__init__.py',
   'PYMODULE'),
  ('pandas.tseries.api',
   'c:\\program files\\python\\lib\\site-packages\\pandas\\tseries\\api.py',
   'PYMODULE'),
  ('pandas.tseries.frequencies',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\tseries\\frequencies.py',
   'PYMODULE'),
  ('pandas.tseries.offsets',
   'c:\\program files\\python\\lib\\site-packages\\pandas\\tseries\\offsets.py',
   'PYMODULE'),
  ('pandas.util',
   'c:\\program files\\python\\lib\\site-packages\\pandas\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.util._decorators',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\util\\_decorators.py',
   'PYMODULE'),
  ('pandas.util._exceptions',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\util\\_exceptions.py',
   'PYMODULE'),
  ('pandas.util._print_versions',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\util\\_print_versions.py',
   'PYMODULE'),
  ('pandas.util._str_methods',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\util\\_str_methods.py',
   'PYMODULE'),
  ('pandas.util._tester',
   'c:\\program files\\python\\lib\\site-packages\\pandas\\util\\_tester.py',
   'PYMODULE'),
  ('pandas.util._validators',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\util\\_validators.py',
   'PYMODULE'),
  ('pandas.util.version',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pandas\\util\\version\\__init__.py',
   'PYMODULE'),
  ('parameter_validator',
   'C:\\Users\\<USER>\\Desktop\\yl\\2.0\\parameter_validator.py',
   'PYMODULE'),
  ('pathlib', 'c:\\program files\\python\\lib\\pathlib.py', 'PYMODULE'),
  ('pdb', 'c:\\program files\\python\\lib\\pdb.py', 'PYMODULE'),
  ('pickle', 'c:\\program files\\python\\lib\\pickle.py', 'PYMODULE'),
  ('pkg_resources',
   'c:\\program files\\python\\lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('pkg_resources.py2_warn',
   'c:\\program files\\python\\lib\\site-packages\\pkg_resources\\py2_warn.py',
   'PYMODULE'),
  ('pkgutil', 'c:\\program files\\python\\lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'c:\\program files\\python\\lib\\platform.py', 'PYMODULE'),
  ('platformdirs',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('platformdirs.android',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\platformdirs\\android.py',
   'PYMODULE'),
  ('platformdirs.api',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\platformdirs\\api.py',
   'PYMODULE'),
  ('platformdirs.macos',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\platformdirs\\macos.py',
   'PYMODULE'),
  ('platformdirs.unix',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\platformdirs\\unix.py',
   'PYMODULE'),
  ('platformdirs.version',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\platformdirs\\version.py',
   'PYMODULE'),
  ('platformdirs.windows',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\platformdirs\\windows.py',
   'PYMODULE'),
  ('plistlib', 'c:\\program files\\python\\lib\\plistlib.py', 'PYMODULE'),
  ('pprint', 'c:\\program files\\python\\lib\\pprint.py', 'PYMODULE'),
  ('psutil',
   'c:\\program files\\python\\lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._common',
   'c:\\program files\\python\\lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('psutil._compat',
   'c:\\program files\\python\\lib\\site-packages\\psutil\\_compat.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'c:\\program files\\python\\lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('py_compile', 'c:\\program files\\python\\lib\\py_compile.py', 'PYMODULE'),
  ('pycparser',
   'c:\\program files\\python\\lib\\site-packages\\pycparser\\__init__.py',
   'PYMODULE'),
  ('pycparser.ast_transforms',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pycparser\\ast_transforms.py',
   'PYMODULE'),
  ('pycparser.c_ast',
   'c:\\program files\\python\\lib\\site-packages\\pycparser\\c_ast.py',
   'PYMODULE'),
  ('pycparser.c_lexer',
   'c:\\program files\\python\\lib\\site-packages\\pycparser\\c_lexer.py',
   'PYMODULE'),
  ('pycparser.c_parser',
   'c:\\program files\\python\\lib\\site-packages\\pycparser\\c_parser.py',
   'PYMODULE'),
  ('pycparser.lextab',
   'c:\\program files\\python\\lib\\site-packages\\pycparser\\lextab.py',
   'PYMODULE'),
  ('pycparser.ply',
   'c:\\program files\\python\\lib\\site-packages\\pycparser\\ply\\__init__.py',
   'PYMODULE'),
  ('pycparser.ply.lex',
   'c:\\program files\\python\\lib\\site-packages\\pycparser\\ply\\lex.py',
   'PYMODULE'),
  ('pycparser.ply.yacc',
   'c:\\program files\\python\\lib\\site-packages\\pycparser\\ply\\yacc.py',
   'PYMODULE'),
  ('pycparser.plyparser',
   'c:\\program files\\python\\lib\\site-packages\\pycparser\\plyparser.py',
   'PYMODULE'),
  ('pycparser.yacctab',
   'c:\\program files\\python\\lib\\site-packages\\pycparser\\yacctab.py',
   'PYMODULE'),
  ('pydoc', 'c:\\program files\\python\\lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data',
   'c:\\program files\\python\\lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'c:\\program files\\python\\lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pyparsing',
   'c:\\program files\\python\\lib\\site-packages\\pyparsing.py',
   'PYMODULE'),
  ('pythoncom',
   'c:\\program files\\python\\lib\\site-packages\\pythoncom.py',
   'PYMODULE'),
  ('pytz',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\__init__.py',
   'PYMODULE'),
  ('pytz.exceptions',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE'),
  ('pytz.lazy',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\lazy.py',
   'PYMODULE'),
  ('pytz.tzfile',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   'c:\\program files\\python\\lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE'),
  ('pywin',
   'c:\\program '
   'files\\python\\lib\\site-packages\\Pythonwin\\pywin\\__init__.py',
   'PYMODULE'),
  ('pywin.dialogs',
   'c:\\program '
   'files\\python\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\__init__.py',
   'PYMODULE'),
  ('pywin.dialogs.list',
   'c:\\program '
   'files\\python\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\list.py',
   'PYMODULE'),
  ('pywin.dialogs.status',
   'c:\\program '
   'files\\python\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\status.py',
   'PYMODULE'),
  ('pywin.mfc',
   'c:\\program '
   'files\\python\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\__init__.py',
   'PYMODULE'),
  ('pywin.mfc.dialog',
   'c:\\program '
   'files\\python\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\dialog.py',
   'PYMODULE'),
  ('pywin.mfc.object',
   'c:\\program '
   'files\\python\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\object.py',
   'PYMODULE'),
  ('pywin.mfc.thread',
   'c:\\program '
   'files\\python\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\thread.py',
   'PYMODULE'),
  ('pywin.mfc.window',
   'c:\\program '
   'files\\python\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\window.py',
   'PYMODULE'),
  ('pywintypes',
   'c:\\program files\\python\\lib\\site-packages\\win32\\lib\\pywintypes.py',
   'PYMODULE'),
  ('queue', 'c:\\program files\\python\\lib\\queue.py', 'PYMODULE'),
  ('quopri', 'c:\\program files\\python\\lib\\quopri.py', 'PYMODULE'),
  ('random', 'c:\\program files\\python\\lib\\random.py', 'PYMODULE'),
  ('requests',
   'c:\\program files\\python\\lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'c:\\program files\\python\\lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'c:\\program '
   'files\\python\\lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'c:\\program files\\python\\lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'c:\\program files\\python\\lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'c:\\program files\\python\\lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'c:\\program files\\python\\lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'c:\\program files\\python\\lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'c:\\program files\\python\\lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'c:\\program files\\python\\lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'c:\\program files\\python\\lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'c:\\program files\\python\\lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'c:\\program files\\python\\lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'c:\\program files\\python\\lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'c:\\program files\\python\\lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'c:\\program files\\python\\lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'c:\\program files\\python\\lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('risk_manager',
   'C:\\Users\\<USER>\\Desktop\\yl\\2.0\\risk_manager.py',
   'PYMODULE'),
  ('rlcompleter', 'c:\\program files\\python\\lib\\rlcompleter.py', 'PYMODULE'),
  ('runpy', 'c:\\program files\\python\\lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'c:\\program files\\python\\lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'c:\\program files\\python\\lib\\selectors.py', 'PYMODULE'),
  ('setuptools',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('setuptools._path',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._vendor.autocommand',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\autocommand\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.autocommand.autoasync',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\autocommand\\autoasync.py',
   'PYMODULE'),
  ('setuptools._vendor.autocommand.autocommand',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\autocommand\\autocommand.py',
   'PYMODULE'),
  ('setuptools._vendor.autocommand.automain',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\autocommand\\automain.py',
   'PYMODULE'),
  ('setuptools._vendor.autocommand.autoparse',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\autocommand\\autoparse.py',
   'PYMODULE'),
  ('setuptools._vendor.autocommand.errors',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\autocommand\\errors.py',
   'PYMODULE'),
  ('setuptools._vendor.backports',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.__main__',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__main__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.diagnose',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\diagnose.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._adapters',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._common',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._itertools',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.abc',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.compat',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.compat.py38',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.compat.py39',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.functional',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\functional.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.future',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\future\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.future.adapters',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\future\\adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.readers',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.simple',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\simple.py',
   'PYMODULE'),
  ('setuptools._vendor.inflect',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\inflect\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.inflect.compat',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\inflect\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.inflect.compat.py38',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\inflect\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.collections',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\collections\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text.layouts',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\layouts.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.metadata',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.__main__',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\platformdirs\\__main__.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.android',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\platformdirs\\android.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.api',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\platformdirs\\api.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.macos',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\platformdirs\\macos.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.unix',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\platformdirs\\unix.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.version',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\platformdirs\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.windows',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\platformdirs\\windows.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.typeguard',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\typeguard\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.typeguard._checkers',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\typeguard\\_checkers.py',
   'PYMODULE'),
  ('setuptools._vendor.typeguard._config',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\typeguard\\_config.py',
   'PYMODULE'),
  ('setuptools._vendor.typeguard._decorators',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\typeguard\\_decorators.py',
   'PYMODULE'),
  ('setuptools._vendor.typeguard._exceptions',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\typeguard\\_exceptions.py',
   'PYMODULE'),
  ('setuptools._vendor.typeguard._functions',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\typeguard\\_functions.py',
   'PYMODULE'),
  ('setuptools._vendor.typeguard._importhook',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\typeguard\\_importhook.py',
   'PYMODULE'),
  ('setuptools._vendor.typeguard._memo',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\typeguard\\_memo.py',
   'PYMODULE'),
  ('setuptools._vendor.typeguard._pytest_plugin',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\typeguard\\_pytest_plugin.py',
   'PYMODULE'),
  ('setuptools._vendor.typeguard._suppression',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\typeguard\\_suppression.py',
   'PYMODULE'),
  ('setuptools._vendor.typeguard._transformer',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\typeguard\\_transformer.py',
   'PYMODULE'),
  ('setuptools._vendor.typeguard._union_transformer',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\typeguard\\_union_transformer.py',
   'PYMODULE'),
  ('setuptools._vendor.typeguard._utils',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\typeguard\\_utils.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\wheel\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.__main__',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\wheel\\__main__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel._setuptools_logging',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\wheel\\_setuptools_logging.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.bdist_wheel',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\wheel\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\wheel\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\wheel\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.compat',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.config',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.glob',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex', 'c:\\program files\\python\\lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'c:\\program files\\python\\lib\\shutil.py', 'PYMODULE'),
  ('signal', 'c:\\program files\\python\\lib\\signal.py', 'PYMODULE'),
  ('site', 'c:\\program files\\python\\lib\\site.py', 'PYMODULE'),
  ('six', 'c:\\program files\\python\\lib\\site-packages\\six.py', 'PYMODULE'),
  ('socket', 'c:\\program files\\python\\lib\\socket.py', 'PYMODULE'),
  ('socketserver',
   'c:\\program files\\python\\lib\\socketserver.py',
   'PYMODULE'),
  ('sqlalchemy',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.connectors',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\connectors\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.connectors.mxodbc',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\connectors\\mxodbc.py',
   'PYMODULE'),
  ('sqlalchemy.connectors.pyodbc',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\connectors\\pyodbc.py',
   'PYMODULE'),
  ('sqlalchemy.connectors.zxJDBC',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\connectors\\zxJDBC.py',
   'PYMODULE'),
  ('sqlalchemy.dialects',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.firebird',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\firebird\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.firebird.base',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\firebird\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.firebird.fdb',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\firebird\\fdb.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.firebird.kinterbasdb',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\firebird\\kinterbasdb.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mssql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.adodbapi',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mssql\\adodbapi.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.base',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mssql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.information_schema',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mssql\\information_schema.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.mxodbc',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mssql\\mxodbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.pymssql',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mssql\\pymssql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.pyodbc',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mssql\\pyodbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.zxjdbc',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mssql\\zxjdbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.base',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.cymysql',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\cymysql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.dml',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\dml.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.enumerated',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\enumerated.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.gaerdbms',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\gaerdbms.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.json',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mysqlconnector',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\mysqlconnector.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mysqldb',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\mysqldb.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.oursql',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\oursql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.pymysql',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\pymysql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.pyodbc',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\pyodbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.reflection',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\reflection.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.types',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.zxjdbc',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\zxjdbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\oracle\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.base',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\oracle\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.cx_oracle',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\oracle\\cx_oracle.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.zxjdbc',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\oracle\\zxjdbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.array',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\array.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.base',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.dml',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\dml.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.ext',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\ext.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.hstore',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\hstore.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.json',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.pg8000',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\pg8000.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.psycopg2',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\psycopg2.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.psycopg2cffi',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\psycopg2cffi.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.pygresql',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\pygresql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.pypostgresql',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\pypostgresql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.ranges',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\ranges.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.zxjdbc',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\zxjdbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.base',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.json',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.pysqlcipher',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\pysqlcipher.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.pysqlite',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\pysqlite.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sybase',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\sybase\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sybase.base',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\sybase\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sybase.pyodbc',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\sybase\\pyodbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sybase.pysybase',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\dialects\\sybase\\pysybase.py',
   'PYMODULE'),
  ('sqlalchemy.engine',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\engine\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.engine.base',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\engine\\base.py',
   'PYMODULE'),
  ('sqlalchemy.engine.default',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\engine\\default.py',
   'PYMODULE'),
  ('sqlalchemy.engine.interfaces',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\engine\\interfaces.py',
   'PYMODULE'),
  ('sqlalchemy.engine.reflection',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\engine\\reflection.py',
   'PYMODULE'),
  ('sqlalchemy.engine.result',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\engine\\result.py',
   'PYMODULE'),
  ('sqlalchemy.engine.strategies',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\engine\\strategies.py',
   'PYMODULE'),
  ('sqlalchemy.engine.threadlocal',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\engine\\threadlocal.py',
   'PYMODULE'),
  ('sqlalchemy.engine.url',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\engine\\url.py',
   'PYMODULE'),
  ('sqlalchemy.engine.util',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\engine\\util.py',
   'PYMODULE'),
  ('sqlalchemy.event',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\event\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.event.api',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\event\\api.py',
   'PYMODULE'),
  ('sqlalchemy.event.attr',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\event\\attr.py',
   'PYMODULE'),
  ('sqlalchemy.event.base',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\event\\base.py',
   'PYMODULE'),
  ('sqlalchemy.event.legacy',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\event\\legacy.py',
   'PYMODULE'),
  ('sqlalchemy.event.registry',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\event\\registry.py',
   'PYMODULE'),
  ('sqlalchemy.events',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\events.py',
   'PYMODULE'),
  ('sqlalchemy.exc',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\exc.py',
   'PYMODULE'),
  ('sqlalchemy.ext',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\ext\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.ext.baked',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\ext\\baked.py',
   'PYMODULE'),
  ('sqlalchemy.ext.compiler',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\ext\\compiler.py',
   'PYMODULE'),
  ('sqlalchemy.inspection',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\inspection.py',
   'PYMODULE'),
  ('sqlalchemy.interfaces',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\interfaces.py',
   'PYMODULE'),
  ('sqlalchemy.log',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\log.py',
   'PYMODULE'),
  ('sqlalchemy.orm',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\orm\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.orm.attributes',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\orm\\attributes.py',
   'PYMODULE'),
  ('sqlalchemy.orm.base',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\orm\\base.py',
   'PYMODULE'),
  ('sqlalchemy.orm.collections',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\orm\\collections.py',
   'PYMODULE'),
  ('sqlalchemy.orm.dependency',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\orm\\dependency.py',
   'PYMODULE'),
  ('sqlalchemy.orm.deprecated_interfaces',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\orm\\deprecated_interfaces.py',
   'PYMODULE'),
  ('sqlalchemy.orm.descriptor_props',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\orm\\descriptor_props.py',
   'PYMODULE'),
  ('sqlalchemy.orm.dynamic',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\orm\\dynamic.py',
   'PYMODULE'),
  ('sqlalchemy.orm.evaluator',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\orm\\evaluator.py',
   'PYMODULE'),
  ('sqlalchemy.orm.events',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\orm\\events.py',
   'PYMODULE'),
  ('sqlalchemy.orm.exc',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\orm\\exc.py',
   'PYMODULE'),
  ('sqlalchemy.orm.identity',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\orm\\identity.py',
   'PYMODULE'),
  ('sqlalchemy.orm.instrumentation',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\orm\\instrumentation.py',
   'PYMODULE'),
  ('sqlalchemy.orm.interfaces',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\orm\\interfaces.py',
   'PYMODULE'),
  ('sqlalchemy.orm.loading',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\orm\\loading.py',
   'PYMODULE'),
  ('sqlalchemy.orm.mapper',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\orm\\mapper.py',
   'PYMODULE'),
  ('sqlalchemy.orm.path_registry',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\orm\\path_registry.py',
   'PYMODULE'),
  ('sqlalchemy.orm.persistence',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\orm\\persistence.py',
   'PYMODULE'),
  ('sqlalchemy.orm.properties',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\orm\\properties.py',
   'PYMODULE'),
  ('sqlalchemy.orm.query',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\orm\\query.py',
   'PYMODULE'),
  ('sqlalchemy.orm.relationships',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\orm\\relationships.py',
   'PYMODULE'),
  ('sqlalchemy.orm.scoping',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\orm\\scoping.py',
   'PYMODULE'),
  ('sqlalchemy.orm.session',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\orm\\session.py',
   'PYMODULE'),
  ('sqlalchemy.orm.state',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\orm\\state.py',
   'PYMODULE'),
  ('sqlalchemy.orm.strategies',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\orm\\strategies.py',
   'PYMODULE'),
  ('sqlalchemy.orm.strategy_options',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\orm\\strategy_options.py',
   'PYMODULE'),
  ('sqlalchemy.orm.sync',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\orm\\sync.py',
   'PYMODULE'),
  ('sqlalchemy.orm.unitofwork',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\orm\\unitofwork.py',
   'PYMODULE'),
  ('sqlalchemy.orm.util',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\orm\\util.py',
   'PYMODULE'),
  ('sqlalchemy.pool',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\pool\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.pool.base',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\pool\\base.py',
   'PYMODULE'),
  ('sqlalchemy.pool.dbapi_proxy',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\pool\\dbapi_proxy.py',
   'PYMODULE'),
  ('sqlalchemy.pool.impl',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\pool\\impl.py',
   'PYMODULE'),
  ('sqlalchemy.processors',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\processors.py',
   'PYMODULE'),
  ('sqlalchemy.schema',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\schema.py',
   'PYMODULE'),
  ('sqlalchemy.sql',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\sql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.sql.annotation',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\sql\\annotation.py',
   'PYMODULE'),
  ('sqlalchemy.sql.base',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\sql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.sql.compiler',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\sql\\compiler.py',
   'PYMODULE'),
  ('sqlalchemy.sql.crud',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\sql\\crud.py',
   'PYMODULE'),
  ('sqlalchemy.sql.ddl',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\sql\\ddl.py',
   'PYMODULE'),
  ('sqlalchemy.sql.default_comparator',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\sql\\default_comparator.py',
   'PYMODULE'),
  ('sqlalchemy.sql.dml',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\sql\\dml.py',
   'PYMODULE'),
  ('sqlalchemy.sql.elements',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\sql\\elements.py',
   'PYMODULE'),
  ('sqlalchemy.sql.expression',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\sql\\expression.py',
   'PYMODULE'),
  ('sqlalchemy.sql.functions',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\sql\\functions.py',
   'PYMODULE'),
  ('sqlalchemy.sql.naming',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\sql\\naming.py',
   'PYMODULE'),
  ('sqlalchemy.sql.operators',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\sql\\operators.py',
   'PYMODULE'),
  ('sqlalchemy.sql.schema',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\sql\\schema.py',
   'PYMODULE'),
  ('sqlalchemy.sql.selectable',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\sql\\selectable.py',
   'PYMODULE'),
  ('sqlalchemy.sql.sqltypes',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\sql\\sqltypes.py',
   'PYMODULE'),
  ('sqlalchemy.sql.type_api',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\sql\\type_api.py',
   'PYMODULE'),
  ('sqlalchemy.sql.util',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\sql\\util.py',
   'PYMODULE'),
  ('sqlalchemy.sql.visitors',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\sql\\visitors.py',
   'PYMODULE'),
  ('sqlalchemy.types',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\types.py',
   'PYMODULE'),
  ('sqlalchemy.util',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\util\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.util._collections',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\util\\_collections.py',
   'PYMODULE'),
  ('sqlalchemy.util.compat',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\util\\compat.py',
   'PYMODULE'),
  ('sqlalchemy.util.deprecations',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\util\\deprecations.py',
   'PYMODULE'),
  ('sqlalchemy.util.langhelpers',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\util\\langhelpers.py',
   'PYMODULE'),
  ('sqlalchemy.util.queue',
   'c:\\program files\\python\\lib\\site-packages\\sqlalchemy\\util\\queue.py',
   'PYMODULE'),
  ('sqlalchemy.util.topological',
   'c:\\program '
   'files\\python\\lib\\site-packages\\sqlalchemy\\util\\topological.py',
   'PYMODULE'),
  ('sqlite3',
   'c:\\program files\\python\\lib\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   'c:\\program files\\python\\lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'c:\\program files\\python\\lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('ssl', 'c:\\program files\\python\\lib\\ssl.py', 'PYMODULE'),
  ('strategies',
   'C:\\Users\\<USER>\\Desktop\\yl\\2.0\\strategies.py',
   'PYMODULE'),
  ('strategies_extended',
   'C:\\Users\\<USER>\\Desktop\\yl\\2.0\\strategies_extended.py',
   'PYMODULE'),
  ('strategy_tabs',
   'C:\\Users\\<USER>\\Desktop\\yl\\2.0\\strategy_tabs.py',
   'PYMODULE'),
  ('string', 'c:\\program files\\python\\lib\\string.py', 'PYMODULE'),
  ('stringprep', 'c:\\program files\\python\\lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'c:\\program files\\python\\lib\\subprocess.py', 'PYMODULE'),
  ('sysconfig', 'c:\\program files\\python\\lib\\sysconfig.py', 'PYMODULE'),
  ('system_log_tab',
   'C:\\Users\\<USER>\\Desktop\\yl\\2.0\\system_log_tab.py',
   'PYMODULE'),
  ('tarfile', 'c:\\program files\\python\\lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'c:\\program files\\python\\lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'c:\\program files\\python\\lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'c:\\program files\\python\\lib\\threading.py', 'PYMODULE'),
  ('tkinter',
   'c:\\program files\\python\\lib\\tkinter\\__init__.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'c:\\program files\\python\\lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.constants',
   'c:\\program files\\python\\lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('tkinter.dialog',
   'c:\\program files\\python\\lib\\tkinter\\dialog.py',
   'PYMODULE'),
  ('tkinter.filedialog',
   'c:\\program files\\python\\lib\\tkinter\\filedialog.py',
   'PYMODULE'),
  ('tkinter.font',
   'c:\\program files\\python\\lib\\tkinter\\font.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'c:\\program files\\python\\lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.simpledialog',
   'c:\\program files\\python\\lib\\tkinter\\simpledialog.py',
   'PYMODULE'),
  ('tkinter.ttk',
   'c:\\program files\\python\\lib\\tkinter\\ttk.py',
   'PYMODULE'),
  ('token', 'c:\\program files\\python\\lib\\token.py', 'PYMODULE'),
  ('tokenize', 'c:\\program files\\python\\lib\\tokenize.py', 'PYMODULE'),
  ('tomli',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('tomli._parser',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('tomli._re',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('tomli._types',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('tracemalloc', 'c:\\program files\\python\\lib\\tracemalloc.py', 'PYMODULE'),
  ('tty', 'c:\\program files\\python\\lib\\tty.py', 'PYMODULE'),
  ('typeguard',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\typeguard\\__init__.py',
   'PYMODULE'),
  ('typeguard._checkers',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\typeguard\\_checkers.py',
   'PYMODULE'),
  ('typeguard._config',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\typeguard\\_config.py',
   'PYMODULE'),
  ('typeguard._decorators',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\typeguard\\_decorators.py',
   'PYMODULE'),
  ('typeguard._exceptions',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\typeguard\\_exceptions.py',
   'PYMODULE'),
  ('typeguard._functions',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\typeguard\\_functions.py',
   'PYMODULE'),
  ('typeguard._importhook',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\typeguard\\_importhook.py',
   'PYMODULE'),
  ('typeguard._memo',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\typeguard\\_memo.py',
   'PYMODULE'),
  ('typeguard._suppression',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\typeguard\\_suppression.py',
   'PYMODULE'),
  ('typeguard._transformer',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\typeguard\\_transformer.py',
   'PYMODULE'),
  ('typeguard._union_transformer',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\typeguard\\_union_transformer.py',
   'PYMODULE'),
  ('typeguard._utils',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\typeguard\\_utils.py',
   'PYMODULE'),
  ('typing', 'c:\\program files\\python\\lib\\typing.py', 'PYMODULE'),
  ('typing_extensions',
   'c:\\program files\\python\\lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('unittest',
   'c:\\program files\\python\\lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest.async_case',
   'c:\\program files\\python\\lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case',
   'c:\\program files\\python\\lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.loader',
   'c:\\program files\\python\\lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main',
   'c:\\program files\\python\\lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.mock',
   'c:\\program files\\python\\lib\\unittest\\mock.py',
   'PYMODULE'),
  ('unittest.result',
   'c:\\program files\\python\\lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'c:\\program files\\python\\lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'c:\\program files\\python\\lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'c:\\program files\\python\\lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util',
   'c:\\program files\\python\\lib\\unittest\\util.py',
   'PYMODULE'),
  ('urllib', 'c:\\program files\\python\\lib\\urllib\\__init__.py', 'PYMODULE'),
  ('urllib.error',
   'c:\\program files\\python\\lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'c:\\program files\\python\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'c:\\program files\\python\\lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'c:\\program files\\python\\lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib3',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._collections',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._version',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'c:\\program '
   'files\\python\\lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib._appengine_environ',
   'c:\\program '
   'files\\python\\lib\\site-packages\\urllib3\\contrib\\_appengine_environ.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'c:\\program '
   'files\\python\\lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.packages',
   'c:\\program '
   'files\\python\\lib\\site-packages\\urllib3\\packages\\__init__.py',
   'PYMODULE'),
  ('urllib3.packages.backports',
   'c:\\program '
   'files\\python\\lib\\site-packages\\urllib3\\packages\\backports\\__init__.py',
   'PYMODULE'),
  ('urllib3.packages.backports.makefile',
   'c:\\program '
   'files\\python\\lib\\site-packages\\urllib3\\packages\\backports\\makefile.py',
   'PYMODULE'),
  ('urllib3.packages.six',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\packages\\six.py',
   'PYMODULE'),
  ('urllib3.packages.ssl_match_hostname',
   'c:\\program '
   'files\\python\\lib\\site-packages\\urllib3\\packages\\ssl_match_hostname\\__init__.py',
   'PYMODULE'),
  ('urllib3.packages.ssl_match_hostname._implementation',
   'c:\\program '
   'files\\python\\lib\\site-packages\\urllib3\\packages\\ssl_match_hostname\\_implementation.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.request',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\request.py',
   'PYMODULE'),
  ('urllib3.response',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'c:\\program '
   'files\\python\\lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.queue',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\util\\queue.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'c:\\program files\\python\\lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('user_friendly_messages',
   'C:\\Users\\<USER>\\Desktop\\yl\\2.0\\user_friendly_messages.py',
   'PYMODULE'),
  ('uu', 'c:\\program files\\python\\lib\\uu.py', 'PYMODULE'),
  ('uuid', 'c:\\program files\\python\\lib\\uuid.py', 'PYMODULE'),
  ('webbrowser', 'c:\\program files\\python\\lib\\webbrowser.py', 'PYMODULE'),
  ('wheel',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\wheel\\__init__.py',
   'PYMODULE'),
  ('wheel._setuptools_logging',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\wheel\\_setuptools_logging.py',
   'PYMODULE'),
  ('wheel.bdist_wheel',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\wheel\\bdist_wheel.py',
   'PYMODULE'),
  ('wheel.cli',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('wheel.cli.convert',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('wheel.cli.pack',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('wheel.cli.tags',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('wheel.cli.unpack',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('wheel.macosx_libfile',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('wheel.metadata',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\wheel\\metadata.py',
   'PYMODULE'),
  ('wheel.util',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\wheel\\util.py',
   'PYMODULE'),
  ('wheel.vendored',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('wheel.vendored.packaging',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._elffile',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._manylinux',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._musllinux',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._parser',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._structures',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._tokenizer',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.markers',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.requirements',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.specifiers',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.tags',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.utils',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.version',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('wheel.wheelfile',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('win32com',
   'c:\\program files\\python\\lib\\site-packages\\win32com\\__init__.py',
   'PYMODULE'),
  ('win32com.client',
   'c:\\program '
   'files\\python\\lib\\site-packages\\win32com\\client\\__init__.py',
   'PYMODULE'),
  ('win32com.client.CLSIDToClass',
   'c:\\program '
   'files\\python\\lib\\site-packages\\win32com\\client\\CLSIDToClass.py',
   'PYMODULE'),
  ('win32com.client.build',
   'c:\\program files\\python\\lib\\site-packages\\win32com\\client\\build.py',
   'PYMODULE'),
  ('win32com.client.dynamic',
   'c:\\program '
   'files\\python\\lib\\site-packages\\win32com\\client\\dynamic.py',
   'PYMODULE'),
  ('win32com.client.gencache',
   'c:\\program '
   'files\\python\\lib\\site-packages\\win32com\\client\\gencache.py',
   'PYMODULE'),
  ('win32com.client.genpy',
   'c:\\program files\\python\\lib\\site-packages\\win32com\\client\\genpy.py',
   'PYMODULE'),
  ('win32com.client.makepy',
   'c:\\program files\\python\\lib\\site-packages\\win32com\\client\\makepy.py',
   'PYMODULE'),
  ('win32com.client.selecttlb',
   'c:\\program '
   'files\\python\\lib\\site-packages\\win32com\\client\\selecttlb.py',
   'PYMODULE'),
  ('win32com.client.util',
   'c:\\program files\\python\\lib\\site-packages\\win32com\\client\\util.py',
   'PYMODULE'),
  ('win32com.server',
   'c:\\program '
   'files\\python\\lib\\site-packages\\win32com\\server\\__init__.py',
   'PYMODULE'),
  ('win32com.server.dispatcher',
   'c:\\program '
   'files\\python\\lib\\site-packages\\win32com\\server\\dispatcher.py',
   'PYMODULE'),
  ('win32com.server.exception',
   'c:\\program '
   'files\\python\\lib\\site-packages\\win32com\\server\\exception.py',
   'PYMODULE'),
  ('win32com.server.policy',
   'c:\\program files\\python\\lib\\site-packages\\win32com\\server\\policy.py',
   'PYMODULE'),
  ('win32com.server.util',
   'c:\\program files\\python\\lib\\site-packages\\win32com\\server\\util.py',
   'PYMODULE'),
  ('win32com.universal',
   'c:\\program files\\python\\lib\\site-packages\\win32com\\universal.py',
   'PYMODULE'),
  ('win32com.util',
   'c:\\program files\\python\\lib\\site-packages\\win32com\\util.py',
   'PYMODULE'),
  ('win32con',
   'c:\\program files\\python\\lib\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE'),
  ('win32traceutil',
   'c:\\program '
   'files\\python\\lib\\site-packages\\win32\\lib\\win32traceutil.py',
   'PYMODULE'),
  ('winerror',
   'c:\\program files\\python\\lib\\site-packages\\win32\\lib\\winerror.py',
   'PYMODULE'),
  ('wsgiref',
   'c:\\program files\\python\\lib\\wsgiref\\__init__.py',
   'PYMODULE'),
  ('wsgiref.handlers',
   'c:\\program files\\python\\lib\\wsgiref\\handlers.py',
   'PYMODULE'),
  ('wsgiref.headers',
   'c:\\program files\\python\\lib\\wsgiref\\headers.py',
   'PYMODULE'),
  ('wsgiref.util',
   'c:\\program files\\python\\lib\\wsgiref\\util.py',
   'PYMODULE'),
  ('xml', 'c:\\program files\\python\\lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.dom',
   'c:\\program files\\python\\lib\\xml\\dom\\__init__.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'c:\\program files\\python\\lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'c:\\program files\\python\\lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'c:\\program files\\python\\lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'c:\\program files\\python\\lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'c:\\program files\\python\\lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'c:\\program files\\python\\lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'c:\\program files\\python\\lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.etree',
   'c:\\program files\\python\\lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'c:\\program files\\python\\lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'c:\\program files\\python\\lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'c:\\program files\\python\\lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'c:\\program files\\python\\lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers',
   'c:\\program files\\python\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'c:\\program files\\python\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'c:\\program files\\python\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'c:\\program files\\python\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'c:\\program files\\python\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'c:\\program files\\python\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'c:\\program files\\python\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'c:\\program files\\python\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc', 'c:\\program files\\python\\lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xmlrpc.client',
   'c:\\program files\\python\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('zipfile', 'c:\\program files\\python\\lib\\zipfile.py', 'PYMODULE'),
  ('zipimport', 'c:\\program files\\python\\lib\\zipimport.py', 'PYMODULE'),
  ('zipp',
   'c:\\program files\\python\\lib\\site-packages\\zipp\\__init__.py',
   'PYMODULE'),
  ('zipp.compat',
   'c:\\program files\\python\\lib\\site-packages\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('zipp.compat.overlay',
   'c:\\program files\\python\\lib\\site-packages\\zipp\\compat\\overlay.py',
   'PYMODULE'),
  ('zipp.compat.py310',
   'c:\\program files\\python\\lib\\site-packages\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('zipp.glob',
   'c:\\program files\\python\\lib\\site-packages\\zipp\\glob.py',
   'PYMODULE')])
