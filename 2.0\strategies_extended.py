"""
扩展交易策略模块
包含成交量突破策略和智能网格策略的完整实现
"""
import ccxt
import time
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from logger import get_logger, log_trade, log_error, log_strategy_start, log_strategy_stop

class VolumeBreakoutStrategy:
    def __init__(self, exchange, symbol, lookback_period=20, volume_multiplier=2, breakout_threshold=0.02):
        self.exchange = exchange
        self.symbol = symbol
        self.lookback_period = lookback_period
        self.volume_multiplier = volume_multiplier
        self.breakout_threshold = breakout_threshold
        self.position = 0
        self.entry_price = 0
        self.stop_loss_pct = 0.03  # 3%止损
        self.take_profit_pct = 0.06  # 6%止盈
        self.running = False
        self.logger = get_logger("volume_breakout")

    def get_market_data(self, timeframe='1h', limit=100):
        """获取市场数据"""
        try:
            ohlcv = self.exchange.fetch_ohlcv(self.symbol, timeframe, limit=limit)
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')

            # 计算技术指标
            df['volume_ma'] = df['volume'].rolling(window=self.lookback_period).mean()
            df['price_ma'] = df['close'].rolling(window=self.lookback_period).mean()
            df['resistance'] = df['high'].rolling(window=self.lookback_period).max()
            df['support'] = df['low'].rolling(window=self.lookback_period).min()

            return df
        except Exception as e:
            self.logger.error(f"获取市场数据失败: {e}")
            return None

    def detect_breakout(self, df):
        """检测突破信号"""
        df['breakout_signal'] = 0

        for i in range(self.lookback_period, len(df)):
            current_row = df.iloc[i]
            prev_row = df.iloc[i-1]

            # 向上突破条件
            upward_breakout = (
                current_row['close'] > prev_row['resistance'] * (1 + self.breakout_threshold) and
                current_row['volume'] > current_row['volume_ma'] * self.volume_multiplier and
                current_row['close'] > current_row['open']  # 阳线
            )

            # 向下突破条件
            downward_breakout = (
                current_row['close'] < prev_row['support'] * (1 - self.breakout_threshold) and
                current_row['volume'] > current_row['volume_ma'] * self.volume_multiplier and
                current_row['close'] < current_row['open']  # 阴线
            )

            if upward_breakout:
                df.iloc[i, df.columns.get_loc('breakout_signal')] = 1
            elif downward_breakout:
                df.iloc[i, df.columns.get_loc('breakout_signal')] = -1

        return df

    def calculate_position_size(self, current_price, account_risk=0.02):
        """根据风险控制计算仓位"""
        try:
            balance = self.exchange.fetch_balance()
            account_balance = balance['USDT']['free']
            # 根据止损比例计算仓位
            risk_amount = account_balance * account_risk
            position_value = risk_amount / self.stop_loss_pct
            position_size = position_value / current_price
            return max(position_size, 0.001)
        except Exception as e:
            self.logger.error(f"计算仓位失败:{e}")
            return 0.001


class VolumeBreakoutStrategyExtended(VolumeBreakoutStrategy):
    """成交量突破策略的扩展实现"""
    
    def manage_risk(self, current_price):
        """风险管理"""
        if self.position == 0 or self.entry_price == 0:
            return False
        
        # 多头仓位风险管理
        if self.position > 0:
            # 止损
            if current_price <= self.entry_price * (1 - self.stop_loss_pct):
                self.close_position(current_price, "止损")
                return True
            # 止盈
            elif current_price >= self.entry_price * (1 + self.take_profit_pct):
                self.close_position(current_price, "止盈")
                return True
        
        # 空头仓位风险管理
        elif self.position < 0:
            # 止损
            if current_price >= self.entry_price * (1 + self.stop_loss_pct):
                self.close_position(current_price, "止损")
                return True
            # 止盈
            elif current_price <= self.entry_price * (1 - self.take_profit_pct):
                self.close_position(current_price, "止盈")
                return True
        
        return False
    
    def close_position(self, current_price, reason):
        """平仓"""
        try:
            balance = self.exchange.fetch_balance()
            base_currency = self.symbol.split('/')[0]
            
            if self.position > 0:  # 平多头
                sell_amount = balance[base_currency]['free']
                if sell_amount > 0.001:
                    order = self.exchange.create_market_sell_order(self.symbol, sell_amount)
                    profit_pct = (current_price - self.entry_price) / self.entry_price * 100
                    log_trade("volume_breakout", "平多头", self.symbol, sell_amount, current_price, order.get('id'))
                    self.logger.info(f"平多头({reason}):{sell_amount:.6f} @ {current_price:.2f},收益:{profit_pct:.2f}%")
            
            elif self.position < 0:  # 平空头
                # 这里简化处理，实际需要根据是否支持做空
                self.logger.info(f"平空头({reason}): @{current_price:.2f}")
            
            self.position = 0
            self.entry_price = 0
        except Exception as e:
            log_error("volume_breakout", "平仓失败", e)
            self.logger.error(f"平仓失败: {e}")
    
    def execute_breakout_trade(self, signal, current_price, volume_ratio):
        """执行突破交易"""
        try:
            if signal == 1 and self.position <= 0:  # 向上突破买入
                position_size = self.calculate_position_size(current_price)
                order = self.exchange.create_market_buy_order(self.symbol, position_size)
                self.position = 1
                self.entry_price = current_price
                log_trade("volume_breakout", "突破买入", self.symbol, position_size, current_price, order.get('id'))
                self.logger.info(f"向上突破买入:{position_size:.6f} @ {current_price:.2f},成交量比:{volume_ratio:.2f}")
            
            elif signal == -1 and self.position >= 0:  # 向下突破卖出
                # 注意: 这里只是示例，实际使用时需要考虑平台是否支持做空
                self.logger.info(f"向下突破信号@{current_price:.2f},成交量比:{volume_ratio:.2f}")
                # 实际使用时可以考虑平掉多头仓位
                if self.position > 0:
                    self.close_position(current_price, "向下突破平仓")
        except Exception as e:
            log_error("volume_breakout", "执行突破交易失败", e)
            self.logger.error(f"执行突破交易失败: {e}")
    
    def run_backtest(self, days=30):
        """回测突破策略"""
        df = self.get_market_data(timeframe='4h', limit=days*6)
        if df is None:
            return
        
        df = self.detect_breakout(df)
        # 模拟交易
        capital = 10000
        position = 0
        entry_price = 0
        trades = []
        
        for i in range(self.lookback_period, len(df)):
            current_price = df['close'].iloc[i]
            signal = df['breakout_signal'].iloc[i]
            volume_ratio = df['volume'].iloc[i] / df['volume_ma'].iloc[i]
            
            # 风险管理
            if position != 0:
                if position > 0:  # 多头
                    if current_price <= entry_price * (1 - self.stop_loss_pct):
                        # 止损
                        capital = position * current_price
                        profit = (current_price - entry_price) / entry_price
                        trades.append({'type': 'sell', 'price': current_price, 'profit': profit, 'reason': '止损'})
                        position = 0
                    elif current_price >= entry_price * (1 + self.take_profit_pct):
                        # 止盈
                        capital = position * current_price
                        profit = (current_price - entry_price) / entry_price
                        trades.append({'type': 'sell', 'price': current_price, 'profit': profit, 'reason': '止盈'})
                        position = 0
            
            # 交易信号
            if signal == 1 and position == 0:
                position = capital / current_price
                entry_price = current_price
                capital = 0
                trades.append({'type': 'buy', 'price': current_price, 'volume_ratio': volume_ratio})
        
        # 最终回测结果
        if position > 0:
            capital = position * df['close'].iloc[-1]
        
        total_return = (capital - 10000) / 10000
        win_trades = [t for t in trades if t.get('profit', 0) > 0]
        win_rate = len(win_trades) / max(len([t for t in trades if 'profit' in t]), 1)
        
        self.logger.info(f"突破策略回测结果:")
        self.logger.info(f"总收益率:{total_return:.2%}")
        self.logger.info(f"交易次数:{len(trades)}")
        self.logger.info(f"胜率:{win_rate:.2%}")
        
        return trades
    
    def run_live(self):
        """实时交易"""
        self.logger.info("开始成交量突破策略...")
        self.running = True
        
        while self.running:
            try:
                df = self.get_market_data(timeframe='1h', limit=50)
                if df is None:
                    time.sleep(300)
                    continue
                
                df = self.detect_breakout(df)
                current_price = df['close'].iloc[-1]
                signal = df['breakout_signal'].iloc[-1]
                volume_ratio = df['volume'].iloc[-1] / df['volume_ma'].iloc[-1] if not pd.isna(df['volume_ma'].iloc[-1]) else 1
                
                # 先检查风险管理
                if self.manage_risk(current_price):
                    time.sleep(300)
                    continue
                
                # 执行交易信号
                if signal != 0:
                    self.execute_breakout_trade(signal, current_price, volume_ratio)

                # 显示详细的实时状态
                signal_status = "突破信号" if signal != 0 else "无信号"
                volume_status = "高成交量" if volume_ratio > self.volume_multiplier else "正常成交量"
                self.logger.info(f"当前价格: {current_price:.6f}, 仓位: {self.position}, 成交量比: {volume_ratio:.2f} ({volume_status}), {signal_status}")
                time.sleep(300)  # 5分钟检查一次
            except KeyboardInterrupt:
                self.logger.info("策略停止")
                break
            except Exception as e:
                log_error("volume_breakout", "运行错误", e)
                self.logger.error(f"运行错误: {e}")
                time.sleep(60)
    
    def get_current_price(self):
        """获取当前价格"""
        try:
            # 方法1: 使用ticker获取最新价格
            ticker = self.exchange.fetch_ticker(self.symbol)
            price = float(ticker['last'])

            if price > 0:
                self.last_price = getattr(self, 'last_price', price)
                self.last_price = price
                return price

            # 方法2: 如果ticker价格异常，使用orderbook
            orderbook = self.exchange.fetch_order_book(self.symbol, limit=5)
            if orderbook['bids'] and orderbook['asks']:
                bid_price = float(orderbook['bids'][0][0])
                ask_price = float(orderbook['asks'][0][0])
                price = (bid_price + ask_price) / 2
                self.last_price = price
                return price

            # 方法3: 使用最近交易记录
            trades = self.exchange.fetch_trades(self.symbol, limit=1)
            if trades:
                price = float(trades[0]['price'])
                self.last_price = price
                return price

            # 如果所有方法都失败，返回缓存价格
            return getattr(self, 'last_price', 0.0)

        except Exception as e:
            self.logger.error(f"获取价格失败: {e}")
            return getattr(self, 'last_price', 0.0)

    def stop(self):
        """停止策略"""
        self.running = False
        log_strategy_stop("volume_breakout")


class SmartGridStrategy:
    def __init__(self, exchange, symbol, initial_investment=1000):
        self.exchange = exchange
        self.symbol = symbol
        self.initial_investment = initial_investment
        self.grids = {}
        self.market_trend = 'neutral'  # 'bull', 'bear', 'neutral'
        self.grid_count = 10
        self.base_grid_spacing = 0.01  # 1%
        self.last_rebalance_time = 0
        self.running = False
        self.logger = get_logger("smart_grid")
    
    def analyze_market_trend(self, timeframe='4h', limit=48):
        """分析市场趋势"""
        try:
            ohlcv = self.exchange.fetch_ohlcv(self.symbol, timeframe, limit=limit)
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            
            # 计算不同时间周期的移动平均线
            df['ma_short'] = df['close'].rolling(window=5).mean()
            df['ma_medium'] = df['close'].rolling(window=12).mean()
            df['ma_long'] = df['close'].rolling(window=24).mean()
            
            # 计算RSI
            delta = df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            df['rsi'] = 100 - (100 / (1 + rs))
            
            # 趋势判断逻辑
            latest = df.iloc[-1]
            prev = df.iloc[-5]  # 5个周期前
            
            # 牛头市场条件
            bull_conditions = [
                latest['ma_short'] > latest['ma_medium'] > latest['ma_long'],
                latest['close'] > prev['close'] * 1.02,  # 价格上涨超过2%
                latest['rsi'] < 80  # RSI不要太高
            ]
            
            # 熊头市场条件
            bear_conditions = [
                latest['ma_short'] < latest['ma_medium'] < latest['ma_long'],
                latest['close'] < prev['close'] * 0.98,  # 价格下跌超过2%
                latest['rsi'] > 20  # RSI不要太低
            ]
            
            if sum(bull_conditions) >= 2:
                self.market_trend = 'bull'
            elif sum(bear_conditions) >= 2:
                self.market_trend = 'bear'
            else:
                self.market_trend = 'neutral'
            
            self.logger.info(f"市场趋势:{self.market_trend},RSI:{latest['rsi']:.2f}")
            return self.market_trend
        except Exception as e:
            log_error("smart_grid", "分析市场趋势失败", e)
            self.logger.error(f"分析市场趋势失败:{e}")
            return 'neutral'

    def calculate_adaptive_grid_params(self, current_price):
        """根据市场趋势调整网格参数"""
        volatility = self.calculate_volatility()

        if self.market_trend == 'bull':
            # 牛市:网格偏向上方，间距较大
            grid_spacing = self.base_grid_spacing * (1 + volatility)
            lower_grids = 3
            upper_grids = 7
        elif self.market_trend == 'bear':
            # 熊市:网格偏向下方，间距较小
            grid_spacing = self.base_grid_spacing * (1 - volatility * 0.3)
            lower_grids = 7
            upper_grids = 3
        else:
            # 震荡: 对称网格
            grid_spacing = self.base_grid_spacing
            lower_grids = 5
            upper_grids = 5

        return grid_spacing, lower_grids, upper_grids

    def calculate_volatility(self, days=7):
        """计算历史波动率"""
        try:
            ohlcv = self.exchange.fetch_ohlcv(self.symbol, '1h', limit=days*24)
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            returns = df['close'].pct_change().dropna()
            volatility = returns.std() * np.sqrt(24)  # 年化波动率
            return min(volatility, 0.5)  # 限制最大波动率
        except Exception as e:
            self.logger.error(f"计算波动率失败: {e}")
            return 0.02

    def create_smart_grids(self):
        """创建智能网格"""
        try:
            current_price = self.get_current_price()
            grid_spacing, lower_grids, upper_grids = self.calculate_adaptive_grid_params(current_price)

            # 取消所有旧订单
            self.cancel_all_orders()

            # 计算每个网格的投资金额
            total_grids = lower_grids + upper_grids
            amount_per_grid = self.initial_investment / total_grids / current_price

            # 创建下方网格(买单)
            for i in range(1, lower_grids + 1):
                grid_price = current_price * (1 - i * grid_spacing)
                try:
                    order = self.exchange.create_limit_buy_order(
                        self.symbol, amount_per_grid, grid_price
                    )
                    self.grids[grid_price] = {
                        'type': 'buy',
                        'order': order,
                        'amount': amount_per_grid
                    }
                    self.logger.info(f"买单网格:{grid_price:.2f}")
                except Exception as e:
                    self.logger.error(f"创建买单失败:{e}")

            # 创建上方网格(卖单)
            balance = self.exchange.fetch_balance()
            base_currency = self.symbol.split('/')[0]
            available_amount = balance[base_currency]['free']

            if available_amount > amount_per_grid:
                sell_amount_per_grid = available_amount / upper_grids
                for i in range(1, upper_grids + 1):
                    grid_price = current_price * (1 + i * grid_spacing)
                    try:
                        order = self.exchange.create_limit_sell_order(
                            self.symbol, sell_amount_per_grid, grid_price
                        )
                        self.grids[grid_price] = {
                            'type': 'sell',
                            'order': order,
                            'amount': sell_amount_per_grid
                        }
                        self.logger.info(f"卖单网格:{grid_price:.2f}")
                    except Exception as e:
                        self.logger.error(f"创建卖单失败:{e}")
        except Exception as e:
            log_error("smart_grid", "创建智能网格失败", e)
            self.logger.error(f"创建智能网格失败:{e}")

    def cancel_all_orders(self):
        """取消所有挂单"""
        try:
            open_orders = self.exchange.fetch_open_orders(self.symbol)
            for order in open_orders:
                self.exchange.cancel_order(order['id'], self.symbol)
            self.grids.clear()
            self.logger.info("已取消所有旧订单")
        except Exception as e:
            self.logger.error(f"取消订单失败:{e}")

    def get_current_price(self):
        """获取当前价格 - 增强版"""
        try:
            # 方法1: 使用ticker获取最新价格
            ticker = self.exchange.fetch_ticker(self.symbol)
            price = float(ticker['last'])

            if price > 0:
                self.last_price = getattr(self, 'last_price', price)  # 缓存成功获取的价格
                self.last_price = price
                return price

            # 方法2: 如果ticker价格异常，使用orderbook
            orderbook = self.exchange.fetch_order_book(self.symbol, limit=5)
            if orderbook['bids'] and orderbook['asks']:
                bid_price = float(orderbook['bids'][0][0])
                ask_price = float(orderbook['asks'][0][0])
                price = (bid_price + ask_price) / 2
                self.last_price = price
                return price

            # 方法3: 使用最近交易记录
            trades = self.exchange.fetch_trades(self.symbol, limit=1)
            if trades:
                price = float(trades[0]['price'])
                self.last_price = price
                return price

            # 如果所有方法都失败，返回缓存价格
            return getattr(self, 'last_price', 0.0)

        except Exception as e:
            self.logger.error(f"获取价格失败: {e}")
            # 返回上次成功获取的价格，如果没有则返回0
            return getattr(self, 'last_price', 0.0)

    def rebalance_grids(self):
        """重新平衡网格"""
        current_time = time.time()
        # 每2小时重新平衡一次
        if current_time - self.last_rebalance_time < 7200:
            return

        self.logger.info("开始重新平衡网格...")
        self.analyze_market_trend()
        self.create_smart_grids()
        self.last_rebalance_time = current_time

    def monitor_and_recreate_orders(self):
        """监控订单并重新创建"""
        for price, grid_info in list(self.grids.items()):
            try:
                order_status = self.exchange.fetch_order(
                    grid_info['order']['id'], self.symbol
                )

                if order_status['status'] == 'closed':
                    self.logger.info(f"网格订单成交:{price:.2f},类型:{grid_info['type']}")
                    # 根据策略决定是否放置新订单
                    current_price = self.get_current_price()
                    grid_spacing, _, _ = self.calculate_adaptive_grid_params(current_price)

                    if grid_info['type'] == 'buy':
                        # 买单成交了，在更高价格放卖单
                        new_sell_price = price * (1 + grid_spacing)
                        if new_sell_price < current_price * 1.1:  # 不要与当前价格太远
                            new_order = self.exchange.create_limit_sell_order(
                                self.symbol, grid_info['amount'], new_sell_price
                            )
                            self.grids[new_sell_price] = {
                                'type': 'sell',
                                'order': new_order,
                                'amount': grid_info['amount']
                            }
                    else:
                        # 卖单成交了，在更低价格放买单
                        new_buy_price = price * (1 - grid_spacing)
                        if new_buy_price > current_price * 0.9:  # 不要与当前价格太远
                            new_order = self.exchange.create_limit_buy_order(
                                self.symbol, grid_info['amount'], new_buy_price
                            )
                            self.grids[new_buy_price] = {
                                'type': 'buy',
                                'order': new_order,
                                'amount': grid_info['amount']
                            }

                    # 删除已成交的订单
                    del self.grids[price]
            except Exception as e:
                self.logger.error(f"监控订单失败: {e}")

    def run(self):
        """运行智能网格策略"""
        self.logger.info("启动智能网格策略...")
        self.running = True

        # 初始化
        self.analyze_market_trend()
        self.create_smart_grids()

        while self.running:
            try:
                self.monitor_and_recreate_orders()
                self.rebalance_grids()

                # 显示当前状态
                current_price = self.get_current_price()
                active_orders = len(self.grids)
                self.logger.info(f"当前价格: {current_price:.6f}, 活跃订单: {active_orders}, 趋势: {self.market_trend}")

                time.sleep(300)  # 5分钟检查一次
            except KeyboardInterrupt:
                self.logger.info("智能网格策略停止")
                self.cancel_all_orders()
                break
            except Exception as e:
                log_error("smart_grid", "运行错误", e)
                self.logger.error(f"运行错误:{e}")
                time.sleep(60)

    def stop(self):
        """停止策略"""
        self.running = False
        self.cancel_all_orders()
        log_strategy_stop("smart_grid")
