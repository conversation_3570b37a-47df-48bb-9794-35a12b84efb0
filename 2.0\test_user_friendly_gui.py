#!/usr/bin/env python3
"""
用户友好GUI组件测试
测试新的用户友好输入组件是否正常工作
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_user_friendly_inputs():
    """测试用户友好输入组件"""
    
    # 创建主窗口
    root = tk.Tk()
    root.title("用户友好输入组件测试")
    root.geometry("800x600")
    
    # 创建主框架
    main_frame = ttk.Frame(root)
    main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
    
    # 标题
    title_label = ttk.Label(main_frame, text="🧪 用户友好输入组件测试", 
                           font=("Arial", 16, "bold"))
    title_label.pack(pady=(0, 20))
    
    # 说明
    info_label = ttk.Label(main_frame, 
                          text="测试新的用户友好输入组件，包括参数说明、范围建议、使用示例和风险提示",
                          wraplength=600, justify=tk.CENTER)
    info_label.pack(pady=(0, 20))
    
    try:
        from user_friendly_input import create_user_friendly_input
        
        # 创建测试输入组件
        test_frame = ttk.LabelFrame(main_frame, text="网格交易策略参数测试")
        test_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # 基准价格输入
        base_price_input = create_user_friendly_input(test_frame, "grid_base_price")
        base_price_input.pack(fill=tk.X, pady=5, padx=10)
        base_price_input.set_value("0.21375")
        
        # 网格间距输入
        grid_spacing_input = create_user_friendly_input(test_frame, "grid_spacing")
        grid_spacing_input.pack(fill=tk.X, pady=5, padx=10)
        
        # 网格数量输入
        grid_count_input = create_user_friendly_input(test_frame, "grid_count")
        grid_count_input.pack(fill=tk.X, pady=5, padx=10)
        
        # 交易数量输入
        order_amount_input = create_user_friendly_input(test_frame, "grid_order_amount")
        order_amount_input.pack(fill=tk.X, pady=5, padx=10)
        order_amount_input.set_value("100")
        
        # 测试按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=20)
        
        def validate_all():
            """验证所有输入"""
            inputs = [
                ("基准价格", base_price_input),
                ("网格间距", grid_spacing_input),
                ("网格数量", grid_count_input),
                ("交易数量", order_amount_input)
            ]
            
            results = []
            for name, input_widget in inputs:
                is_valid = input_widget.is_value_valid()
                value = input_widget.get_value()
                message = input_widget.get_validation_message()
                
                status = "✅ 有效" if is_valid else "❌ 无效"
                results.append(f"{name}: {value} - {status}")
                if not is_valid:
                    results.append(f"  错误: {message}")
            
            # 显示结果
            result_text = "\n".join(results)
            
            # 创建结果窗口
            result_window = tk.Toplevel(root)
            result_window.title("验证结果")
            result_window.geometry("400x300")
            
            text_widget = tk.Text(result_window, wrap=tk.WORD)
            text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            text_widget.insert(tk.END, result_text)
            text_widget.configure(state=tk.DISABLED)
        
        def get_all_values():
            """获取所有值"""
            values = {
                "基准价格": base_price_input.get_value(),
                "网格间距": grid_spacing_input.get_value(),
                "网格数量": grid_count_input.get_value(),
                "交易数量": order_amount_input.get_value()
            }
            
            # 显示值
            value_text = "\n".join([f"{k}: {v}" for k, v in values.items()])
            
            # 创建值显示窗口
            value_window = tk.Toplevel(root)
            value_window.title("当前值")
            value_window.geometry("300x200")
            
            text_widget = tk.Text(value_window, wrap=tk.WORD)
            text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            text_widget.insert(tk.END, value_text)
            text_widget.configure(state=tk.DISABLED)
        
        # 测试按钮
        ttk.Button(button_frame, text="验证所有输入", command=validate_all).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="获取所有值", command=get_all_values).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="关闭", command=root.destroy).pack(side=tk.RIGHT, padx=5)
        
        # 状态标签
        status_label = ttk.Label(main_frame, 
                                text="✅ 用户友好输入组件加载成功！点击参数旁边的 '?' 按钮查看详细说明。",
                                foreground="green")
        status_label.pack(pady=10)
        
    except Exception as e:
        # 错误处理
        error_label = ttk.Label(main_frame, 
                               text=f"❌ 组件加载失败: {str(e)}",
                               foreground="red")
        error_label.pack(pady=20)
        
        # 错误详情
        error_text = tk.Text(main_frame, height=10, wrap=tk.WORD)
        error_text.pack(fill=tk.BOTH, expand=True, pady=10)
        
        import traceback
        error_details = traceback.format_exc()
        error_text.insert(tk.END, error_details)
        error_text.configure(state=tk.DISABLED)
    
    # 运行主循环
    root.mainloop()

def test_parameter_definitions():
    """测试参数定义"""
    print("🔍 测试参数定义...")
    
    try:
        from user_friendly_input import PARAMETER_DEFINITIONS
        
        print(f"✅ 成功加载 {len(PARAMETER_DEFINITIONS)} 个参数定义")
        
        for key, param_info in PARAMETER_DEFINITIONS.items():
            print(f"\n📋 {key}:")
            print(f"  名称: {param_info.name}")
            print(f"  描述: {param_info.description[:50]}...")
            print(f"  建议范围: {param_info.suggested_range}")
            print(f"  示例数量: {len(param_info.examples)}")
            print(f"  风险警告数量: {len(param_info.risk_warnings)}")
            
        return True
        
    except Exception as e:
        print(f"❌ 参数定义测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始用户友好GUI组件测试")
    print("=" * 50)
    
    # 测试参数定义
    if test_parameter_definitions():
        print("\n✅ 参数定义测试通过")
    else:
        print("\n❌ 参数定义测试失败")
        return
    
    print("\n🖥️ 启动GUI测试...")
    print("请在弹出的窗口中测试用户友好输入组件")
    
    # 启动GUI测试
    test_user_friendly_inputs()
    
    print("\n✅ GUI测试完成")

if __name__ == "__main__":
    main()
