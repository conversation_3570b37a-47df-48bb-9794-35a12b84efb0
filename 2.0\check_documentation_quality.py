#!/usr/bin/env python3
"""
代码注释质量检查工具

本工具用于检查量化交易系统核心代码文件的注释质量，
确保所有注释符合标准要求，便于用户理解和维护。
"""

import os
import re
import ast
from typing import Dict, List, Tuple
from datetime import datetime

class DocumentationChecker:
    """代码注释质量检查器"""
    
    def __init__(self):
        self.target_files = [
            'strategies.py',
            'exchange_manager.py', 
            'environment_manager.py',
            'parameter_validator.py',
            'error_handler.py',
            'user_friendly_messages.py',
            'test_framework.py'
        ]
        
        self.results = {}
        
    def check_file_documentation(self, filepath: str) -> Dict:
        """检查单个文件的注释质量"""
        if not os.path.exists(filepath):
            return {
                'file': filepath,
                'exists': False,
                'error': '文件不存在'
            }
        
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 解析AST
            tree = ast.parse(content)
            
            result = {
                'file': filepath,
                'exists': True,
                'module_docstring': self._check_module_docstring(tree),
                'classes': self._check_classes_documentation(tree),
                'functions': self._check_functions_documentation(tree),
                'chinese_content': self._check_chinese_content(content),
                'docstring_format': self._check_docstring_format(content),
                'line_comments': self._check_line_comments(content)
            }
            
            return result
            
        except Exception as e:
            return {
                'file': filepath,
                'exists': True,
                'error': f'解析错误: {str(e)}'
            }
    
    def _check_module_docstring(self, tree: ast.AST) -> Dict:
        """检查模块级文档字符串"""
        module_doc = ast.get_docstring(tree)
        
        if not module_doc:
            return {'exists': False, 'quality': 'POOR'}
        
        # 检查文档字符串质量
        quality_score = 0
        details = []
        
        # 长度检查
        if len(module_doc) > 100:
            quality_score += 1
            details.append('长度充足')
        else:
            details.append('长度不足')
        
        # 中文内容检查
        if any('\u4e00' <= char <= '\u9fff' for char in module_doc):
            quality_score += 1
            details.append('包含中文')
        else:
            details.append('缺少中文')
        
        # 关键词检查
        keywords = ['功能', '模块', '系统', '策略', '管理', '验证', '错误', '测试']
        if any(keyword in module_doc for keyword in keywords):
            quality_score += 1
            details.append('包含关键词')
        
        quality = 'EXCELLENT' if quality_score >= 3 else 'GOOD' if quality_score >= 2 else 'POOR'
        
        return {
            'exists': True,
            'length': len(module_doc),
            'quality': quality,
            'details': details
        }
    
    def _check_classes_documentation(self, tree: ast.AST) -> List[Dict]:
        """检查类的文档字符串"""
        classes = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef):
                class_doc = ast.get_docstring(node)
                
                class_info = {
                    'name': node.name,
                    'has_docstring': bool(class_doc),
                    'docstring_length': len(class_doc) if class_doc else 0,
                    'methods_count': len([n for n in node.body if isinstance(n, ast.FunctionDef)]),
                    'documented_methods': 0
                }
                
                # 检查方法文档
                for method_node in node.body:
                    if isinstance(method_node, ast.FunctionDef):
                        method_doc = ast.get_docstring(method_node)
                        if method_doc:
                            class_info['documented_methods'] += 1
                
                # 计算文档覆盖率
                if class_info['methods_count'] > 0:
                    coverage = class_info['documented_methods'] / class_info['methods_count']
                    class_info['documentation_coverage'] = f"{coverage:.1%}"
                else:
                    class_info['documentation_coverage'] = "N/A"
                
                classes.append(class_info)
        
        return classes
    
    def _check_functions_documentation(self, tree: ast.AST) -> List[Dict]:
        """检查函数的文档字符串"""
        functions = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef) and not self._is_method(node, tree):
                func_doc = ast.get_docstring(node)
                
                func_info = {
                    'name': node.name,
                    'has_docstring': bool(func_doc),
                    'docstring_length': len(func_doc) if func_doc else 0,
                    'args_count': len(node.args.args),
                    'has_return': any(isinstance(n, ast.Return) for n in ast.walk(node))
                }
                
                functions.append(func_info)
        
        return functions
    
    def _is_method(self, func_node: ast.FunctionDef, tree: ast.AST) -> bool:
        """判断函数是否为类方法"""
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef):
                if func_node in node.body:
                    return True
        return False
    
    def _check_chinese_content(self, content: str) -> Dict:
        """检查中文内容比例"""
        total_chars = len(content)
        chinese_chars = sum(1 for char in content if '\u4e00' <= char <= '\u9fff')
        
        if total_chars > 0:
            chinese_ratio = chinese_chars / total_chars
        else:
            chinese_ratio = 0
        
        return {
            'total_chars': total_chars,
            'chinese_chars': chinese_chars,
            'chinese_ratio': f"{chinese_ratio:.2%}"
        }
    
    def _check_docstring_format(self, content: str) -> Dict:
        """检查文档字符串格式"""
        # 统计三引号文档字符串
        triple_quote_pattern = r'"""[\s\S]*?"""'
        docstrings = re.findall(triple_quote_pattern, content)
        
        format_info = {
            'docstring_count': len(docstrings),
            'avg_length': 0,
            'has_args_section': 0,
            'has_returns_section': 0,
            'has_examples': 0
        }
        
        if docstrings:
            total_length = sum(len(doc) for doc in docstrings)
            format_info['avg_length'] = total_length // len(docstrings)
            
            # 检查特定章节
            for doc in docstrings:
                if 'Args:' in doc or '参数:' in doc:
                    format_info['has_args_section'] += 1
                if 'Returns:' in doc or '返回:' in doc:
                    format_info['has_returns_section'] += 1
                if '示例:' in doc or 'Example' in doc or '```' in doc:
                    format_info['has_examples'] += 1
        
        return format_info
    
    def _check_line_comments(self, content: str) -> Dict:
        """检查行内注释"""
        lines = content.split('\n')
        
        comment_info = {
            'total_lines': len(lines),
            'comment_lines': 0,
            'chinese_comment_lines': 0,
            'comment_ratio': 0
        }
        
        for line in lines:
            stripped = line.strip()
            if stripped.startswith('#'):
                comment_info['comment_lines'] += 1
                if any('\u4e00' <= char <= '\u9fff' for char in stripped):
                    comment_info['chinese_comment_lines'] += 1
        
        if comment_info['total_lines'] > 0:
            comment_info['comment_ratio'] = f"{comment_info['comment_lines'] / comment_info['total_lines']:.2%}"
        
        return comment_info
    
    def run_quality_check(self) -> Dict:
        """运行完整的质量检查"""
        print("🔍 开始代码注释质量检查...")
        print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        
        all_results = {}
        
        for filename in self.target_files:
            print(f"\n📄 检查文件: {filename}")
            result = self.check_file_documentation(filename)
            all_results[filename] = result
            
            if 'error' in result:
                print(f"   ❌ {result['error']}")
                continue
            
            # 显示检查结果
            print(f"   📝 模块文档: {'✅' if result['module_docstring']['exists'] else '❌'}")
            print(f"   🏗️  类数量: {len(result['classes'])}")
            print(f"   🔧 函数数量: {len(result['functions'])}")
            print(f"   🇨🇳 中文比例: {result['chinese_content']['chinese_ratio']}")
            print(f"   💬 注释比例: {result['line_comments']['comment_ratio']}")
        
        return all_results
    
    def generate_quality_report(self, results: Dict) -> str:
        """生成质量报告"""
        report = f"""
# 代码注释质量检查报告

## 检查概览
- **检查时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- **检查文件数**: {len(self.target_files)}
- **检查工具**: 代码注释质量检查器 v1.0

## 详细检查结果

"""
        
        for filename, result in results.items():
            if 'error' in result:
                report += f"### ❌ {filename}\n**错误**: {result['error']}\n\n"
                continue
            
            report += f"### 📄 {filename}\n\n"
            
            # 模块文档
            module_doc = result['module_docstring']
            if module_doc['exists']:
                report += f"- **模块文档**: ✅ 存在 ({module_doc['length']} 字符, 质量: {module_doc['quality']})\n"
            else:
                report += f"- **模块文档**: ❌ 缺失\n"
            
            # 类文档
            classes = result['classes']
            if classes:
                documented_classes = sum(1 for c in classes if c['has_docstring'])
                report += f"- **类文档**: {documented_classes}/{len(classes)} 个类有文档\n"
                
                for cls in classes:
                    report += f"  - `{cls['name']}`: {'✅' if cls['has_docstring'] else '❌'} "
                    report += f"(方法文档覆盖率: {cls['documentation_coverage']})\n"
            
            # 中文内容
            chinese = result['chinese_content']
            report += f"- **中文内容**: {chinese['chinese_ratio']} ({chinese['chinese_chars']}/{chinese['total_chars']} 字符)\n"
            
            # 注释比例
            comments = result['line_comments']
            report += f"- **行内注释**: {comments['comment_ratio']} ({comments['comment_lines']}/{comments['total_lines']} 行)\n"
            
            report += "\n"
        
        # 总结
        report += "## 📊 质量评估\n\n"
        
        total_files = len([r for r in results.values() if 'error' not in r])
        files_with_module_doc = len([r for r in results.values() if 'error' not in r and r['module_docstring']['exists']])
        
        report += f"- **模块文档覆盖率**: {files_with_module_doc}/{total_files} ({files_with_module_doc/total_files:.1%})\n"
        
        # 计算平均中文比例
        chinese_ratios = []
        for result in results.values():
            if 'error' not in result:
                ratio_str = result['chinese_content']['chinese_ratio'].rstrip('%')
                chinese_ratios.append(float(ratio_str))
        
        if chinese_ratios:
            avg_chinese = sum(chinese_ratios) / len(chinese_ratios)
            report += f"- **平均中文内容比例**: {avg_chinese:.1f}%\n"
        
        report += "\n## 🎯 改进建议\n\n"
        
        if files_with_module_doc < total_files:
            report += "1. **补充模块文档**: 为缺少文档的模块添加详细说明\n"
        
        if avg_chinese < 10:
            report += "2. **增加中文注释**: 提高中文注释的比例，便于理解\n"
        
        report += "3. **持续改进**: 定期检查和更新注释内容\n"
        report += "4. **标准化**: 统一注释格式和风格\n"
        
        return report

def main():
    """主函数"""
    checker = DocumentationChecker()
    results = checker.run_quality_check()
    
    # 生成报告
    report = checker.generate_quality_report(results)
    
    # 保存报告
    report_filename = f"documentation_quality_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
    
    try:
        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"\n📋 质量检查报告已生成: {report_filename}")
        
    except Exception as e:
        print(f"\n❌ 报告生成失败: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 代码注释质量检查完成！")

if __name__ == "__main__":
    main()
