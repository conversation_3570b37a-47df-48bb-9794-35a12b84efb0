#!/usr/bin/env python3
"""
测试增强的参数验证和错误处理
验证中文错误消息和用户友好提示
"""

import sys
import os
from datetime import datetime

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_parameter_validation():
    """测试参数验证功能"""
    print("=" * 60)
    print("测试参数验证功能")
    print("=" * 60)
    
    try:
        from parameter_validator import validator, ValidationError
        print("✓ 参数验证器导入成功")
        
        # 测试交易对验证
        print("\n1. 测试交易对验证:")
        
        # 有效交易对
        try:
            validator.validate_symbol("CFX/USDT")
            print("  ✓ 有效交易对 'CFX/USDT' 验证通过")
        except ValidationError as e:
            print(f"  ✗ 有效交易对验证失败: {e}")
        
        # 无效交易对
        try:
            validator.validate_symbol("INVALID")
            print("  ✗ 无效交易对应该被拒绝")
        except ValidationError as e:
            print(f"  ✓ 无效交易对被正确拒绝: {e}")
        
        # 测试价格验证
        print("\n2. 测试价格验证:")
        
        # 有效价格
        try:
            validator.validate_price(0.213456)
            print("  ✓ 有效价格 0.213456 验证通过")
        except ValidationError as e:
            print(f"  ✗ 有效价格验证失败: {e}")
        
        # 负价格
        try:
            validator.validate_price(-0.1)
            print("  ✗ 负价格应该被拒绝")
        except ValidationError as e:
            print(f"  ✓ 负价格被正确拒绝: {e}")
        
        # 测试数量验证
        print("\n3. 测试数量验证:")
        
        # 有效数量
        try:
            validator.validate_amount(100)
            print("  ✓ 有效数量 100 验证通过")
        except ValidationError as e:
            print(f"  ✗ 有效数量验证失败: {e}")
        
        # 零数量
        try:
            validator.validate_amount(0)
            print("  ✗ 零数量应该被拒绝")
        except ValidationError as e:
            print(f"  ✓ 零数量被正确拒绝: {e}")
        
        # 测试网格参数验证
        print("\n4. 测试网格参数验证:")
        
        # 有效网格参数
        try:
            validator.validate_grid_parameters(0.213456, 0.02, 10, 100)
            print("  ✓ 有效网格参数验证通过")
        except ValidationError as e:
            print(f"  ✗ 有效网格参数验证失败: {e}")
        
        # 网格间距过小
        try:
            validator.validate_grid_parameters(0.213456, 0.0001, 10, 100)
            print("  ✗ 过小网格间距应该被拒绝")
        except ValidationError as e:
            print(f"  ✓ 过小网格间距被正确拒绝: {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ 参数验证测试失败: {e}")
        return False

def test_error_handling():
    """测试错误处理功能"""
    print("\n" + "=" * 60)
    print("测试错误处理功能")
    print("=" * 60)
    
    try:
        from error_handler import error_handler, ErrorCode
        print("✓ 错误处理器导入成功")
        
        # 测试网络错误处理
        print("\n1. 测试网络错误处理:")
        
        network_error = ConnectionError("Connection timeout")
        error_details = error_handler.handle_error(network_error)
        
        print(f"  错误代码: {error_details['error_code']}")
        print(f"  错误标题: {error_details['title']}")
        print(f"  错误消息: {error_details['message']}")
        print(f"  建议数量: {len(error_details['suggestions'])}")
        
        # 测试用户友好消息格式化
        user_message = error_handler.format_user_message(error_details)
        print(f"\n  用户友好消息长度: {len(user_message)} 字符")
        has_chinese = any('\u4e00' <= char <= '\u9fff' for char in user_message)
        print(f"  包含中文: {'是' if has_chinese else '否'}")
        
        # 测试API错误处理
        print("\n2. 测试API错误处理:")
        
        api_error = Exception("Invalid API key")
        error_details = error_handler.handle_error(api_error)
        
        print(f"  错误代码: {error_details['error_code']}")
        print(f"  错误标题: {error_details['title']}")
        
        return True
        
    except Exception as e:
        print(f"✗ 错误处理测试失败: {e}")
        return False

def test_strategy_validation():
    """测试策略参数验证"""
    print("\n" + "=" * 60)
    print("测试策略参数验证")
    print("=" * 60)
    
    try:
        from test_framework import test_framework
        mock_exchange = test_framework.create_mock_exchange()
        print("✓ 模拟交易所创建成功")
        
        # 测试网格交易策略验证
        print("\n1. 测试网格交易策略验证:")
        
        try:
            from strategies import GridTrading
            
            # 有效参数
            strategy = GridTrading(
                exchange=mock_exchange,
                symbol='CFX/USDT',
                base_price=0.213456,
                grid_spacing=2.0,
                grid_count=10,
                order_amount=100
            )
            print("  ✓ 有效网格交易参数创建成功")
        except Exception as e:
            print(f"  ✗ 有效网格交易参数创建失败: {e}")
        
        # 无效参数 - 负价格
        try:
            strategy = GridTrading(
                exchange=mock_exchange,
                symbol='CFX/USDT',
                base_price=-0.1,
                grid_spacing=2.0,
                grid_count=10,
                order_amount=100
            )
            print("  ✗ 负价格参数应该被拒绝")
        except Exception as e:
            print(f"  ✓ 负价格参数被正确拒绝")
            # 检查是否包含中文错误消息
            error_str = str(e)
            has_chinese = any('\u4e00' <= char <= '\u9fff' for char in error_str)
            if has_chinese:
                print("    ✓ 错误消息包含中文")
            else:
                print("    ✗ 错误消息不包含中文")
        
        # 测试移动平均线策略验证
        print("\n2. 测试移动平均线策略验证:")
        
        try:
            from strategies import MovingAverageStrategy
            
            # 有效参数
            strategy = MovingAverageStrategy(
                exchange=mock_exchange,
                symbol='CFX/USDT',
                short_period=5,
                long_period=20,
                amount=100
            )
            print("  ✓ 有效移动平均线参数创建成功")
        except Exception as e:
            print(f"  ✗ 有效移动平均线参数创建失败: {e}")
        
        # 无效参数 - 短期大于长期
        try:
            strategy = MovingAverageStrategy(
                exchange=mock_exchange,
                symbol='CFX/USDT',
                short_period=30,
                long_period=10,
                amount=100
            )
            print("  ✗ 短期大于长期的参数应该被拒绝")
        except Exception as e:
            print(f"  ✓ 短期大于长期的参数被正确拒绝")
            error_str = str(e)
            has_chinese = any('\u4e00' <= char <= '\u9fff' for char in error_str)
            if has_chinese:
                print("    ✓ 错误消息包含中文")
        
        return True
        
    except Exception as e:
        print(f"✗ 策略验证测试失败: {e}")
        return False

def test_user_messages():
    """测试用户友好消息"""
    print("\n" + "=" * 60)
    print("测试用户友好消息")
    print("=" * 60)
    
    try:
        from user_friendly_messages import user_messages
        print("✓ 用户消息中心导入成功")
        
        # 测试成功消息模板
        print("\n1. 测试消息模板:")
        
        success_templates = user_messages.success_messages
        warning_templates = user_messages.warning_messages
        guidance_templates = user_messages.guidance_messages
        
        print(f"  成功消息模板数量: {len(success_templates)}")
        print(f"  警告消息模板数量: {len(warning_templates)}")
        print(f"  指导消息模板数量: {len(guidance_templates)}")
        
        # 检查模板内容
        for template_name, template in success_templates.items():
            if all(key in template for key in ['title', 'message', 'tips']):
                print(f"  ✓ 成功消息模板 '{template_name}' 结构完整")
            else:
                print(f"  ✗ 成功消息模板 '{template_name}' 结构不完整")
        
        # 测试中文内容
        print("\n2. 测试中文内容:")
        
        chinese_count = 0
        total_templates = len(success_templates) + len(warning_templates) + len(guidance_templates)
        
        for templates in [success_templates, warning_templates, guidance_templates]:
            for template in templates.values():
                if isinstance(template, dict):
                    for value in template.values():
                        if isinstance(value, str):
                            has_chinese = any('\u4e00' <= char <= '\u9fff' for char in value)
                            if has_chinese:
                                chinese_count += 1
                                break
                        elif isinstance(value, list):
                            for item in value:
                                if isinstance(item, str):
                                    has_chinese = any('\u4e00' <= char <= '\u9fff' for char in item)
                                    if has_chinese:
                                        chinese_count += 1
                                        break
        
        print(f"  包含中文的模板: {chinese_count}/{total_templates}")
        
        return True
        
    except Exception as e:
        print(f"✗ 用户消息测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 参数验证增强和错误消息本地化测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tests = [
        ("参数验证功能", test_parameter_validation),
        ("错误处理功能", test_error_handling),
        ("策略参数验证", test_strategy_validation),
        ("用户友好消息", test_user_messages)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"\n✅ {test_name} 测试通过")
            else:
                print(f"\n❌ {test_name} 测试失败")
        except Exception as e:
            print(f"\n🔥 {test_name} 测试异常: {e}")
    
    print(f"\n{'='*60}")
    print("测试结果摘要")
    print(f"{'='*60}")
    print(f"总测试项: {total}")
    print(f"通过: {passed}")
    print(f"失败: {total - passed}")
    print(f"通过率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有测试通过！参数验证增强和错误消息本地化功能正常。")
        print("\n✨ 改进效果:")
        print("  • 参数验证更加严格，提供详细的边界条件检查")
        print("  • 错误消息完全中文化，用户体验更友好")
        print("  • 提供具体的解决建议和操作指导")
        print("  • 统一的错误处理机制，便于维护")
    else:
        print("\n⚠️ 部分测试失败，请检查相关功能。")
    
    print(f"\n{'='*60}")

if __name__ == "__main__":
    main()
