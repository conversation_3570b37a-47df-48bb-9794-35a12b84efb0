#!/usr/bin/env python3
"""
参数验证器模块

本模块提供了量化交易系统的统一参数验证功能，确保所有输入参数都在合理范围内，
避免因参数设置不当导致的策略失败或资金损失。

核心特性：
1. 全面验证：覆盖所有参数类型的验证规则
2. 中文提示：所有错误消息都是中文，便于理解
3. 智能建议：每个错误都提供具体的解决建议
4. 业务逻辑：不仅验证格式，还验证业务合理性
5. 可扩展性：易于添加新的验证规则

验证范围：
- 交易对格式验证：确保符合标准格式
- 价格参数验证：检查价格范围和合理性
- 数量参数验证：验证交易数量和精度
- 百分比验证：确保百分比在有效范围内
- 策略专用验证：针对不同策略的特殊验证

设计理念：
- 早期发现：在参数设置阶段就发现问题
- 用户友好：提供清晰的错误说明和建议
- 安全第一：严格的边界检查防止风险
- 标准化：统一的验证接口和错误格式

使用场景：
- 策略参数初始化验证
- 用户输入参数检查
- API参数合法性验证
- 配置文件参数验证

作者：量化交易系统开发团队
版本：v2.0 (增强版，包含中文错误消息)
"""

import re
from typing import Union, List, Dict, Any, Optional
from decimal import Decimal
from logger import get_logger

class ValidationError(Exception):
    """
    参数验证错误异常类

    这是一个自定义异常类，用于表示参数验证失败的情况。
    相比普通的异常，它提供了更丰富的错误信息，包括错误代码和解决建议。

    属性：
        message (str): 错误消息，描述具体的验证失败原因
        error_code (str): 错误代码，用于程序化处理错误
        suggestions (List[str]): 解决建议列表，帮助用户修正参数

    使用示例：
    ```python
    raise ValidationError(
        "价格必须大于0",
        error_code="NEGATIVE_PRICE",
        suggestions=["请输入正数价格", "最小价格: 0.000001"]
    )
    ```
    """
    def __init__(self, message: str, error_code: str = None, suggestions: List[str] = None):
        """
        初始化验证错误

        Args:
            message (str): 错误消息
            error_code (str, optional): 错误代码
            suggestions (List[str], optional): 解决建议列表
        """
        super().__init__(message)
        self.error_code = error_code
        self.suggestions = suggestions or []

class ParameterValidator:
    """
    参数验证器类

    这是量化交易系统的参数验证核心，提供全面的参数检查功能。
    它不仅验证参数的格式和类型，还会检查参数的业务合理性。

    验证功能：
    1. 基础验证：类型、格式、范围检查
    2. 业务验证：根据交易业务逻辑进行合理性检查
    3. 安全验证：防止可能导致风险的参数配置
    4. 智能提示：为每种错误提供具体的修正建议

    验证规则：
    - 交易对：必须符合 BASE/QUOTE 格式，如 CFX/USDT
    - 价格：必须为正数，在合理范围内
    - 数量：必须为正数，精度不超过限制
    - 百分比：必须在 0-100% 范围内
    - 整数：必须在指定范围内

    特色功能：
    - 中文错误消息：所有错误都有中文说明
    - 解决建议：每个错误都提供具体的修正方法
    - 上下文感知：根据参数类型提供针对性建议
    - 可配置限制：可以调整各种参数的限制范围

    使用示例：
    ```python
    validator = ParameterValidator()

    # 验证交易对
    validator.validate_symbol('CFX/USDT')

    # 验证价格
    validator.validate_price(0.213456)

    # 验证网格参数
    validator.validate_grid_parameters(0.213456, 0.02, 10, 100)
    ```
    """

    def __init__(self):
        """
        初始化参数验证器

        设置验证规则、限制参数和常用配置。
        所有的验证标准都在这里定义。
        """
        # 日志记录器，用于记录验证过程和错误
        self.logger = get_logger("parameter_validator")

        # 交易对格式的正则表达式：基础货币/计价货币
        # 例如：BTC/USDT, CFX/USDT, ETH/BTC
        self.valid_symbol_pattern = re.compile(r'^[A-Z0-9]{2,10}/[A-Z0-9]{2,10}$')

        # 常见交易对列表，用于提供建议
        self.common_symbols = [
            'BTC/USDT', 'ETH/USDT', 'CFX/USDT', 'BNB/USDT',
            'ADA/USDT', 'DOT/USDT', 'LINK/USDT', 'UNI/USDT'
        ]
        
        # 价格范围限制 (USDT计价)
        self.price_limits = {
            'min_price': 0.000001,  # 最小价格
            'max_price': 1000000,   # 最大价格
            'max_deviation': 0.5    # 最大偏离比例 50%
        }
        
        # 数量限制
        self.amount_limits = {
            'min_amount': 0.000001,  # 最小数量
            'max_amount': 1000000,   # 最大数量
            'precision': 8           # 最大精度
        }
        
        # 百分比限制
        self.percentage_limits = {
            'min_percentage': 0.0001,  # 0.01%
            'max_percentage': 1.0      # 100%
        }
    
    def validate_symbol(self, symbol: str) -> bool:
        """验证交易对格式"""
        if not isinstance(symbol, str):
            raise ValidationError(
                "交易对必须是字符串类型",
                "INVALID_SYMBOL_TYPE",
                ["请输入正确的交易对格式，如 'CFX/USDT'"]
            )
        
        if not symbol:
            raise ValidationError(
                "交易对不能为空",
                "EMPTY_SYMBOL",
                ["请输入交易对，如 'CFX/USDT'", "常见交易对: " + ", ".join(self.common_symbols[:5])]
            )
        
        if not self.valid_symbol_pattern.match(symbol.upper()):
            raise ValidationError(
                f"交易对格式不正确: {symbol}",
                "INVALID_SYMBOL_FORMAT",
                [
                    "正确格式: 基础货币/计价货币，如 'CFX/USDT'",
                    "货币代码应为2-10位大写字母或数字",
                    f"常见交易对: {', '.join(self.common_symbols[:3])}"
                ]
            )
        
        return True
    
    def validate_price(self, price: Union[float, int, str], current_price: float = None) -> bool:
        """验证价格参数"""
        try:
            price_value = float(price)
        except (ValueError, TypeError):
            raise ValidationError(
                f"价格必须是数字类型: {price}",
                "INVALID_PRICE_TYPE",
                ["请输入有效的数字价格", "例如: 0.213456"]
            )
        
        if price_value <= 0:
            raise ValidationError(
                f"价格必须大于0: {price_value}",
                "NEGATIVE_PRICE",
                ["请输入正数价格", "最小价格: 0.000001"]
            )
        
        if price_value < self.price_limits['min_price']:
            raise ValidationError(
                f"价格过小: {price_value}，最小价格: {self.price_limits['min_price']}",
                "PRICE_TOO_SMALL",
                [f"请输入大于 {self.price_limits['min_price']} 的价格"]
            )
        
        if price_value > self.price_limits['max_price']:
            raise ValidationError(
                f"价格过大: {price_value}，最大价格: {self.price_limits['max_price']}",
                "PRICE_TOO_LARGE",
                [f"请输入小于 {self.price_limits['max_price']} 的价格"]
            )
        
        # 如果提供了当前价格，检查偏离度
        if current_price is not None and current_price > 0:
            deviation = abs(price_value - current_price) / current_price
            if deviation > self.price_limits['max_deviation']:
                raise ValidationError(
                    f"价格偏离当前价格过大: {deviation:.1%}，最大允许偏离: {self.price_limits['max_deviation']:.1%}",
                    "PRICE_DEVIATION_TOO_LARGE",
                    [
                        f"当前价格: {current_price}",
                        f"建议价格范围: {current_price * (1 - self.price_limits['max_deviation']):.6f} - {current_price * (1 + self.price_limits['max_deviation']):.6f}"
                    ]
                )
        
        return True
    
    def validate_amount(self, amount: Union[float, int, str]) -> bool:
        """验证数量参数"""
        try:
            amount_value = float(amount)
        except (ValueError, TypeError):
            raise ValidationError(
                f"数量必须是数字类型: {amount}",
                "INVALID_AMOUNT_TYPE",
                ["请输入有效的数字数量", "例如: 100"]
            )
        
        if amount_value <= 0:
            raise ValidationError(
                f"数量必须大于0: {amount_value}",
                "NEGATIVE_AMOUNT",
                ["请输入正数数量", f"最小数量: {self.amount_limits['min_amount']}"]
            )
        
        if amount_value < self.amount_limits['min_amount']:
            raise ValidationError(
                f"数量过小: {amount_value}，最小数量: {self.amount_limits['min_amount']}",
                "AMOUNT_TOO_SMALL",
                [f"请输入大于 {self.amount_limits['min_amount']} 的数量"]
            )
        
        if amount_value > self.amount_limits['max_amount']:
            raise ValidationError(
                f"数量过大: {amount_value}，最大数量: {self.amount_limits['max_amount']}",
                "AMOUNT_TOO_LARGE",
                [f"请输入小于 {self.amount_limits['max_amount']} 的数量"]
            )
        
        # 检查精度
        decimal_places = len(str(amount_value).split('.')[-1]) if '.' in str(amount_value) else 0
        if decimal_places > self.amount_limits['precision']:
            raise ValidationError(
                f"数量精度过高: {decimal_places} 位小数，最大精度: {self.amount_limits['precision']} 位",
                "AMOUNT_PRECISION_TOO_HIGH",
                [f"请将数量精度控制在 {self.amount_limits['precision']} 位小数以内"]
            )
        
        return True
    
    def validate_percentage(self, percentage: Union[float, int, str], allow_zero: bool = False) -> bool:
        """验证百分比参数"""
        try:
            pct_value = float(percentage)
        except (ValueError, TypeError):
            raise ValidationError(
                f"百分比必须是数字类型: {percentage}",
                "INVALID_PERCENTAGE_TYPE",
                ["请输入有效的数字百分比", "例如: 0.02 (表示2%)"]
            )
        
        if not allow_zero and pct_value <= 0:
            raise ValidationError(
                f"百分比必须大于0: {pct_value}",
                "NEGATIVE_PERCENTAGE",
                ["请输入正数百分比", f"最小百分比: {self.percentage_limits['min_percentage']} (0.01%)"]
            )
        
        if allow_zero and pct_value < 0:
            raise ValidationError(
                f"百分比不能为负数: {pct_value}",
                "NEGATIVE_PERCENTAGE",
                ["请输入非负数百分比"]
            )
        
        if pct_value > self.percentage_limits['max_percentage']:
            raise ValidationError(
                f"百分比过大: {pct_value}，最大百分比: {self.percentage_limits['max_percentage']} (100%)",
                "PERCENTAGE_TOO_LARGE",
                [f"请输入小于 {self.percentage_limits['max_percentage']} (100%) 的百分比"]
            )
        
        return True
    
    def validate_integer(self, value: Union[int, str], min_value: int = 1, max_value: int = 1000) -> bool:
        """验证整数参数"""
        try:
            int_value = int(value)
        except (ValueError, TypeError):
            raise ValidationError(
                f"参数必须是整数类型: {value}",
                "INVALID_INTEGER_TYPE",
                ["请输入有效的整数", f"例如: {min_value}"]
            )
        
        if int_value < min_value:
            raise ValidationError(
                f"参数过小: {int_value}，最小值: {min_value}",
                "INTEGER_TOO_SMALL",
                [f"请输入大于等于 {min_value} 的整数"]
            )
        
        if int_value > max_value:
            raise ValidationError(
                f"参数过大: {int_value}，最大值: {max_value}",
                "INTEGER_TOO_LARGE",
                [f"请输入小于等于 {max_value} 的整数"]
            )
        
        return True
    
    def validate_grid_parameters(self, base_price: float, grid_spacing: float, 
                                grid_count: int, order_amount: float) -> bool:
        """验证网格交易参数"""
        # 验证基础参数
        self.validate_price(base_price)
        self.validate_percentage(grid_spacing)
        self.validate_integer(grid_count, min_value=2, max_value=100)
        self.validate_amount(order_amount)
        
        # 网格特定验证
        if grid_spacing < 0.001:  # 0.1%
            raise ValidationError(
                f"网格间距过小: {grid_spacing:.4f}，建议最小间距: 0.001 (0.1%)",
                "GRID_SPACING_TOO_SMALL",
                ["网格间距过小可能导致频繁交易", "建议间距: 0.01-0.05 (1%-5%)"]
            )
        
        if grid_spacing > 0.2:  # 20%
            raise ValidationError(
                f"网格间距过大: {grid_spacing:.4f}，建议最大间距: 0.2 (20%)",
                "GRID_SPACING_TOO_LARGE",
                ["网格间距过大可能错过交易机会", "建议间距: 0.01-0.05 (1%-5%)"]
            )
        
        if grid_count > 50:
            raise ValidationError(
                f"网格数量过多: {grid_count}，建议最大数量: 50",
                "GRID_COUNT_TOO_LARGE",
                ["网格数量过多可能影响性能", "建议数量: 10-30"]
            )
        
        # 计算总投资金额
        total_investment = base_price * order_amount * grid_count / 2
        if total_investment > 100000:  # 10万USDT
            raise ValidationError(
                f"总投资金额过大: {total_investment:.2f} USDT",
                "TOTAL_INVESTMENT_TOO_LARGE",
                [
                    "请减少网格数量或单笔订单金额",
                    f"当前配置需要约 {total_investment:.2f} USDT"
                ]
            )
        
        return True
    
    def validate_ma_parameters(self, short_period: int, long_period: int, amount: float) -> bool:
        """验证移动平均线参数"""
        self.validate_integer(short_period, min_value=2, max_value=100)
        self.validate_integer(long_period, min_value=5, max_value=500)
        self.validate_amount(amount)
        
        # 移动平均线特定验证
        if short_period >= long_period:
            raise ValidationError(
                f"短期均线周期 ({short_period}) 必须小于长期均线周期 ({long_period})",
                "INVALID_MA_PERIODS",
                [
                    "短期均线用于捕捉快速变化",
                    "长期均线用于确定趋势方向",
                    "建议配置: 短期5-20，长期20-100"
                ]
            )
        
        if long_period / short_period < 2:
            raise ValidationError(
                f"长短期均线周期比例过小: {long_period/short_period:.1f}，建议至少2倍",
                "MA_RATIO_TOO_SMALL",
                [
                    "比例过小可能导致信号不明显",
                    "建议比例: 3-5倍",
                    f"例如: 短期{short_period}，长期{short_period*3}-{short_period*5}"
                ]
            )
        
        return True
    
    def validate_rsi_parameters(self, period: int, oversold: float, overbought: float, amount: float) -> bool:
        """验证RSI参数"""
        self.validate_integer(period, min_value=5, max_value=50)
        self.validate_amount(amount)
        
        # RSI特定验证
        if not (0 < oversold < 50):
            raise ValidationError(
                f"RSI超卖阈值无效: {oversold}，应在0-50之间",
                "INVALID_RSI_OVERSOLD",
                ["超卖阈值通常设置在20-40之间", "推荐值: 30"]
            )
        
        if not (50 < overbought < 100):
            raise ValidationError(
                f"RSI超买阈值无效: {overbought}，应在50-100之间",
                "INVALID_RSI_OVERBOUGHT",
                ["超买阈值通常设置在60-80之间", "推荐值: 70"]
            )
        
        if overbought - oversold < 20:
            raise ValidationError(
                f"RSI超买超卖阈值间距过小: {overbought - oversold}，建议至少20",
                "RSI_THRESHOLD_GAP_TOO_SMALL",
                [
                    "间距过小可能导致频繁交易",
                    f"建议配置: 超卖{oversold}，超买{oversold + 40}"
                ]
            )
        
        return True

# 全局验证器实例
validator = ParameterValidator()
