
# 修复价格获取的代码片段
def get_real_price(self, symbol='CFX/USDT'):
    """获取真实价格的改进版本"""
    try:
        # 方法1: 使用ticker
        ticker = self.exchange.fetch_ticker(symbol)
        price = float(ticker['last'])
        
        if price > 0:
            return price
        
        # 方法2: 使用orderbook
        orderbook = self.exchange.fetch_order_book(symbol, limit=5)
        if orderbook['bids'] and orderbook['asks']:
            bid_price = float(orderbook['bids'][0][0])
            ask_price = float(orderbook['asks'][0][0])
            price = (bid_price + ask_price) / 2
            return price
        
        # 方法3: 使用最近交易
        trades = self.exchange.fetch_trades(symbol, limit=1)
        if trades:
            price = float(trades[0]['price'])
            return price
            
        return None
        
    except Exception as e:
        print(f"获取价格失败: {e}")
        return None

# 在网格策略中的使用示例
def update_price_in_grid_strategy():
    """在网格策略中更新价格获取逻辑"""
    
    # 替换原来的价格获取代码
    # 原代码可能是:
    # current_price = 0.21  # 硬编码的默认值
    
    # 新代码:
    current_price = self.get_real_price(self.symbol)
    
    if current_price is None:
        self.logger.error("无法获取当前价格，策略暂停")
        return
    
    if current_price <= 0:
        self.logger.error(f"价格异常: {current_price}")
        return
    
    self.logger.info(f"当前价格: {current_price:.6f}")
    
    # 继续网格策略逻辑...
