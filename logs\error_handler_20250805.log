2025-08-05 17:46:23 - ERROR - 错误处理: 网络连接超时 - 连接交易所服务器超时
2025-08-05 17:46:23 - ERROR - 错误代码: NETWORK_002
2025-08-05 17:46:23 - ERROR - 原始错误: Connection timeout
2025-08-05 17:46:23 - DEBUG - 堆栈跟踪: NoneType: None

2025-08-05 17:46:23 - ERROR - 错误处理: API密钥无效 - 提供的API密钥无效或已过期
2025-08-05 17:46:23 - ERROR - 错误代码: API_001
2025-08-05 17:46:23 - ERROR - 原始错误: Invalid API key
2025-08-05 17:46:23 - DEBUG - 堆栈跟踪: NoneType: None

2025-08-05 17:46:27 - ERROR - 错误处理: 系统错误 - 系统发生未知错误
2025-08-05 17:46:27 - ERROR - 错误代码: SYSTEM_001
2025-08-05 17:46:27 - ERROR - 原始错误: 价格必须大于0: -0.1
2025-08-05 17:46:27 - ERROR - 错误上下文: {'strategy': 'GridTrading', 'symbol': 'CFX/USDT', 'base_price': -0.1, 'grid_spacing': 2.0, 'grid_count': 10, 'order_amount': 100}
2025-08-05 17:46:27 - DEBUG - 堆栈跟踪: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\yl\strategies.py", line 20, in __init__
    self._validate_parameters(symbol, base_price, grid_spacing, grid_count, order_amount)
  File "C:\Users\<USER>\Desktop\yl\strategies.py", line 54, in _validate_parameters
    validator.validate_grid_parameters(base_price, grid_spacing/100, grid_count, order_amount)
  File "C:\Users\<USER>\Desktop\yl\parameter_validator.py", line 238, in validate_grid_parameters
    self.validate_price(base_price)
  File "C:\Users\<USER>\Desktop\yl\parameter_validator.py", line 95, in validate_price
    raise ValidationError(
parameter_validator.ValidationError: 价格必须大于0: -0.1

2025-08-05 17:46:27 - ERROR - 错误处理: 系统错误 - 系统发生未知错误
2025-08-05 17:46:27 - ERROR - 错误代码: SYSTEM_001
2025-08-05 17:46:27 - ERROR - 原始错误: 短期均线周期 (30) 必须小于长期均线周期 (10)
2025-08-05 17:46:27 - ERROR - 错误上下文: {'strategy': 'MovingAverageStrategy', 'symbol': 'CFX/USDT', 'short_period': 30, 'long_period': 10, 'amount': 100}
2025-08-05 17:46:27 - DEBUG - 堆栈跟踪: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\yl\strategies.py", line 196, in __init__
    self._validate_parameters(symbol, short_period, long_period, amount)
  File "C:\Users\<USER>\Desktop\yl\strategies.py", line 231, in _validate_parameters
    validator.validate_ma_parameters(short_period, long_period, amount)
  File "C:\Users\<USER>\Desktop\yl\parameter_validator.py", line 287, in validate_ma_parameters
    raise ValidationError(
parameter_validator.ValidationError: 短期均线周期 (30) 必须小于长期均线周期 (10)

