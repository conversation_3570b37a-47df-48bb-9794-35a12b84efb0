#!/usr/bin/env python3
"""
测试价格修复效果
"""

import ccxt
import time
from strategies import GridTrading
from logger import get_logger

def test_price_retrieval():
    """测试价格获取功能"""
    print("=" * 60)
    print("测试价格获取功能")
    print("=" * 60)
    
    try:
        # 创建交易所实例（使用OKX测试）
        exchange = ccxt.okx({
            'enableRateLimit': True,
            'timeout': 30000,
        })
        
        # 测试CFX/USDT价格获取
        symbol = 'CFX/USDT'
        
        print(f"测试交易对: {symbol}")
        
        # 创建网格交易实例
        grid_strategy = GridTrading(
            exchange=exchange,
            symbol=symbol,
            base_price=0.21,  # 基准价格
            grid_spacing=2.0,  # 2%网格间距
            grid_count=10,     # 10个网格
            order_amount=100   # 每次100个CFX
        )
        
        print("\n测试价格获取方法:")
        
        # 测试多次价格获取
        for i in range(5):
            try:
                price = grid_strategy.get_current_price()
                print(f"第{i+1}次获取: {price:.6f} USDT")
                time.sleep(1)
            except Exception as e:
                print(f"第{i+1}次获取失败: {e}")
        
        print(f"\n缓存的最后价格: {grid_strategy.last_price}")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        return False

def test_grid_strategy_simulation():
    """模拟测试网格策略（不实际下单）"""
    print("\n" + "=" * 60)
    print("模拟网格策略测试")
    print("=" * 60)
    
    try:
        # 创建模拟交易所
        class MockExchange:
            def __init__(self):
                self.current_price = 0.213  # 模拟当前价格
                self.price_change = 0.001   # 价格变化幅度
                
            def fetch_ticker(self, symbol):
                # 模拟价格波动
                import random
                self.current_price += random.uniform(-self.price_change, self.price_change)
                return {'last': self.current_price}
            
            def fetch_order_book(self, symbol, limit=5):
                bid = self.current_price - 0.0001
                ask = self.current_price + 0.0001
                return {
                    'bids': [[bid, 1000]],
                    'asks': [[ask, 1000]]
                }
            
            def fetch_trades(self, symbol, limit=1):
                return [{'price': self.current_price}]
        
        # 创建模拟网格策略
        mock_exchange = MockExchange()
        grid_strategy = GridTrading(
            exchange=mock_exchange,
            symbol='CFX/USDT',
            base_price=0.21,
            grid_spacing=2.0,
            grid_count=10,
            order_amount=100
        )
        
        print("开始模拟价格监控...")
        print("(按Ctrl+C停止)")
        
        # 模拟运行10次价格检查
        for i in range(10):
            try:
                price = grid_strategy.get_current_price()
                print(f"[模拟] 第{i+1}次 - 当前价格: {price:.6f} USDT")
                time.sleep(2)
            except KeyboardInterrupt:
                print("\n模拟测试停止")
                break
            except Exception as e:
                print(f"模拟测试错误: {e}")
        
        return True
        
    except Exception as e:
        print(f"模拟测试失败: {e}")
        return False

def test_real_exchange_connection():
    """测试真实交易所连接"""
    print("\n" + "=" * 60)
    print("真实交易所连接测试")
    print("=" * 60)
    
    exchanges_to_test = [
        ('Binance', ccxt.binance),
        ('OKX', ccxt.okx),
        ('Huobi', ccxt.huobi)
    ]
    
    symbol = 'CFX/USDT'
    
    for exchange_name, exchange_class in exchanges_to_test:
        print(f"\n测试 {exchange_name}:")
        print("-" * 20)
        
        try:
            exchange = exchange_class({
                'enableRateLimit': True,
                'timeout': 30000,
            })
            
            # 测试市场数据
            markets = exchange.load_markets()
            if symbol in markets:
                ticker = exchange.fetch_ticker(symbol)
                price = ticker['last']
                volume = ticker['baseVolume']
                change = ticker['percentage']
                
                print(f"✓ 价格: ${price:.6f}")
                print(f"✓ 24h成交量: {volume:,.0f} CFX")
                print(f"✓ 24h涨跌: {change:.2f}%")
            else:
                print(f"✗ {symbol} 不支持")
                
        except Exception as e:
            print(f"✗ 连接失败: {str(e)[:50]}...")

def create_price_monitoring_script():
    """创建价格监控脚本"""
    print("\n" + "=" * 60)
    print("创建价格监控脚本")
    print("=" * 60)
    
    script_content = '''#!/usr/bin/env python3
"""
CFX价格实时监控脚本
"""

import ccxt
import time
from datetime import datetime

def monitor_cfx_price():
    """监控CFX价格"""
    print("CFX价格实时监控")
    print("=" * 40)
    
    # 创建交易所实例
    exchanges = {
        'Binance': ccxt.binance({'enableRateLimit': True}),
        'OKX': ccxt.okx({'enableRateLimit': True}),
        'Huobi': ccxt.huobi({'enableRateLimit': True})
    }
    
    symbol = 'CFX/USDT'
    
    try:
        while True:
            print(f"\\n[{datetime.now().strftime('%H:%M:%S')}] CFX价格:")
            
            for name, exchange in exchanges.items():
                try:
                    ticker = exchange.fetch_ticker(symbol)
                    price = ticker['last']
                    change = ticker['percentage']
                    print(f"{name:8}: ${price:.6f} ({change:+.2f}%)")
                except Exception as e:
                    print(f"{name:8}: 获取失败 - {str(e)[:30]}...")
            
            time.sleep(10)  # 每10秒更新一次
            
    except KeyboardInterrupt:
        print("\\n监控停止")

if __name__ == "__main__":
    monitor_cfx_price()
'''
    
    try:
        with open('cfx_price_monitor.py', 'w', encoding='utf-8') as f:
            f.write(script_content)
        print("✓ 已创建价格监控脚本: cfx_price_monitor.py")
        print("  使用方法: python cfx_price_monitor.py")
    except Exception as e:
        print(f"✗ 创建脚本失败: {e}")

def main():
    """主函数"""
    print("量化交易系统价格修复测试")
    print(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. 测试价格获取功能
    print("\n1. 测试价格获取功能...")
    success1 = test_price_retrieval()
    
    # 2. 测试真实交易所连接
    print("\n2. 测试真实交易所连接...")
    test_real_exchange_connection()
    
    # 3. 模拟网格策略测试
    print("\n3. 模拟网格策略测试...")
    success2 = test_grid_strategy_simulation()
    
    # 4. 创建监控脚本
    print("\n4. 创建价格监控脚本...")
    create_price_monitoring_script()
    
    # 总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    if success1 and success2:
        print("✅ 价格修复测试通过")
        print("\n修复内容:")
        print("1. ✓ 改进了价格获取逻辑")
        print("2. ✓ 添加了价格缓存机制")
        print("3. ✓ 增加了实时价格显示")
        print("4. ✓ 优化了检查频率")
        
        print("\n建议:")
        print("1. 重新启动您的量化交易系统")
        print("2. 现在应该能看到真实的CFX价格")
        print("3. 价格每5秒更新一次")
        print("4. 可以使用 cfx_price_monitor.py 独立监控价格")
    else:
        print("❌ 部分测试失败")
        print("请检查网络连接和交易所API")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
