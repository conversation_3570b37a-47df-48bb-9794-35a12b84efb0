#!/usr/bin/env python3
"""
简化的文档质量检查工具
"""

import os
import re
from datetime import datetime

def check_file_documentation(filename):
    """检查单个文件的文档质量"""
    if not os.path.exists(filename):
        return f"❌ {filename} - 文件不存在"
    
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 基本统计
        total_lines = len(content.split('\n'))
        chinese_chars = sum(1 for char in content if '\u4e00' <= char <= '\u9fff')
        total_chars = len(content)
        
        # 检查模块文档字符串
        has_module_doc = content.strip().startswith('"""') or content.strip().startswith("'''")
        
        # 检查类和函数文档
        class_count = len(re.findall(r'class\s+\w+', content))
        function_count = len(re.findall(r'def\s+\w+', content))
        docstring_count = len(re.findall(r'"""[\s\S]*?"""', content))
        
        # 检查注释行
        comment_lines = len([line for line in content.split('\n') if line.strip().startswith('#')])
        
        result = {
            'filename': filename,
            'total_lines': total_lines,
            'chinese_ratio': f"{chinese_chars/total_chars*100:.1f}%" if total_chars > 0 else "0%",
            'has_module_doc': has_module_doc,
            'class_count': class_count,
            'function_count': function_count,
            'docstring_count': docstring_count,
            'comment_lines': comment_lines,
            'comment_ratio': f"{comment_lines/total_lines*100:.1f}%" if total_lines > 0 else "0%"
        }
        
        return result
        
    except Exception as e:
        return f"❌ {filename} - 读取错误: {e}"

def main():
    """主函数"""
    print("🔍 代码注释质量检查")
    print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    target_files = [
        'strategies.py',
        'exchange_manager.py', 
        'environment_manager.py',
        'parameter_validator.py',
        'error_handler.py',
        'user_friendly_messages.py',
        'test_framework.py'
    ]
    
    results = []
    
    for filename in target_files:
        print(f"\n📄 检查文件: {filename}")
        result = check_file_documentation(filename)
        
        if isinstance(result, str):  # 错误信息
            print(f"   {result}")
            continue
        
        results.append(result)
        
        # 显示结果
        print(f"   📝 模块文档: {'✅' if result['has_module_doc'] else '❌'}")
        print(f"   🏗️  类数量: {result['class_count']}")
        print(f"   🔧 函数数量: {result['function_count']}")
        print(f"   📚 文档字符串: {result['docstring_count']}")
        print(f"   🇨🇳 中文比例: {result['chinese_ratio']}")
        print(f"   💬 注释比例: {result['comment_ratio']}")
        print(f"   📏 总行数: {result['total_lines']}")
    
    # 生成总结
    if results:
        print(f"\n{'='*60}")
        print("📊 总结统计")
        print(f"{'='*60}")
        
        total_files = len(results)
        files_with_module_doc = sum(1 for r in results if r['has_module_doc'])
        total_classes = sum(r['class_count'] for r in results)
        total_functions = sum(r['function_count'] for r in results)
        total_docstrings = sum(r['docstring_count'] for r in results)
        
        print(f"检查文件数: {total_files}")
        print(f"模块文档覆盖率: {files_with_module_doc}/{total_files} ({files_with_module_doc/total_files:.1%})")
        print(f"总类数量: {total_classes}")
        print(f"总函数数量: {total_functions}")
        print(f"总文档字符串: {total_docstrings}")
        
        # 计算平均中文比例
        chinese_ratios = [float(r['chinese_ratio'].rstrip('%')) for r in results]
        avg_chinese = sum(chinese_ratios) / len(chinese_ratios) if chinese_ratios else 0
        print(f"平均中文内容比例: {avg_chinese:.1f}%")
        
        # 评估质量
        print(f"\n🎯 质量评估:")
        if files_with_module_doc == total_files:
            print("✅ 所有文件都有模块文档")
        else:
            print(f"⚠️ {total_files - files_with_module_doc} 个文件缺少模块文档")
        
        if avg_chinese >= 15:
            print("✅ 中文注释比例良好")
        elif avg_chinese >= 10:
            print("⚠️ 中文注释比例一般，建议增加")
        else:
            print("❌ 中文注释比例偏低，需要改进")
        
        if total_docstrings >= total_classes + total_functions * 0.8:
            print("✅ 文档字符串覆盖率良好")
        else:
            print("⚠️ 建议为更多类和函数添加文档字符串")
    
    print(f"\n🎉 检查完成！")

if __name__ == "__main__":
    main()
