"""
Gate.io交易所API适配器
处理Gate.io特有的API调用和数据格式
"""

import ccxt
import hashlib
import hmac
import time
import json
from logger import get_logger

class GateAdapter:
    """Gate.io API适配器"""
    
    def __init__(self, api_key, secret_key, sandbox=True):
        self.api_key = api_key
        self.secret_key = secret_key
        self.sandbox = sandbox
        self.logger = get_logger("gate_adapter")
        
        # 初始化CCXT Gate.io实例
        self.exchange = ccxt.gateio({
            'apiKey': api_key,
            'secret': secret_key,
            'sandbox': sandbox,
            'enableRateLimit': True,
            'timeout': 30000,
            'options': {
                'defaultType': 'spot',
                'adjustForTimeDifference': True,
            }
        })
    
    def get_signature(self, method, url, query_string, payload):
        """生成Gate.io API签名"""
        timestamp = str(int(time.time()))
        
        # 构建签名字符串
        if payload:
            body_hash = hashlib.sha512(payload.encode()).hexdigest()
        else:
            body_hash = hashlib.sha512(b'').hexdigest()
        
        sign_string = f"{method}\n{url}\n{query_string}\n{body_hash}\n{timestamp}"
        
        # 生成签名
        signature = hmac.new(
            self.secret_key.encode(),
            sign_string.encode(),
            hashlib.sha512
        ).hexdigest()
        
        return {
            'KEY': self.api_key,
            'Timestamp': timestamp,
            'SIGN': signature
        }
    
    def fetch_balance(self):
        """获取账户余额"""
        try:
            return self.exchange.fetch_balance()
        except Exception as e:
            self.logger.error(f"获取余额失败: {e}")
            raise e
    
    def fetch_ticker(self, symbol):
        """获取行情数据"""
        try:
            return self.exchange.fetch_ticker(symbol)
        except Exception as e:
            self.logger.error(f"获取行情失败: {e}")
            raise e
    
    def fetch_ohlcv(self, symbol, timeframe='1h', limit=100):
        """获取K线数据"""
        try:
            return self.exchange.fetch_ohlcv(symbol, timeframe, limit=limit)
        except Exception as e:
            self.logger.error(f"获取K线数据失败: {e}")
            raise e
    
    def fetch_order_book(self, symbol, limit=20):
        """获取订单簿"""
        try:
            return self.exchange.fetch_order_book(symbol, limit)
        except Exception as e:
            self.logger.error(f"获取订单簿失败: {e}")
            raise e
    
    def create_order(self, symbol, order_type, side, amount, price=None, params=None):
        """创建订单"""
        try:
            if order_type == 'market':
                if side == 'buy':
                    return self.exchange.create_market_buy_order(symbol, amount, None, None, params)
                else:
                    return self.exchange.create_market_sell_order(symbol, amount, None, None, params)
            elif order_type == 'limit':
                if not price:
                    raise ValueError("限价单必须指定价格")
                if side == 'buy':
                    return self.exchange.create_limit_buy_order(symbol, amount, price, None, params)
                else:
                    return self.exchange.create_limit_sell_order(symbol, amount, price, None, params)
            else:
                raise ValueError(f"不支持的订单类型: {order_type}")
        except Exception as e:
            self.logger.error(f"创建订单失败: {e}")
            raise e
    
    def cancel_order(self, order_id, symbol):
        """取消订单"""
        try:
            return self.exchange.cancel_order(order_id, symbol)
        except Exception as e:
            self.logger.error(f"取消订单失败: {e}")
            raise e
    
    def fetch_order(self, order_id, symbol):
        """获取订单信息"""
        try:
            return self.exchange.fetch_order(order_id, symbol)
        except Exception as e:
            self.logger.error(f"获取订单信息失败: {e}")
            raise e
    
    def fetch_open_orders(self, symbol=None):
        """获取挂单"""
        try:
            return self.exchange.fetch_open_orders(symbol)
        except Exception as e:
            self.logger.error(f"获取挂单失败: {e}")
            raise e
    
    def fetch_trades(self, symbol, limit=50):
        """获取交易历史"""
        try:
            return self.exchange.fetch_trades(symbol, limit=limit)
        except Exception as e:
            self.logger.error(f"获取交易历史失败: {e}")
            raise e
    
    def fetch_my_trades(self, symbol=None, limit=50):
        """获取我的交易记录"""
        try:
            return self.exchange.fetch_my_trades(symbol, limit=limit)
        except Exception as e:
            self.logger.error(f"获取交易记录失败: {e}")
            raise e
    
    def load_markets(self):
        """加载市场信息"""
        try:
            return self.exchange.load_markets()
        except Exception as e:
            self.logger.error(f"加载市场信息失败: {e}")
            raise e
    
    def get_trading_fees(self, symbol=None):
        """获取交易费率"""
        try:
            return self.exchange.fetch_trading_fees()
        except Exception as e:
            self.logger.error(f"获取交易费率失败: {e}")
            return {}
    
    def get_deposit_address(self, currency):
        """获取充值地址"""
        try:
            return self.exchange.fetch_deposit_address(currency)
        except Exception as e:
            self.logger.error(f"获取充值地址失败: {e}")
            raise e
    
    def withdraw(self, currency, amount, address, tag=None):
        """提现"""
        try:
            return self.exchange.withdraw(currency, amount, address, tag)
        except Exception as e:
            self.logger.error(f"提现失败: {e}")
            raise e
    
    def fetch_deposit_history(self, currency=None, limit=50):
        """获取充值历史"""
        try:
            return self.exchange.fetch_deposits(currency, limit=limit)
        except Exception as e:
            self.logger.error(f"获取充值历史失败: {e}")
            return []
    
    def fetch_withdrawal_history(self, currency=None, limit=50):
        """获取提现历史"""
        try:
            return self.exchange.fetch_withdrawals(currency, limit=limit)
        except Exception as e:
            self.logger.error(f"获取提现历史失败: {e}")
            return []
    
    def get_server_time(self):
        """获取服务器时间"""
        try:
            # Gate.io没有专门的服务器时间接口，使用本地时间
            return int(time.time() * 1000)
        except Exception as e:
            self.logger.error(f"获取服务器时间失败: {e}")
            return int(time.time() * 1000)
    
    def test_connection(self):
        """测试连接"""
        try:
            # 测试获取余额
            balance = self.fetch_balance()
            
            # 测试获取市场数据
            ticker = self.fetch_ticker('BTC/USDT')
            
            self.logger.info("Gate.io连接测试成功")
            return True
        except Exception as e:
            self.logger.error(f"Gate.io连接测试失败: {e}")
            return False
    
    def get_exchange_info(self):
        """获取交易所信息"""
        try:
            markets = self.load_markets()
            balance = self.fetch_balance()
            
            return {
                'name': 'Gate.io',
                'markets_count': len(markets),
                'balance_currencies': len(balance.get('total', {})),
                'sandbox': self.sandbox,
                'connected': True
            }
        except Exception as e:
            self.logger.error(f"获取交易所信息失败: {e}")
            return {
                'name': 'Gate.io',
                'markets_count': 0,
                'balance_currencies': 0,
                'sandbox': self.sandbox,
                'connected': False
            }
