"""
交易所连接管理模块

本模块负责管理与多个加密货币交易所的连接，提供统一的交易接口，
支持多种主流交易所，并具备完善的错误处理和重连机制。

主要功能：
1. 多交易所支持：Binance、OKX、Huobi、Gate.io 等主流交易所
2. 统一接口：为不同交易所提供一致的调用接口
3. 连接管理：自动重连、连接状态监控、超时处理
4. 错误处理：完善的错误捕获和用户友好的错误提示
5. 环境适配：支持模拟、测试网、实盘等不同环境

设计特点：
- 采用适配器模式，统一不同交易所的API差异
- 支持连接池管理，可同时连接多个交易所
- 内置重试机制，提高连接的稳定性
- 集成环境管理器，自动适配不同交易环境

使用场景：
- 量化交易系统的交易所接入
- 多交易所套利策略
- 交易所API的统一管理
- 跨交易所的资产管理

注意事项：
- 需要在各交易所申请API密钥
- 不同交易所的API限制和规则可能不同
- 实盘交易前请在测试环境充分验证
- 妥善保管API密钥，避免泄露

作者：量化交易系统开发团队
版本：v2.0 (增强版，包含错误处理和中文提示)
"""
import ccxt
import time
from logger import get_logger
import tkinter.messagebox as messagebox
from gate_adapter import GateAdapter
from environment_manager import environment_manager
from error_handler import error_handler, ErrorCode

class ExchangeManager:
    """
    交易所管理器类

    这是量化交易系统的核心组件之一，负责管理与各大加密货币交易所的连接。
    它提供了统一的接口来访问不同的交易所，简化了多交易所交易的复杂性。

    主要职责：
    1. 交易所连接管理：建立、维护、监控与交易所的API连接
    2. 统一接口封装：为不同交易所提供一致的调用方式
    3. 错误处理：处理网络错误、API错误等各种异常情况
    4. 环境适配：根据当前环境（模拟/测试/实盘）自动配置连接参数

    支持的交易所：
    - Binance：全球最大的加密货币交易所
    - OKX：知名的数字资产交易平台
    - Huobi：老牌的数字货币交易所
    - Gate.io：支持多种数字资产的交易平台

    使用示例：
    ```python
    # 创建交易所管理器
    manager = ExchangeManager()

    # 连接到交易所
    success = manager.connect_exchange('binance', api_key, secret)

    # 获取当前交易所
    exchange = manager.get_current_exchange()

    # 执行交易操作
    ticker = exchange.fetch_ticker('BTC/USDT')
    ```
    """

    def __init__(self):
        """
        初始化交易所管理器

        设置基本属性和支持的交易所配置信息。
        每个交易所都有详细的配置，包括API类、名称、测试交易对等。
        """
        # 存储已连接的交易所实例 {交易所名称: 交易所对象}
        self.exchanges = {}

        # 当前活跃的交易所对象
        self.current_exchange = None

        # 日志记录器，用于记录连接状态和错误信息
        self.logger = get_logger("exchange_manager")

        # 支持的交易所配置信息
        self.supported_exchanges = {
            'binance': {
                'class': ccxt.binance,
                'name': 'Binance',
                'test_symbol': 'BTC/USDT',
                'features': ['spot', 'margin', 'future', 'option'],
                'auth_method': 'hmac_sha256'
            },
            'okx': {
                'class': ccxt.okx,
                'name': 'OKX',
                'test_symbol': 'BTC/USDT',
                'features': ['spot', 'margin', 'future', 'option'],
                'auth_method': 'hmac_sha256'
            },
            'huobi': {
                'class': ccxt.huobi,
                'name': 'Huobi',
                'test_symbol': 'BTC/USDT',
                'features': ['spot', 'margin', 'future'],
                'auth_method': 'hmac_sha256'
            },
            'gate': {
                'class': ccxt.gateio,
                'name': 'Gate.io',
                'test_symbol': 'BTC/USDT',
                'features': ['spot', 'margin', 'future', 'option'],
                'auth_method': 'hmac_sha512',
                'special_config': {
                    'spot_api': 'https://api.gateio.ws/api/v4',
                    'futures_api': 'https://api.gateio.ws/api/v4/futures',
                    'delivery_api': 'https://api.gateio.ws/api/v4/delivery',
                    'options_api': 'https://api.gateio.ws/api/v4/options',
                    'websocket_spot': 'wss://api.gateio.ws/ws/v4/',
                    'websocket_futures': 'wss://fx-ws.gateio.ws/v4/ws/usdt',
                    'websocket_delivery': 'wss://fx-ws.gateio.ws/v4/ws/delivery/usdt'
                }
            }
        }
    
    def connect_exchange(self, exchange_name, api_key, secret, passphrase="", sandbox=None, max_retries=3):
        """连接交易所 - 带重试机制"""
        # 如果未指定sandbox，则根据环境管理器决定
        if sandbox is None:
            sandbox = environment_manager.should_use_sandbox()

        return self._connect_with_retry(exchange_name, api_key, secret, passphrase, sandbox, max_retries)

    def _connect_with_retry(self, exchange_name, api_key, secret, passphrase="", sandbox=True, max_retries=3):
        """带重试机制的连接方法"""
        last_error = None

        for attempt in range(max_retries):
            try:
                self.logger.info(f"尝试连接 {exchange_name} (第 {attempt + 1}/{max_retries} 次)")

                if exchange_name not in self.supported_exchanges:
                    raise ValueError(f"不支持的交易所: {exchange_name}")

                exchange_class = self.supported_exchanges[exchange_name]['class']

                # 使用环境管理器获取API配置
                config = environment_manager.get_api_config(exchange_name, api_key, secret, passphrase)
                config['timeout'] = 30000 + (attempt * 10000)  # 每次重试增加超时时间

                # Gate.io特殊配置
                if exchange_name == 'gate':
                    # 使用专门的Gate.io适配器
                    exchange = GateAdapter(api_key, secret, config['sandbox'])
                    # 测试连接
                    if self._test_connection_with_retry(exchange, exchange_name, attempt):
                        self.exchanges[exchange_name] = exchange
                        self.current_exchange = exchange
                        self.logger.info(f"成功连接到 Gate.io (第 {attempt + 1} 次尝试)")
                        return True
                    else:
                        raise Exception("Gate.io 连接测试失败")
                else:
                    # 创建其他交易所实例
                    exchange = exchange_class(config)

                # 测试连接
                if self._test_connection_with_retry(exchange, exchange_name, attempt):
                    # 保存连接
                    self.exchanges[exchange_name] = exchange
                    self.current_exchange = exchange

                    self.logger.info(f"成功连接到 {self.supported_exchanges[exchange_name]['name']} (第 {attempt + 1} 次尝试)")
                    return True
                else:
                    raise Exception("连接测试失败")

            except Exception as e:
                last_error = e

                # 使用错误处理中心处理错误
                error_details = error_handler.handle_error(e, context={
                    "exchange": exchange_name,
                    "attempt": attempt + 1,
                    "max_retries": max_retries
                })

                self.logger.warning(f"第 {attempt + 1} 次连接 {exchange_name} 失败: {error_details['message']}")

                if attempt < max_retries - 1:
                    # 等待后重试，每次等待时间递增
                    wait_time = (attempt + 1) * 2
                    self.logger.info(f"等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                else:
                    # 最后一次尝试失败，使用错误处理中心
                    error_details = error_handler.handle_error(last_error, ErrorCode.NETWORK_CONNECTION_FAILED, context={
                        "exchange": exchange_name,
                        "max_retries": max_retries
                    })

                    error_msg = f"连接 {exchange_name} 失败 (已重试 {max_retries} 次)"
                    self.logger.error(error_msg)

                    # 显示用户友好的错误消息
                    user_message = error_handler.format_user_message(error_details)
                    messagebox.showerror("连接失败", user_message)
                    return False

        return False
    
    def _test_connection_with_retry(self, exchange, exchange_name, attempt=0):
        """带重试机制的连接测试"""
        try:
            self.logger.info(f"测试 {exchange_name} 连接...")

            # 测试获取市场数据 (比获取余额更轻量)
            test_symbol = self.supported_exchanges[exchange_name]['test_symbol']
            ticker = exchange.fetch_ticker(test_symbol)

            if ticker and 'last' in ticker:
                self.logger.info(f"{exchange_name} 连接测试成功，{test_symbol} 价格: {ticker['last']}")
                return True
            else:
                raise Exception("获取的市场数据无效")

        except Exception as e:
            self.logger.warning(f"{exchange_name} 连接测试失败: {str(e)}")
            return False

    def test_connection(self, exchange, exchange_name):
        """测试交易所连接 (保持向后兼容)"""
        try:
            # 测试获取余额
            balance = exchange.fetch_balance()

            # 测试获取市场数据
            test_symbol = self.supported_exchanges[exchange_name]['test_symbol']
            ticker = exchange.fetch_ticker(test_symbol)
            
            self.logger.info(f"{exchange_name} 连接测试成功")
            
        except Exception as e:
            raise Exception(f"连接测试失败: {str(e)}")
    
    def get_exchange(self, exchange_name=None):
        """获取交易所实例"""
        if exchange_name:
            return self.exchanges.get(exchange_name)
        return self.current_exchange
    
    def switch_exchange(self, exchange_name):
        """切换当前交易所"""
        if exchange_name in self.exchanges:
            self.current_exchange = self.exchanges[exchange_name]
            self.logger.info(f"切换到 {self.supported_exchanges[exchange_name]['name']}")
            return True
        return False
    
    def get_balance(self, exchange_name=None):
        """获取账户余额"""
        try:
            exchange = self.get_exchange(exchange_name)
            if not exchange:
                return {}

            # 检查是否是Gate.io适配器
            if hasattr(exchange, 'fetch_balance'):
                balance = exchange.fetch_balance()
            else:
                balance = exchange.fetch_balance()
            return balance
        except Exception as e:
            self.logger.error(f"获取余额失败: {e}")
            return {}
    
    def get_ticker(self, symbol, exchange_name=None):
        """获取行情数据"""
        try:
            exchange = self.get_exchange(exchange_name)
            if not exchange:
                return {}
            
            ticker = exchange.fetch_ticker(symbol)
            return ticker
        except Exception as e:
            self.logger.error(f"获取行情失败: {e}")
            return {}
    
    def get_ohlcv(self, symbol, timeframe='1h', limit=100, exchange_name=None):
        """获取K线数据"""
        try:
            exchange = self.get_exchange(exchange_name)
            if not exchange:
                return []
            
            ohlcv = exchange.fetch_ohlcv(symbol, timeframe, limit=limit)
            return ohlcv
        except Exception as e:
            self.logger.error(f"获取K线数据失败: {e}")
            return []
    
    def create_order(self, symbol, order_type, side, amount, price=None, exchange_name=None):
        """创建订单"""
        try:
            exchange = self.get_exchange(exchange_name)
            if not exchange:
                raise Exception("未连接交易所")

            # 统一的订单创建接口
            order = exchange.create_order(symbol, order_type, side, amount, price)

            self.logger.info(f"订单创建成功: {order.get('id', 'unknown')}")
            return order

        except Exception as e:
            self.logger.error(f"创建订单失败: {e}")
            raise e
    
    def cancel_order(self, order_id, symbol, exchange_name=None):
        """取消订单"""
        try:
            exchange = self.get_exchange(exchange_name)
            if not exchange:
                raise Exception("未连接交易所")
            
            result = exchange.cancel_order(order_id, symbol)
            self.logger.info(f"订单取消成功: {order_id}")
            return result
            
        except Exception as e:
            self.logger.error(f"取消订单失败: {e}")
            raise e
    
    def get_open_orders(self, symbol=None, exchange_name=None):
        """获取挂单"""
        try:
            exchange = self.get_exchange(exchange_name)
            if not exchange:
                return []
            
            orders = exchange.fetch_open_orders(symbol)
            return orders
        except Exception as e:
            self.logger.error(f"获取挂单失败: {e}")
            return []
    
    def get_order_status(self, order_id, symbol, exchange_name=None):
        """获取订单状态"""
        try:
            exchange = self.get_exchange(exchange_name)
            if not exchange:
                return {}
            
            order = exchange.fetch_order(order_id, symbol)
            return order
        except Exception as e:
            self.logger.error(f"获取订单状态失败: {e}")
            return {}
    
    def get_supported_symbols(self, exchange_name=None):
        """获取支持的交易对"""
        try:
            exchange = self.get_exchange(exchange_name)
            if not exchange:
                return []
            
            markets = exchange.load_markets()
            symbols = list(markets.keys())
            
            # 过滤常用的USDT交易对
            usdt_symbols = [s for s in symbols if s.endswith('/USDT')]
            return sorted(usdt_symbols)
            
        except Exception as e:
            self.logger.error(f"获取交易对失败: {e}")
            return []
    
    def get_exchange_info(self, exchange_name=None):
        """获取交易所信息"""
        exchange = self.get_exchange(exchange_name)
        if not exchange:
            return {}
        
        current_name = None
        for name, config in self.supported_exchanges.items():
            if isinstance(exchange, config['class']):
                current_name = name
                break
        
        if not current_name:
            return {}
        
        try:
            balance = self.get_balance(exchange_name)
            total_usdt = balance.get('total', {}).get('USDT', 0)
            
            return {
                'name': self.supported_exchanges[current_name]['name'],
                'id': current_name,
                'connected': True,
                'balance_usdt': total_usdt,
                'sandbox': exchange.sandbox if hasattr(exchange, 'sandbox') else False
            }
        except:
            return {
                'name': self.supported_exchanges[current_name]['name'],
                'id': current_name,
                'connected': True,
                'balance_usdt': 0,
                'sandbox': True
            }
    
    def disconnect_exchange(self, exchange_name):
        """断开交易所连接"""
        if exchange_name in self.exchanges:
            del self.exchanges[exchange_name]
            if self.current_exchange and isinstance(self.current_exchange, self.supported_exchanges[exchange_name]['class']):
                self.current_exchange = None
            self.logger.info(f"断开 {self.supported_exchanges[exchange_name]['name']} 连接")
    
    def disconnect_all(self):
        """断开所有连接"""
        self.exchanges.clear()
        self.current_exchange = None
        self.logger.info("断开所有交易所连接")

    def reconnect_exchange(self, exchange_name, max_retries=3):
        """重新连接指定交易所"""
        if exchange_name not in self.exchanges:
            self.logger.error(f"交易所 {exchange_name} 未连接，无法重连")
            return False

        # 获取原有配置信息
        old_exchange = self.exchanges[exchange_name]

        # 尝试从原有配置中提取信息
        try:
            api_key = getattr(old_exchange, 'apiKey', '')
            secret = getattr(old_exchange, 'secret', '')
            passphrase = getattr(old_exchange, 'password', '')
            sandbox = getattr(old_exchange, 'sandbox', True)

            self.logger.info(f"开始重连 {exchange_name}...")

            # 先断开旧连接
            self.disconnect_exchange(exchange_name)

            # 重新连接
            return self._connect_with_retry(exchange_name, api_key, secret, passphrase, sandbox, max_retries)

        except Exception as e:
            self.logger.error(f"重连 {exchange_name} 失败: {str(e)}")
            return False

    def check_and_reconnect_all(self, max_retries=2):
        """检查所有连接并重连失效的连接"""
        reconnected = []
        failed = []

        for exchange_name in list(self.exchanges.keys()):
            try:
                exchange = self.exchanges[exchange_name]

                # 简单的连接测试
                test_symbol = self.supported_exchanges[exchange_name]['test_symbol']
                ticker = exchange.fetch_ticker(test_symbol)

                if ticker and 'last' in ticker:
                    self.logger.info(f"{exchange_name} 连接正常")
                else:
                    raise Exception("连接测试失败")

            except Exception as e:
                self.logger.warning(f"{exchange_name} 连接异常: {str(e)}，尝试重连...")

                if self.reconnect_exchange(exchange_name, max_retries):
                    reconnected.append(exchange_name)
                    self.logger.info(f"{exchange_name} 重连成功")
                else:
                    failed.append(exchange_name)
                    self.logger.error(f"{exchange_name} 重连失败")

        return {
            'reconnected': reconnected,
            'failed': failed
        }

    def switch_environment_mode(self, new_mode):
        """切换环境模式并重连所有交易所"""
        self.logger.info(f"切换环境模式到: {new_mode}")

        if not self.exchanges:
            self.logger.info("当前无活跃连接，无需重连")
            return {'success': True, 'reconnected': [], 'failed': []}

        # 保存当前连接信息
        connection_info = {}
        for exchange_name, exchange in self.exchanges.items():
            try:
                connection_info[exchange_name] = {
                    'api_key': getattr(exchange, 'apiKey', ''),
                    'secret': getattr(exchange, 'secret', ''),
                    'passphrase': getattr(exchange, 'password', ''),
                }
            except Exception as e:
                self.logger.error(f"保存 {exchange_name} 连接信息失败: {e}")

        # 断开所有连接
        self.disconnect_all()

        # 使用新环境模式重新连接
        reconnected = []
        failed = []

        for exchange_name, info in connection_info.items():
            try:
                self.logger.info(f"使用新环境模式重连 {exchange_name}...")

                success = self.connect_exchange(
                    exchange_name=exchange_name,
                    api_key=info['api_key'],
                    secret=info['secret'],
                    passphrase=info['passphrase'],
                    max_retries=2
                )

                if success:
                    reconnected.append(exchange_name)
                    self.logger.info(f"{exchange_name} 重连成功")
                else:
                    failed.append(exchange_name)
                    self.logger.error(f"{exchange_name} 重连失败")

            except Exception as e:
                failed.append(exchange_name)
                self.logger.error(f"{exchange_name} 重连异常: {e}")

        result = {
            'success': len(failed) == 0,
            'reconnected': reconnected,
            'failed': failed
        }

        self.logger.info(f"环境切换完成: {result}")
        return result

# 全局交易所管理器实例
exchange_manager = ExchangeManager()
