"""
交易所连接管理模块
处理多个交易所的API连接和统一接口
"""
import ccxt
import time
from logger import get_logger
import tkinter.messagebox as messagebox

class ExchangeManager:
    def __init__(self):
        self.exchanges = {}
        self.current_exchange = None
        self.logger = get_logger("exchange_manager")
        
        # 支持的交易所配置
        self.supported_exchanges = {
            'binance': {
                'class': ccxt.binance,
                'name': 'Binance',
                'test_symbol': 'BTC/USDT'
            },
            'okx': {
                'class': ccxt.okx,
                'name': 'OKX',
                'test_symbol': 'BTC/USDT'
            },
            'huobi': {
                'class': ccxt.huobi,
                'name': '<PERSON><PERSON><PERSON>',
                'test_symbol': 'BTC/USDT'
            }
        }
    
    def connect_exchange(self, exchange_name, api_key, secret, passphrase="", sandbox=True):
        """连接交易所"""
        try:
            if exchange_name not in self.supported_exchanges:
                raise ValueError(f"不支持的交易所: {exchange_name}")
            
            exchange_class = self.supported_exchanges[exchange_name]['class']
            
            # 配置参数
            config = {
                'apiKey': api_key,
                'secret': secret,
                'sandbox': sandbox,
                'enableRateLimit': True,
                'timeout': 30000,
            }
            
            # OKX需要passphrase
            if exchange_name == 'okx' and passphrase:
                config['password'] = passphrase
            
            # 创建交易所实例
            exchange = exchange_class(config)
            
            # 测试连接
            self.test_connection(exchange, exchange_name)
            
            # 保存连接
            self.exchanges[exchange_name] = exchange
            self.current_exchange = exchange
            
            self.logger.info(f"成功连接到 {self.supported_exchanges[exchange_name]['name']}")
            return True
            
        except Exception as e:
            error_msg = f"连接 {exchange_name} 失败: {str(e)}"
            self.logger.error(error_msg)
            messagebox.showerror("连接错误", error_msg)
            return False
    
    def test_connection(self, exchange, exchange_name):
        """测试交易所连接"""
        try:
            # 测试获取余额
            balance = exchange.fetch_balance()
            
            # 测试获取市场数据
            test_symbol = self.supported_exchanges[exchange_name]['test_symbol']
            ticker = exchange.fetch_ticker(test_symbol)
            
            self.logger.info(f"{exchange_name} 连接测试成功")
            
        except Exception as e:
            raise Exception(f"连接测试失败: {str(e)}")
    
    def get_exchange(self, exchange_name=None):
        """获取交易所实例"""
        if exchange_name:
            return self.exchanges.get(exchange_name)
        return self.current_exchange
    
    def switch_exchange(self, exchange_name):
        """切换当前交易所"""
        if exchange_name in self.exchanges:
            self.current_exchange = self.exchanges[exchange_name]
            self.logger.info(f"切换到 {self.supported_exchanges[exchange_name]['name']}")
            return True
        return False
    
    def get_balance(self, exchange_name=None):
        """获取账户余额"""
        try:
            exchange = self.get_exchange(exchange_name)
            if not exchange:
                return {}
            
            balance = exchange.fetch_balance()
            return balance
        except Exception as e:
            self.logger.error(f"获取余额失败: {e}")
            return {}
    
    def get_ticker(self, symbol, exchange_name=None):
        """获取行情数据"""
        try:
            exchange = self.get_exchange(exchange_name)
            if not exchange:
                return {}
            
            ticker = exchange.fetch_ticker(symbol)
            return ticker
        except Exception as e:
            self.logger.error(f"获取行情失败: {e}")
            return {}
    
    def get_ohlcv(self, symbol, timeframe='1h', limit=100, exchange_name=None):
        """获取K线数据"""
        try:
            exchange = self.get_exchange(exchange_name)
            if not exchange:
                return []
            
            ohlcv = exchange.fetch_ohlcv(symbol, timeframe, limit=limit)
            return ohlcv
        except Exception as e:
            self.logger.error(f"获取K线数据失败: {e}")
            return []
    
    def create_order(self, symbol, order_type, side, amount, price=None, exchange_name=None):
        """创建订单"""
        try:
            exchange = self.get_exchange(exchange_name)
            if not exchange:
                raise Exception("未连接交易所")
            
            if order_type == 'market':
                if side == 'buy':
                    order = exchange.create_market_buy_order(symbol, amount)
                else:
                    order = exchange.create_market_sell_order(symbol, amount)
            elif order_type == 'limit':
                if not price:
                    raise Exception("限价单必须指定价格")
                if side == 'buy':
                    order = exchange.create_limit_buy_order(symbol, amount, price)
                else:
                    order = exchange.create_limit_sell_order(symbol, amount, price)
            else:
                raise Exception(f"不支持的订单类型: {order_type}")
            
            self.logger.info(f"订单创建成功: {order['id']}")
            return order
            
        except Exception as e:
            self.logger.error(f"创建订单失败: {e}")
            raise e
    
    def cancel_order(self, order_id, symbol, exchange_name=None):
        """取消订单"""
        try:
            exchange = self.get_exchange(exchange_name)
            if not exchange:
                raise Exception("未连接交易所")
            
            result = exchange.cancel_order(order_id, symbol)
            self.logger.info(f"订单取消成功: {order_id}")
            return result
            
        except Exception as e:
            self.logger.error(f"取消订单失败: {e}")
            raise e
    
    def get_open_orders(self, symbol=None, exchange_name=None):
        """获取挂单"""
        try:
            exchange = self.get_exchange(exchange_name)
            if not exchange:
                return []
            
            orders = exchange.fetch_open_orders(symbol)
            return orders
        except Exception as e:
            self.logger.error(f"获取挂单失败: {e}")
            return []
    
    def get_order_status(self, order_id, symbol, exchange_name=None):
        """获取订单状态"""
        try:
            exchange = self.get_exchange(exchange_name)
            if not exchange:
                return {}
            
            order = exchange.fetch_order(order_id, symbol)
            return order
        except Exception as e:
            self.logger.error(f"获取订单状态失败: {e}")
            return {}
    
    def get_supported_symbols(self, exchange_name=None):
        """获取支持的交易对"""
        try:
            exchange = self.get_exchange(exchange_name)
            if not exchange:
                return []
            
            markets = exchange.load_markets()
            symbols = list(markets.keys())
            
            # 过滤常用的USDT交易对
            usdt_symbols = [s for s in symbols if s.endswith('/USDT')]
            return sorted(usdt_symbols)
            
        except Exception as e:
            self.logger.error(f"获取交易对失败: {e}")
            return []
    
    def get_exchange_info(self, exchange_name=None):
        """获取交易所信息"""
        exchange = self.get_exchange(exchange_name)
        if not exchange:
            return {}
        
        current_name = None
        for name, config in self.supported_exchanges.items():
            if isinstance(exchange, config['class']):
                current_name = name
                break
        
        if not current_name:
            return {}
        
        try:
            balance = self.get_balance(exchange_name)
            total_usdt = balance.get('total', {}).get('USDT', 0)
            
            return {
                'name': self.supported_exchanges[current_name]['name'],
                'id': current_name,
                'connected': True,
                'balance_usdt': total_usdt,
                'sandbox': exchange.sandbox if hasattr(exchange, 'sandbox') else False
            }
        except:
            return {
                'name': self.supported_exchanges[current_name]['name'],
                'id': current_name,
                'connected': True,
                'balance_usdt': 0,
                'sandbox': True
            }
    
    def disconnect_exchange(self, exchange_name):
        """断开交易所连接"""
        if exchange_name in self.exchanges:
            del self.exchanges[exchange_name]
            if self.current_exchange and isinstance(self.current_exchange, self.supported_exchanges[exchange_name]['class']):
                self.current_exchange = None
            self.logger.info(f"断开 {self.supported_exchanges[exchange_name]['name']} 连接")
    
    def disconnect_all(self):
        """断开所有连接"""
        self.exchanges.clear()
        self.current_exchange = None
        self.logger.info("断开所有交易所连接")

# 全局交易所管理器实例
exchange_manager = ExchangeManager()
