"""
主GUI界面
量化交易系统的主窗口和界面管理
"""
import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import threading
import time
from datetime import datetime
import json

from config import ConfigManager, DEFAULT_CONFIG
from exchange_manager import exchange_manager
from risk_manager import RiskManager
from logger import get_logger
from strategy_tabs import *
from system_log_tab import SystemLogTab
from environment_control_panel import EnvironmentControlPanel, EnvironmentStatusBar
from environment_manager import environment_manager

class TradingSystemGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("量化交易系统 v1.0")
        self.root.geometry("1200x800")
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # 初始化组件
        self.config_manager = ConfigManager()
        self.risk_manager = None
        self.logger = get_logger("main_gui")
        
        # 策略实例
        self.strategies = {}
        self.strategy_threads = {}
        
        # 创建界面
        self.create_menu()
        self.create_environment_panel()
        self.create_main_interface()
        self.create_status_bar()
        
        # 启动状态更新
        self.start_status_update()
        
        self.logger.info("量化交易系统GUI启动")
    
    def create_menu(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="连接交易所", command=self.show_exchange_dialog)
        file_menu.add_command(label="设置密码", command=self.set_password)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.on_closing)
        
        # 策略菜单
        strategy_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="策略", menu=strategy_menu)
        strategy_menu.add_command(label="停止所有策略", command=self.stop_all_strategies)
        strategy_menu.add_command(label="紧急平仓", command=self.emergency_close_all)
        
        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="关于", command=self.show_about)

    def create_environment_panel(self):
        """创建环境控制面板"""
        self.env_panel = EnvironmentControlPanel(self.root, self)

        # 注册环境变化回调
        environment_manager.add_mode_change_callback(self.on_environment_changed)
    
    def create_main_interface(self):
        """创建主界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建标签页控件
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 创建各个策略标签页
        self.create_strategy_tabs()

        # 创建系统日志标签页
        self.create_system_log_tab()

        # 创建风险管理标签页
        self.create_risk_management_tab()
    
    def create_strategy_tabs(self):
        """创建策略标签页"""
        # 网格交易策略
        self.grid_tab = GridTradingTab(self.notebook, self)
        self.notebook.add(self.grid_tab.frame, text="网格交易")
        
        # 移动平均线策略
        self.ma_tab = MovingAverageTab(self.notebook, self)
        self.notebook.add(self.ma_tab.frame, text="移动平均线")
        
        # RSI反转策略
        self.rsi_tab = RSIStrategyTab(self.notebook, self)
        self.notebook.add(self.rsi_tab.frame, text="RSI反转")
        
        # 成交量突破策略
        self.volume_tab = VolumeBreakoutTab(self.notebook, self)
        self.notebook.add(self.volume_tab.frame, text="成交量突破")
        
        # 智能网格策略
        self.smart_grid_tab = SmartGridTab(self.notebook, self)
        self.notebook.add(self.smart_grid_tab.frame, text="智能网格")

    def create_system_log_tab(self):
        """创建系统日志标签页"""
        self.log_tab = SystemLogTab(self.notebook, self)
        self.notebook.add(self.log_tab.frame, text="系统日志")
    
    def create_risk_management_tab(self):
        """创建风险管理标签页"""
        self.risk_tab = RiskManagementTab(self.notebook, self)
        self.notebook.add(self.risk_tab.frame, text="风险管理与监控")
    
    def create_status_bar(self):
        """创建状态栏"""
        self.status_frame = ttk.Frame(self.root)
        self.status_frame.pack(fill=tk.X, side=tk.BOTTOM)

        # 环境状态
        self.env_status = EnvironmentStatusBar(self.status_frame)

        # 连接状态
        self.connection_label = ttk.Label(self.status_frame, text="未连接交易所", foreground="red")
        self.connection_label.pack(side=tk.LEFT, padx=5)

        # 时间显示
        self.time_label = ttk.Label(self.status_frame, text="")
        self.time_label.pack(side=tk.RIGHT, padx=5)
    
    def show_exchange_dialog(self):
        """显示交易所连接对话框"""
        dialog = ExchangeConnectionDialog(self.root, self.config_manager)
        if dialog.result:
            exchange_name, api_key, secret, passphrase, sandbox = dialog.result
            
            # 连接交易所
            success = exchange_manager.connect_exchange(
                exchange_name, api_key, secret, passphrase, sandbox
            )
            
            if success:
                # 保存API凭证
                self.config_manager.save_api_credentials(exchange_name, api_key, secret, passphrase)
                
                # 初始化风险管理器
                try:
                    balance = exchange_manager.get_balance()
                    initial_capital = balance.get('total', {}).get('USDT', 10000)
                    self.risk_manager = RiskManager(exchange_manager.get_exchange(), initial_capital)
                except:
                    self.risk_manager = RiskManager(exchange_manager.get_exchange(), 10000)
                
                # 更新所有标签页的交易所信息
                self.update_all_tabs()
                
                messagebox.showinfo("成功", f"成功连接到 {exchange_name}")
            else:
                messagebox.showerror("错误", f"连接 {exchange_name} 失败")
    
    def set_password(self):
        """设置加密密码"""
        password = simpledialog.askstring("设置密码", "请输入加密密码:", show='*')
        if password:
            self.config_manager.set_password(password)
            messagebox.showinfo("成功", "密码设置成功")
    
    def update_all_tabs(self):
        """更新所有标签页"""
        tabs = [self.grid_tab, self.ma_tab, self.rsi_tab, self.volume_tab, self.smart_grid_tab, self.risk_tab]
        for tab in tabs:
            if hasattr(tab, 'update_exchange_info'):
                tab.update_exchange_info()
    
    def stop_all_strategies(self):
        """停止所有策略"""
        if messagebox.askyesno("确认", "确定要停止所有运行中的策略吗?"):
            for strategy_name, strategy in self.strategies.items():
                if hasattr(strategy, 'stop'):
                    strategy.stop()
            
            # 等待线程结束
            for thread in self.strategy_threads.values():
                if thread.is_alive():
                    thread.join(timeout=5)
            
            self.strategies.clear()
            self.strategy_threads.clear()
            
            messagebox.showinfo("完成", "所有策略已停止")
    
    def emergency_close_all(self):
        """紧急平仓"""
        if messagebox.askyesno("紧急平仓", "确定要执行紧急平仓操作吗?\n这将取消所有挂单并卖出所有持仓!"):
            try:
                exchange = exchange_manager.get_exchange()
                if not exchange:
                    messagebox.showerror("错误", "未连接交易所")
                    return
                
                # 取消所有挂单
                open_orders = exchange.fetch_open_orders()
                for order in open_orders:
                    try:
                        exchange.cancel_order(order['id'], order['symbol'])
                    except:
                        pass
                
                # 卖出所有持仓
                balance = exchange.fetch_balance()
                for currency, amount in balance['free'].items():
                    if currency != 'USDT' and amount > 0.001:
                        try:
                            symbol = f"{currency}/USDT"
                            exchange.create_market_sell_order(symbol, amount)
                        except:
                            pass
                
                messagebox.showinfo("完成", "紧急平仓操作完成")
                
            except Exception as e:
                messagebox.showerror("错误", f"紧急平仓失败: {str(e)}")
    
    def start_status_update(self):
        """启动状态更新"""
        def update_status():
            while True:
                try:
                    # 更新时间
                    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    self.time_label.config(text=current_time)
                    
                    # 更新连接状态
                    if exchange_manager.current_exchange:
                        exchange_info = exchange_manager.get_exchange_info()
                        status_text = f"已连接: {exchange_info.get('name', 'Unknown')}"
                        if exchange_info.get('sandbox', False):
                            status_text += " (测试环境)"
                        self.connection_label.config(text=status_text, foreground="green")
                    else:
                        self.connection_label.config(text="未连接交易所", foreground="red")
                    
                    time.sleep(1)
                except:
                    break
        
        status_thread = threading.Thread(target=update_status)
        status_thread.daemon = True
        status_thread.start()
    
    def show_about(self):
        """显示关于对话框"""
        about_text = """
量化交易系统 v1.0

基于4.txt文件内容开发的完整GUI量化交易系统

支持策略:
• 网格交易策略
• 移动平均线策略  
• RSI反转策略
• 成交量突破策略
• 智能网格策略

支持交易所:
• Binance
• OKX  
• Huobi

开发者: AI Assistant
        """
        messagebox.showinfo("关于", about_text)
    
    def on_closing(self):
        """关闭程序"""
        if messagebox.askyesno("退出", "确定要退出量化交易系统吗?"):
            # 停止所有策略
            for strategy in self.strategies.values():
                if hasattr(strategy, 'stop'):
                    strategy.stop()
            
            # 停止风险管理
            if self.risk_manager:
                self.risk_manager.stop_monitoring()
            
            # 断开交易所连接
            exchange_manager.disconnect_all()

            # 清理日志标签页
            if hasattr(self, 'log_tab'):
                self.log_tab.cleanup()

            self.root.destroy()

    def on_environment_changed(self, old_mode, new_mode):
        """环境模式变化处理"""
        self.logger.info(f"环境模式变化: {old_mode} -> {new_mode}")

        try:
            # 切换交易所连接环境
            result = exchange_manager.switch_environment_mode(new_mode)

            if result['success']:
                self.logger.info(f"环境切换成功: {result}")

                # 显示成功消息
                if result['reconnected']:
                    messagebox.showinfo(
                        "环境切换成功",
                        f"已成功切换到 {environment_manager.get_mode_info(new_mode)['name']}\n"
                        f"重连交易所: {', '.join(result['reconnected'])}"
                    )
            else:
                self.logger.error(f"环境切换部分失败: {result}")

                # 显示警告消息
                warning_msg = f"环境切换到 {environment_manager.get_mode_info(new_mode)['name']} 完成\n"
                if result['reconnected']:
                    warning_msg += f"成功重连: {', '.join(result['reconnected'])}\n"
                if result['failed']:
                    warning_msg += f"重连失败: {', '.join(result['failed'])}"

                messagebox.showwarning("环境切换警告", warning_msg)

            # 更新界面状态
            self.update_interface_for_environment(new_mode)

        except Exception as e:
            self.logger.error(f"环境切换处理失败: {e}")
            messagebox.showerror("环境切换错误", f"环境切换处理失败: {str(e)}")

    def update_interface_for_environment(self, mode):
        """根据环境模式更新界面"""
        mode_info = environment_manager.get_mode_info(mode)

        # 更新窗口标题
        base_title = "量化交易系统 v1.0"
        if mode == environment_manager.MODE_LIVE:
            self.root.title(f"{base_title} - 🔴 实盘模式")
        elif mode == environment_manager.MODE_TESTNET:
            self.root.title(f"{base_title} - 🟠 测试网模式")
        else:
            self.root.title(f"{base_title} - 🟢 模拟模式")

        # 记录界面更新
        self.logger.info(f"界面已更新为 {mode_info['name']} 模式")
    
    def run(self):
        """运行GUI"""
        self.root.mainloop()


class ExchangeConnectionDialog:
    """交易所连接对话框"""
    def __init__(self, parent, config_manager):
        self.result = None
        self.config_manager = config_manager
        
        # 创建对话框窗口
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("连接交易所")
        self.dialog.geometry("400x300")
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        self.create_widgets()
        
        # 居中显示
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (self.dialog.winfo_width() // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (self.dialog.winfo_height() // 2)
        self.dialog.geometry(f"+{x}+{y}")
        
        self.dialog.wait_window()
    
    def create_widgets(self):
        """创建对话框控件"""
        # 主框架
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 交易所选择
        ttk.Label(main_frame, text="选择交易所:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.exchange_var = tk.StringVar(value="binance")
        exchange_combo = ttk.Combobox(main_frame, textvariable=self.exchange_var,
                                     values=["binance", "okx", "huobi", "gate"], state="readonly")
        exchange_combo.grid(row=0, column=1, sticky=tk.EW, pady=5)
        
        # API Key
        ttk.Label(main_frame, text="API Key:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.api_key_var = tk.StringVar()
        ttk.Entry(main_frame, textvariable=self.api_key_var, show="*").grid(row=1, column=1, sticky=tk.EW, pady=5)
        
        # Secret
        ttk.Label(main_frame, text="Secret:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.secret_var = tk.StringVar()
        ttk.Entry(main_frame, textvariable=self.secret_var, show="*").grid(row=2, column=1, sticky=tk.EW, pady=5)
        
        # Passphrase (OKX需要)
        ttk.Label(main_frame, text="Passphrase:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.passphrase_var = tk.StringVar()
        ttk.Entry(main_frame, textvariable=self.passphrase_var, show="*").grid(row=3, column=1, sticky=tk.EW, pady=5)
        
        # 测试环境选项
        self.sandbox_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(main_frame, text="使用测试环境", variable=self.sandbox_var).grid(row=4, column=0, columnspan=2, pady=10)
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=5, column=0, columnspan=2, pady=10)
        
        ttk.Button(button_frame, text="连接", command=self.connect).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="取消", command=self.cancel).pack(side=tk.LEFT, padx=5)
        
        # 配置列权重
        main_frame.columnconfigure(1, weight=1)
    
    def connect(self):
        """连接按钮回调"""
        exchange_name = self.exchange_var.get()
        api_key = self.api_key_var.get().strip()
        secret = self.secret_var.get().strip()
        passphrase = self.passphrase_var.get().strip()
        sandbox = self.sandbox_var.get()
        
        if not api_key or not secret:
            messagebox.showerror("错误", "请填写API Key和Secret")
            return
        
        self.result = (exchange_name, api_key, secret, passphrase, sandbox)
        self.dialog.destroy()
    
    def cancel(self):
        """取消按钮回调"""
        self.dialog.destroy()


if __name__ == "__main__":
    app = TradingSystemGUI()
    app.run()
